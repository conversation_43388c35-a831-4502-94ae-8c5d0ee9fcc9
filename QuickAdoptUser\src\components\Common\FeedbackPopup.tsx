import React from 'react';
import { thumpsUp, thumpsDown } from '../../assets/icons/icons';
import HtmlUtils from './HtmlUtils';
import { TextField, Typography } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';

interface FeedbackPopupProps {
    open: boolean;
    onClose: () => void;
    onFeedback: (helpful: boolean, message?: string) => void;
}

const FeedbackPopup: React.FC<FeedbackPopupProps> = ({ open, onClose, onFeedback }) => {
  const [message, setMessage] = React.useState("");
  const [showTextBox, setShowTextBox] = React.useState(false);
  const [isClosing, setIsClosing] = React.useState(false);

  const handleClose = () => {
    setIsClosing(true);
    setTimeout(() => {
      onClose();
      setIsClosing(false);
    }, 300);
  };

  const handleFeedback = (helpful: boolean, message?: string) => {
    onFeedback(helpful, message);
    setTimeout(() => {
      handleClose();
    }, 2000);
  };



  if (!open && !isClosing) return null;

  return (
    <div
      style={{
        position: 'fixed',
        bottom: '20px',
        right: '20px',
        background: '#fff',
        borderRadius: '20px',
        boxShadow: '0 4px 16px rgba(0,0,0,0.15)',
        padding: '16px',
        width: 'calc(50% - 310px)',
        zIndex: 999999,
        textAlign: 'center',
        transition: 'transform 0.3s ease-out, opacity 0.3s ease-out',
        transform: isClosing ? 'translateY(100%)' : 'translateY(0)',
        opacity: isClosing ? 0 : 1,
      }}
    >
      <div style={{ gap: "16px", display: "flex", flexDirection: "column" }}>
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            position: 'relative',
          }}
        >
          <Typography
            variant="subtitle1"
            style={{
              flex: 1,
              textAlign: 'center',
              fontFamily: 'Poppins',
              fontWeight: 500,
              fontSize: '18px',
              color: '#5f9ea0'
            }}
          >
            Give a Feedback
          </Typography>
          <button
            onClick={handleClose}
            aria-label="Close"
            style={{
              position: 'absolute',
              right: 8,
              top: '50%',
              transform: 'translateY(-50%)',
              background: 'none',
              border: 'none',
              padding: 0,
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              height: 24,
              width: 24,
            }}
          >
            <CloseIcon fontSize="small" />
          </button>
        </div>


        <div
          style={{
            fontFamily: 'Poppins',
            fontWeight: 500,
            fontSize: '16px',
          }}
        >
          Did you find this AI-generated guide helpful?
        </div>

        <div
          style={{
            display: 'flex',
            gap: '12px',
            justifyContent: 'center',
          }}
        >
          <button
            onClick={() => handleFeedback(true)}
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              background: '#5F9EA01F',
              border: 'none',
              borderRadius: '12px',
              padding: '12px',
              width: 'calc(100% - 190px)',
              height: 'fit-content',
              cursor: 'pointer',
              transition: 'background 0.2s, box-shadow 0.2s',
            }}
          >
            <span
              role="img"
              aria-label="thumbs up"
              style={{ marginBottom: '5px' }}
            >
              <HtmlUtils html={thumpsUp} />
            </span>
            <span
              style={{
                color: '#5d9ea0',
                fontWeight: 500,
                fontSize: '14px',
              }}
            >
              Helpful
            </span>
          </button>

          <button
            onClick={() => setShowTextBox(true)}
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              border: 'none',
              borderRadius: '12px',
              padding: '12px',
              width: 'calc(100% - 190px)',
              height: 'fit-content',
              cursor: 'pointer',
              transition: 'background 0.2s, box-shadow 0.2s',
              background: '#5F9EA01F',

            }}
          >
            <span
              role="img"
              aria-label="thumbs down"
              style={{ marginBottom: '5px' }}

            >
              <HtmlUtils html={thumpsDown} />
            </span>
            <span
              style={{
                color: '#5d9ea0',
                fontWeight: 500,
                fontSize: '14px',
              }}
            >
              Not Really
            </span>
          </button>
        </div>

        {showTextBox && (
          <div >
            <Typography
              variant="body2"
              style={{
                marginBottom: '8px',
                textAlign: 'left',
                fontFamily: 'Poppins',
                fontWeight: 400,
                fontSize: '16px',
              }}
            >
              Your Message{' '}
              <span style={{ color: '#888' }}>(Optional)</span>
            </Typography>

            <TextField
              placeholder="Write your feedback here"
              value={message}
              onChange={(e) => {
                const input = e.target.value;
                setMessage(input.slice(0, 1000)); // ✂️ Always keep only first 1000 characters
              }}
              multiline
              rows={3}
              fullWidth
              variant="outlined"
              helperText={`${message.length}/1000 characters`}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: '12px',
                  padding: '12px 8px !important',
                },
                '& .MuiInputBase-input::placeholder': {
                fontFamily: 'Poppins',
                  fontWeight: 400,
                  fontSize: '16px',
                },
              }}
            />




            <div
              style={{
                display: 'flex',
                marginTop: '16px',
                justifyContent: 'flex-end'
              }}
            >
              <button
                style={{
                  color: '#5f9ea0',
                  fontWeight: 500,
                  cursor: 'pointer',
                  background: '#fff',
                  border: 'none',
                  padding: '12px',
                  height: 'fit-content',
                  borderRadius: '8px'
                }}
                onClick={() => handleFeedback(false, '')}
              >
                Skip
              </button>
              <button
                style={{
                  borderRadius: '8px',
                  background: '#5f9ea0',
                  color: 'white',
                  padding: '12px',
                  fontWeight: 500,
                  cursor: 'pointer',
                  border: 'none',
                  height: 'fit-content',
                }}
                onClick={() => handleFeedback(false, message)}
              >
                Submit
              </button>
            </div>
          </div>
        )}
      </div>

    </div>
  );

};

export default FeedbackPopup;
