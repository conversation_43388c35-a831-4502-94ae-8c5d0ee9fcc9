
// axiosInterceptor.ts
import axios, { AxiosRequestConfig, AxiosResponse } from "axios";

export const userUrl = process.env.REACT_APP_USER_API;

export const adminUrl = process.env.React_APP_ADMIN_API;

export const apiTime=process.env.REACT_APP_USER_TRACKING_TIME;

export const speakModal=process.env.REACT_APP_SpeakModal || "ElevenLabs";

export const donaEnabled=process.env.REACT_APP_DONA_ENABLED;

export type JToken = any;

////console.log({ userUrl });

const userApiService = axios.create({
	baseURL: userUrl,
	headers: {
		"Content-Type": "application/json",
		"Accept":"application/json",
		"Access-Control-Allow-Origin": "*",
	},
});

const adminApiService = axios.create({
	baseURL: adminUrl,
	headers: {
		"Content-Type": "application/json",
		"Accept":"application/json",
		"Access-Control-Allow-Origin": "*",
	},
});

userApiService.interceptors.request.use(
	(config) => {
		return config;
	},
	(error) => {
		// Handle request errors here
		return Promise.reject(error);
	}
);

userApiService.interceptors.response.use(
	(response) => {
		return response;
	},
	(error) => {
		let errorMessage = "An unknown error occurred";

		if (error.response) {
			const { status, data } = error.response;
			switch (status) {
				case 400:
					errorMessage = data.message || "Bad Request";
					break;
				case 401:
					errorMessage = "Unauthorized - Please log in";
					break;
				case 403:
					errorMessage = "Forbidden - You do not have permission to access this resource";
					break;
				case 404:
					errorMessage = "Not Found - The requested resource does not exist";
					break;
				case 500:
					errorMessage = "Server Error - Something went wrong on the server";
					break;
				default:
					errorMessage = data.message || `Error: ${status}`;
					break;
			}
		} else if (error.request) {
			errorMessage = "No response received from the server";
		} else {
			errorMessage = error.message || "Request error";
		}

		return Promise.reject(new Error(errorMessage));
	}
);

export default userApiService;

interface ApiResponse<T> {
	data: T;
	status: string;
	message: string;
}

export const POST = async <T>(
	endpoint: string,
	data: Record<string, any>,
	config: AxiosRequestConfig = {}
): Promise<ApiResponse<T>> => {
	try {
		const response: AxiosResponse<ApiResponse<T>> = await userApiService.post(endpoint, data, config);
		return response.data;
	} catch (error) {
		throw error;
	}
};
