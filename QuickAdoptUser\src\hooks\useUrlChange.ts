import { useState, useEffect } from "react";

/**
 * Simple URL change detection hook that works in both development and production
 * This hook detects URL changes through multiple mechanisms to ensure reliability
 */
export const useUrlChange = () => {
  const [currentUrl, setCurrentUrl] = useState(window.location.href);

  useEffect(() => {
    const updateUrl = () => {
      const newUrl = window.location.href;
      if (newUrl !== currentUrl) {
        console.log('🔄 URL changed from', currentUrl, 'to', newUrl);
        setCurrentUrl(newUrl);
      }
    };

    // Listen for browser back/forward navigation
    const handlePopState = () => {
      console.log('🔄 PopState event detected');
      updateUrl();
    };

    // Listen for hash changes
    const handleHashChange = () => {
      console.log('🔄 HashChange event detected');
      updateUrl();
    };

    // Set up event listeners
    window.addEventListener('popstate', handlePopState);
    window.addEventListener('hashchange', handleHashChange);

    // Poll for URL changes every 500ms as a fallback
    // This will catch programmatic navigation that doesn't trigger events
    const pollInterval = setInterval(() => {
      updateUrl();
    }, 500);

    // Cleanup
    return () => {
      window.removeEventListener('popstate', handlePopState);
      window.removeEventListener('hashchange', handleHashChange);
      clearInterval(pollInterval);
    };
  }, [currentUrl]);

  return currentUrl;
};
