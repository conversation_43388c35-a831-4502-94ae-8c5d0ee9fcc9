/**
 * Text To Speech Service
 *
 * This service provides functionality for text-to-speech conversion using the Web Speech API.
 * It uses the SpeechSynthesis interface to convert text to speech.
 */

// Interface for speech synthesis options
export interface SpeechSynthesisOptions {
  voice?: string; // Voice name
  rate?: number; // 0.1 to 10, default is 1
  pitch?: number; // 0 to 2, default is 1
  volume?: number; // 0 to 1, default is 1
  onStart?: () => void;
  onEnd?: () => void;
  onError?: (error: any) => void;
}

// Current speaking state
let isSpeakingNow = false;
let currentMessageId: string | null = null;

// Check if speech synthesis is supported
export const isSpeechSynthesisSupported = (): boolean => {
  return 'speechSynthesis' in window && 'SpeechSynthesisUtterance' in window;
};

// Get available voices
export const getVoices = (): SpeechSynthesisVoice[] => {
  if (!isSpeechSynthesisSupported()) {
    return [];
  }
  return window.speechSynthesis.getVoices();
};

const selectVoice = (voices: SpeechSynthesisVoice[]): SpeechSynthesisVoice => {
  // First try to find <PERSON> voice specifically
  const samanthaVoice = voices.find(v => v.name === "Samantha" || v.name.includes("Samantha"));
  if (samanthaVoice) return samanthaVoice;
  
  // If Samantha not found, try Indian English voices
  const indianEnglishVoice = voices.find(v => 
    (v.name.includes("Indian") && v.name.includes("English")) || 
    v.lang === "en-IN" || 
    v.name.includes("Raveena")
  );
  if (indianEnglishVoice) return indianEnglishVoice;
  
  // If Indian English not found, try other sweet-sounding female voices
  const preferredVoiceNames = [
    "Google UK English Female",
    "Google US English",
    "Microsoft Zira Desktop - English (United States)",
    "Karen"
  ];
 
  for (const name of preferredVoiceNames) {
    const voice = voices.find(v => v.name.includes(name));
    if (voice) return voice;
  }
 
  // Fallback: pick first female voice
  const femaleVoice = voices.find(v => v.name.toLowerCase().includes('female'));
  return femaleVoice || voices[0]; // fallback to any available voice
};
const getSweetFemaleVoice = (): Promise<SpeechSynthesisVoice> => {
  return new Promise((resolve) => {
    let voices = window.speechSynthesis.getVoices();
 
    if (voices.length) {
      resolve(selectVoice(voices));
    } else {
      window.speechSynthesis.onvoiceschanged = () => {
        voices = window.speechSynthesis.getVoices();
        resolve(selectVoice(voices));
      };
    }
  });
};
const waitForSpeechEnd = () => new Promise<void>((resolve) => {
  const check = () => {
    if (!window.speechSynthesis.speaking) {
      resolve();
    } else {
      setTimeout(check, 50);
    }
  };
  check();
});

const splitText = (text: string): string[] => {
  return text.match(/[^\.!\?]+[\.!\?]+/g) || [text]; // sentence-level split
};

export const speak = async (text: string, options: SpeechSynthesisOptions = {}): Promise<void> => {
  if (!isSpeechSynthesisSupported()) {
    console.error('TTS Service: Speech synthesis not supported in this browser');
    options.onError?.('Speech synthesis not supported');
    return;
  }

  stop();
  await waitForSpeechEnd();
  await new Promise((resolve) => setTimeout(resolve, 100)); // Chrome fix

  try {
    const voice = await getSweetFemaleVoice();
    const chunks = splitText(text);

    let started = false;

    chunks.forEach((chunk, index) => {
      const utterance = new SpeechSynthesisUtterance(chunk);
      utterance.voice = voice;
      utterance.pitch = options.pitch ?? 1.2;
      utterance.rate = options.rate ?? 1.0;
      utterance.volume = options.volume ?? 1.0;

      // Call onStart once, at the start of first chunk
      utterance.onstart = () => {
        if (!started) {
          started = true;
          isSpeakingNow = true;
          options.onStart?.();
        }
      };

      // Only trigger onEnd at the end of last chunk
      if (index === chunks.length - 1) {
        utterance.onend = () => {
          isSpeakingNow = false;
          currentMessageId = null;
          options.onEnd?.();
        };
      }

      utterance.onerror = (event) => {
        console.error('TTS Service: Speech error:', event);
        isSpeakingNow = false;
        currentMessageId = null;
        options.onError?.(event);
      };

      // Speak without awaiting, queue all chunks
      window.speechSynthesis.speak(utterance);
    });

  } catch (error) {
    console.error('TTS Service: Error during speech synthesis:', error);
    isSpeakingNow = false;
    currentMessageId = null;
    options.onError?.(error);
  }
};



// Stop speaking
export const stop = (): void => {
  if (isSpeechSynthesisSupported()) {
    window.speechSynthesis.cancel();
    isSpeakingNow = false;
    currentMessageId = null;
  }
};

// Check if currently speaking
export const isSpeaking = (): boolean => {
  if (isSpeechSynthesisSupported()) {
    return window.speechSynthesis.speaking;
  }
  return isSpeakingNow;
};

// Set current message ID
export const setCurrentMessageId = (id: string | null): void => {
  currentMessageId = id;
};

// Get current message ID
export const getCurrentMessageId = (): string | null => {
  return currentMessageId;
};



// Preload the TTS engine
export const preloadVoices = (): Promise<SpeechSynthesisVoice[]> => {
  return new Promise((resolve) => {
    if (!isSpeechSynthesisSupported()) {
      console.warn('TTS Service: Speech synthesis not supported');
      resolve([]);
      return;
    }

    // Get voices
    let voices = getVoices();

    // If voices are already available, resolve immediately
    if (voices.length > 0) {
      //console.log('TTS Service: Voices already loaded:', voices.length);
      resolve(voices);
      return;
    }

    // Otherwise wait for voices to be loaded
    //console.log('TTS Service: Waiting for voices to load...');

    // Set up event listener for when voices are loaded
    window.speechSynthesis.onvoiceschanged = () => {
      voices = getVoices();
      //console.log('TTS Service: Voices loaded:', voices.length);
      resolve(voices);
    };

    // Set a timeout in case voices never load
    setTimeout(() => {
      voices = getVoices();
      //console.log('TTS Service: Voices after timeout:', voices.length);
      resolve(voices);
    }, 1000);
  });
};
