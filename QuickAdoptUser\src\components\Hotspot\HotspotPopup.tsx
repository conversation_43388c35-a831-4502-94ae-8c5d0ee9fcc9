import React, { useState, useEffect, useRef } from "react";
import { Popover, Button, Typography, Box, LinearProgress, DialogActions, MobileStepper } from "@mui/material";
import { CustomIconButton } from "../../components/Button";
import CloseIcon from "@mui/icons-material/Close";
import { PopoverOrigin } from "@mui/material";
import { browser,  trackUserEngagement, userData, version } from "../UserEngagement/userEngagementTracking";
import PerfectScrollbar from 'react-perfect-scrollbar';
import 'react-perfect-scrollbar/dist/css/styles.css';

interface PopupProps {
    anchorEl: null | HTMLElement;
    onClose: () => void;
    onPrevious: () => void;
    onContinue: () => void;
    onRestart?: () => void; // Added onRestart prop
    title: string;
    text: string;
    imageUrl?: string;
    videoUrl?: string;
    previousButtonLabel: string;
    continueButtonLabel: string;
    previousButtonStyles?: {
        backgroundColor?: string;
        textColor?: string;
        borderColor?: string;
    };
    continueButtonStyles?: {
        backgroundColor?: string;
        textColor?: string;
        borderColor?: string;
    };
    data: any;
    currentStep: number;
    totalSteps: number;
    onDontShowAgain: () => void;
    progress: number;
    textFieldProperties?: any;
    imageProperties?: any;
    customButton?: any;
    modalProperties?: { InteractionWithPopup?: boolean; IncludeRequisiteButtons?: boolean; DismissOption?: boolean; ModalPlacedOn?: string };
    canvasProperties?: {
        Position?: string;
        Padding?: string;
        Radius?: string;
        BorderSize?: string;
        BorderColor?: string;
        BackgroundColor?: string;
        Width?: string;
    };
    htmlSnippet: string;
    OverlayValue: boolean;
    hotspotProperties: any;
    rectData: any;
    rectDataLeft: any;
    selectedOption: any;

}

const HotspotPopup: React.FC<PopupProps> = ({
    rectDataLeft,
    rectData,
    anchorEl,
    onClose,
    onPrevious,
    onContinue,
    onRestart,
    title,
    text,
    imageUrl,
    videoUrl,
    previousButtonLabel,
    continueButtonLabel,
    currentStep,
    totalSteps,
    onDontShowAgain,
    progress,
    textFieldProperties,
    imageProperties,
    customButton,
    modalProperties,
    canvasProperties,
    htmlSnippet,
    previousButtonStyles,
    continueButtonStyles,
    OverlayValue,
    hotspotProperties,
    data,
    selectedOption
}) => {
    let initialTime : any = Date.now();

    const [Overlayvalue, setOverlayValue] = useState(false);
    const [currentStepNew, setCurrentStep] = useState(currentStep);
    useEffect(() => {
        if (OverlayValue && anchorEl !== null) {
            setOverlayValue(true)
        } else {
            setOverlayValue(false);
        }
    }, [OverlayValue]);
    const imageFit =  imageProperties &&  imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.Fit || 'contain';

    // const imageFill = imageProperties?.UploadedImages?.Fill || 'contain';

    const getAnchorAndTransformOrigins = (position: string): { anchorOrigin: PopoverOrigin; transformOrigin: PopoverOrigin } => {
        switch (position) {
            case "top-left":
                return { anchorOrigin: { vertical: "top", horizontal: "left" }, transformOrigin: { vertical: "bottom", horizontal: "right" } };
            case "top-right":
                return { anchorOrigin: { vertical: "top", horizontal: "right" }, transformOrigin: { vertical: "bottom", horizontal: "left" } };
            case "bottom-left":
                return { anchorOrigin: { vertical: "bottom", horizontal: "left" }, transformOrigin: { vertical: "top", horizontal: "right" } };
            case "bottom-right":
                return { anchorOrigin: { vertical: "bottom", horizontal: "right" }, transformOrigin: { vertical: "center", horizontal: "left" } };
            case "center-center":
                return { anchorOrigin: { vertical: "center", horizontal: "center" }, transformOrigin: { vertical: "center", horizontal: "center" } };
            case "top-center":
                return { anchorOrigin: { vertical: "top", horizontal: "center" }, transformOrigin: { vertical: "bottom", horizontal: "center" } };
            case "left-center":
                return { anchorOrigin: { vertical: "center", horizontal: "left" }, transformOrigin: { vertical: "center", horizontal: "right" } };
            case "bottom-center":
                return { anchorOrigin: { vertical: "bottom", horizontal: "center" }, transformOrigin: { vertical: "center", horizontal: "center" } };
            case "right-center":
                return { anchorOrigin: { vertical: "center", horizontal: "right" }, transformOrigin: { vertical: "center", horizontal: "left" } };
            default:
                return { anchorOrigin: { vertical: "center", horizontal: "center" }, transformOrigin: { vertical: "center", horizontal: "center" } };
        }
    };


    const { anchorOrigin, transformOrigin } = getAnchorAndTransformOrigins(canvasProperties?.Position || "center-center");
    const stepData = data?.GuideStep?.[0]?.StepTitle;
    const renderTextStyle = (textProperties: any, alignment: string) => {
        const styles = {
            fontWeight: textProperties?.Bold ? "bold" : "normal",
            fontStyle: textProperties?.Italic ? "italic" : "normal",
            color: textProperties?.TextColor || "#000000",
            textAlign: alignment || "left",
        };

        // Convert object to inline style string
        return Object.entries(styles)
            .map(([key, value]) => `${key.replace(/[A-Z]/g, (match) => `-${match.toLowerCase()}`)}: ${value};`)
            .join(" ");
    };
    const processTextWithLinks = (text: string) => {
        // Regex to find anchor tags and add target="_blank"
        return text.replace(
            /<a\s+([^>]*?)>/g,
            (match, attributes) => `<a ${attributes} target="_blank" rel="noopener noreferrer">`
        );
    };
    const renderHtmlSnippet = (snippet: string) => {
        return snippet.replace(/(<a\s+[^>]*href=")([^"]*)("[^>]*>)/g, (match, p1, p2, p3) => {
            return `${p1}${p2}" target="_blank"${p3}`;
        });
    };
  
    const imageStyle = {
        maxHeight: imageProperties?.MaxImageHeight || "500px",
        // padding: `${imageProperties?.Padding?.Top || 0}px ${imageProperties?.Padding?.Right || 0}px ${imageProperties?.Padding?.Bottom || 0}px ${imageProperties?.Padding?.Left || 0}px`,
        textAlign: imageProperties?.Alignment || "center",
        objectFit: imageFit || "contain",
        width: "100%",
        height: imageProperties?`${imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.SectionHeight || 250}px`:"250px",
        background: imageProperties ?imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.BackgroundColor || "#ffffff" :"#ffffff",
    };

    // const imageStyle = {
    //     backgroundImage: imageUrl ? `url(${imageUrl})` : undefined,
    //     backgroundSize: imageFit, // adjust based on `imageFit` (e.g., 'cover', 'contain')
    //     backgroundPosition: 'center',
    //     backgroundRepeat: 'no-repeat',
    //     maxHeight: imageProperties?.MaxImageHeight || "auto",
    //     textAlign: imageProperties?.Alignment || "center",
    //     width: "100%",
    //     height: "auto",
    // };

    // Utility function to check if HTML content contains meaningful text
    const hasHtmlMeaningfulContent = (htmlContent: string): boolean => {
        if (!htmlContent || htmlContent.trim() === '') {
            return false;
        }

        // Clean up common empty HTML patterns before checking
        let cleanedContent = htmlContent;

        // Remove empty paragraph tags
        cleanedContent = cleanedContent.replace(/<p>\s*(&nbsp;)*\s*<\/p>/gi, '');

        // Remove empty div tags
        cleanedContent = cleanedContent.replace(/<div>\s*(&nbsp;)*\s*<\/div>/gi, '');

        // Remove empty span tags
        cleanedContent = cleanedContent.replace(/<span>\s*(&nbsp;)*\s*<\/span>/gi, '');

        // Remove <br> tags
        cleanedContent = cleanedContent.replace(/<br\s*\/?>/gi, '');

        // Remove &nbsp; entities
        cleanedContent = cleanedContent.replace(/&nbsp;/gi, ' ');

        // If after cleaning there's no content left, return false
        if (cleanedContent.trim() === '') {
            return false;
        }

        // Create a temporary div to parse the cleaned HTML content
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = cleanedContent;

        // Get the text content (strips all HTML tags)
        const textContent = tempDiv.textContent || tempDiv.innerText;

        // Check if there's any non-whitespace text content
        if (textContent === null || textContent.trim() === '') {
            return false;
        }

        // Additional check for common empty HTML patterns
        // This handles cases like "<div><br></div>" or "<p>&nbsp;</p>" that might appear non-empty
        const lowerContent = cleanedContent.toLowerCase();
        const emptyPatterns = [
            '<div><br></div>',
            '<p><br></p>',
            '<div></div>',
            '<p></p>',
            '<span></span>',
            '<p>&nbsp;</p>',
            '<div>&nbsp;</div>',
            '<p> </p>',
            '<div> </div>'
        ];

        if (emptyPatterns.some(pattern => lowerContent.includes(pattern)) && textContent.trim().length <= 1) {
            return false;
        }

        return true;
    };

    // Check if there's a valid image
    const hasValidImage = imageProperties?.some((property: any) =>
        property?.CustomImage?.some((image: any) =>
            image?.Url && typeof image.Url === "string" && image.Url.trim() &&
            (image.Url.startsWith("data:image/") || image.Url.startsWith("http"))
        )
    );

    // Check if there's meaningful text content
    const hasValidTextContent = textFieldProperties?.some((field: any) =>
        field?.Text && typeof field.Text === "string" && hasHtmlMeaningfulContent(field.Text)
    );

    // Check if there are buttons
    const hasButtons = customButton?.length > 0;

    // Check if there's only text content (no images or buttons)
    const hasOnlyTextContent = hasValidTextContent && !hasValidImage && !hasButtons;

    // Check if there's only a button (no text or images)
    const hasOnlyButton = hasButtons && !hasValidTextContent && !hasValidImage && customButton?.length === 1;

    // Check if there's any meaningful content to display
    const hasValidContent = hasValidTextContent || hasValidImage;

    // Determine if we have a single button with no other content
    const hasSingleButton = customButton?.length === 1;
    const singleButtonWithNoContent = hasOnlyButton || (hasSingleButton && !hasValidContent);

    // Get button background color if there's a single button
    const buttonBackgroundColor = hasSingleButton
        ? customButton?.[0]?.ButtonProperties?.ButtonBackgroundColor
        : undefined;

    const canvasStyle = {
        position: canvasProperties?.Position || "center-center",
        borderRadius: canvasProperties?.Radius ? `${canvasProperties.Radius}` : "8px",
        borderColor: canvasProperties?.BorderColor || "transparent",
        borderStyle: "solid",
        borderWidth: canvasProperties?.BorderSize ? `${canvasProperties.BorderSize}` : "0px",
        backgroundColor: singleButtonWithNoContent && buttonBackgroundColor
            ? buttonBackgroundColor
            : canvasProperties?.BackgroundColor || "white",
        // Adjust width based on content type - same logic as tooltips
        maxWidth: `${canvasProperties?.Width} !important` || "300px",

        ...(singleButtonWithNoContent && {
            padding: "0 !important",
        }),
    };

    const handleContinue = () => {
        setCurrentStep(currentStep + 1);
        const hotspotElement = document.getElementById(`hotspotBlink${data?.GuideId}`);
        if (hotspotElement) {
        hotspotElement.remove();
        }

    };


    const handlePrevious = () => {
        if (currentStep > 1) {
            setCurrentStep(currentStep - 1);
            onPrevious();
        }
    };
    const dissmissIconColor = "red";
    const ActionButtonBackgroundcolor = "#f0f0f0";
    const overlay: boolean = Overlayvalue;
    const sectionHeight = imageProperties?.UploadedImages?.[currentStep - 1]?.SectionHeight || "auto";
    const openInNewTab = true;
    let stepStartTime: number | null = null;
    const handleButtonAction = (action: any, buttonName: string) => {
        let timeDiff = Date.now() - initialTime;
        timeDiff = timeDiff/1000;
        if (action.Action === "open-url" || action.Action === "open"|| action.Action === "openurl") {
            const targetUrl = action.TargetUrl;
            if (action.ActionValue === "same-tab") {

                // Open the URL in the same tab
                window.location.href = targetUrl;
            } else {
                // Open the URL in a new tab
                window.open(targetUrl, "_blank", "noopener noreferrer");
            }
            trackUserEngagement("button-click", userData, data, browser,version,buttonName,stepData,timeDiff,0);
        }
else if (action.Action === "close") {
            trackUserEngagement("button-click", userData, data, browser,version,buttonName,stepData,0,timeDiff);
            onClose();
            setOverlayValue(false);
        } else if (action === undefined || null) {
            onClose();
            setOverlayValue(false);
            trackUserEngagement("button-click", userData, data, browser,version,buttonName,stepData,timeDiff,0);
        }
        else if (action.Action.toLowerCase() === "restart") {
            trackUserEngagement("button-click", userData, data, browser,version,buttonName,stepData,timeDiff,0);
            // Navigate to the first step
            if (onRestart) {
                onRestart(); // Use the dedicated restart handler if available
            } else {
                setCurrentStep(1); // In HotspotPopup, steps are 1-indexed
            }
            setOverlayValue(false);
        }
        else if (action.Action.toLowerCase() === "previous") {
                handlePrevious();
setOverlayValue(false);
trackUserEngagement("button-click", userData, data, browser,version,buttonName,stepData,timeDiff,0);
            }

            else if (action.Action.toLowerCase() === "next") {
            handleContinue();
            if (data?.GuideType === "Tour") {
                onContinue();
            }
setOverlayValue(false);
            }

else {
            onClose();
            setOverlayValue(false);
        }


    };
    function getAlignment(alignment: string) {
        switch (alignment) {
          case "start":
            return "flex-start";
          case "end":
            return "flex-end";
          case "center":
          default:
            return "center";
        }
    }
      // State to track if scrolling is needed
	const [needsScrolling, setNeedsScrolling] = useState(false);
	const contentRef = useRef<HTMLDivElement>(null);
	const scrollbarRef = useRef<any>(null);
      // Check if content needs scrolling with improved detection
	useEffect(() => {
		const checkScrollNeeded = () => {
			if (contentRef.current) {
				// Force a reflow to get accurate measurements
				contentRef.current.style.height = 'auto';
				const contentHeight = contentRef.current.scrollHeight;
				const containerHeight = 320; // max-height value
				const shouldScroll = contentHeight > containerHeight;


				setNeedsScrolling(shouldScroll);

				// Force update scrollbar
				if (scrollbarRef.current) {
					// Try multiple methods to update the scrollbar
					if (scrollbarRef.current.updateScroll) {
						scrollbarRef.current.updateScroll();
					}
					// Force re-initialization if needed
					setTimeout(() => {
						if (scrollbarRef.current && scrollbarRef.current.updateScroll) {
							scrollbarRef.current.updateScroll();
						}
					}, 10);
				}
			}
		};


		checkScrollNeeded();


		const timeouts = [
			setTimeout(checkScrollNeeded, 50),
			setTimeout(checkScrollNeeded, 100),
			setTimeout(checkScrollNeeded, 200),
			setTimeout(checkScrollNeeded, 500)
		];


		let resizeObserver: ResizeObserver | null = null;
		let mutationObserver: MutationObserver | null = null;

		if (contentRef.current && window.ResizeObserver) {
			resizeObserver = new ResizeObserver(() => {
				setTimeout(checkScrollNeeded, 10);
			});
			resizeObserver.observe(contentRef.current);
		}


		if (contentRef.current && window.MutationObserver) {
			mutationObserver = new MutationObserver(() => {
				setTimeout(checkScrollNeeded, 10);
			});
			mutationObserver.observe(contentRef.current, {
				childList: true,
				subtree: true,
				attributes: true,
				attributeFilter: ['style', 'class']
			});
		}

		return () => {
			timeouts.forEach(clearTimeout);
			if (resizeObserver) {
				resizeObserver.disconnect();
			}
			if (mutationObserver) {
				mutationObserver.disconnect();
			}
		};
	}, [currentStep]);
	useEffect(() => {
		const rails = document.querySelectorAll('.ps__rail-x');
		rails.forEach(el => {
		  if (el instanceof HTMLElement) {
			el.style.display = 'none';
		  }
		});
	  }, [needsScrolling, currentStep]);
    const groupedButtons = customButton.reduce((acc: any, button: any) => {
        const containerId = button.ContainerId || "default"; // Use a ContainerId or fallback
        if (!acc[containerId]) {
          acc[containerId] = [];
        }
        acc[containerId].push(button);
        return acc;
    }, {});
    const getCanvasPosition = (position: string = "center-center") => {
        switch (position) {
            case "bottom-left":
                return { top: "auto !important" };
            case "bottom-right":
                return { top: "auto !important" };
            case "bottom-center":
                return { top: "51% !important" };
            case "center-center":
                return { top: "25% !important" };
            case "left-center":
                return { top: "28% !important"};
            case "right-center":
                return { top: "25% !important" };

        }
    };
    const steps = data?.GuideStep;
    const primaryColor = "#5F9EA0";
    const progressColor = data?.GuideStep?.[currentStep]?.Modal?.ProgressColor=== "var(--primarycolor)" ? primaryColor : data?.GuideStep?.[currentStep]?.Modal?.ProgressColor;

    const enableProgress = data?.GuideStep?.[0]?.Tooltip?.EnableProgress || false;
	function getProgressTemplate(selectedOption: any) {
		if (selectedOption === "1") {
			return "dots";
		} else if (selectedOption === "2") {
			return "linear";
		} else if (selectedOption === "3") {
			return "BreadCrumbs";
		}
        else if (selectedOption === "4") {
			return "breadcrumbs";
		}

		return data?.GuideStep?.[0]?.Tooltip?.ProgressTemplate || "dots";
	}
	const progressTemplate = getProgressTemplate(selectedOption);
	const renderProgress = () => {


		if (progressTemplate === "dots") {
			return (
				<MobileStepper
					variant="dots"
					steps={steps.length}
					position="static"
					activeStep={currentStep}
					sx={{ backgroundColor: "transparent",position:"inherit !important",  "& .MuiMobileStepper-dotActive": {
                        backgroundColor: progressColor, // Active dot
                      }, }}
					backButton={<Button style={{ visibility: "hidden" }} />}
					nextButton={<Button style={{ visibility: "hidden" }} />}
				/>
			);
		}
        if (progressTemplate === "BreadCrumbs") {
			return (
                <Box sx={{display: "flex",
                alignItems: "center",
                placeContent: "center",
                gap: "5px",padding:"8px"}}>                  {/* Custom Step Indicators */}

                    {Array.from({ length: steps.length }).map((_, index) => (
                      <div
                        key={index}
                        style={{
                          width: '14px',
                          height: '4px',
                          backgroundColor: index === currentStep  ? progressColor : '#e0e0e0', // Active color and inactive color
                          borderRadius: '100px',
                        }}
                      />
                    ))}

                </Box>
              );
		}
		if (progressTemplate === "breadcrumbs") {
			return (
				<Box sx={{padding:"8px"}}>
					<Typography variant="body2" sx={{color : progressColor, fontSize:"12px"}}>
						Step {currentStep+1} of {steps.length}
					</Typography>
				</Box>
			);
		}

		if (progressTemplate === "linear") {
			return (
				<Box>
					<Typography variant="body2">
						<LinearProgress
							variant="determinate"
							value={progress}
                            sx={{height: "6px",
                                borderRadius: "20px",
                                margin: "6px 10px",
                                '& .MuiLinearProgress-bar': {
                                backgroundColor: progressColor, // progress bar color
                              },}}
						/>
					</Typography>
				</Box>
			);
		}

		return null;
	};
    return (

        <div>
            {overlay && (
                <div style={{
                    position: 'fixed', top: 0, left: 0, right: 0, bottom: 0, backgroundColor: 'rgba(0, 0, 0, 0.5)', zIndex: 999,
                }} />
            )}
            {data && (
                <Popover
                className={
                    data?.GuideType === "Tour"
                        ? "qadpt-turendusr qadpt-htsenduser"
                        : "qadpt-htsenduser"
                    }
                    open={Boolean(anchorEl)}
                    anchorEl={anchorEl}
                    onClose={undefined}
                    anchorOrigin={anchorOrigin}
                    transformOrigin={transformOrigin}
                    disableScrollLock={true}
                    sx={{
                        position: "absolute !important",
                        zIndex: "auto !important",
                        pointerEvents: "auto",
                        '& .MuiPaper-root:not(.MuiMobileStepper-root)': {
                            zIndex: 1000,
                            pointerEvents: "auto",
                            ...canvasStyle,

                            ...getCanvasPosition(canvasProperties?.Position || "center-center"),
                            bottom: canvasProperties?.Position === "bottom-left" ? " 0 !important" : canvasProperties?.Position === "bottom-right" ? "0 !important" : "",
                            top: (data?.GuideType === "Hotspot" || data?.GuideType==="Tour") && rectData ? `${rectData}px !important` : "100px",
                            left: (data?.GuideType === "Hotspot" || data?.GuideType === "Tour") && rectDataLeft ? `${rectDataLeft}px !important` : "100px",
                            overflow:"hidden"


                        },
                    }}
                >
                                       <div style={{placeContent:"end",display:"flex"}}>
                    {modalProperties?.DismissOption && (
                            <CustomIconButton sx={{
                                position: "fixed",
                                boxShadow: "rgba(0, 0, 0, 0.06) 0px 4px 8px",
                                left: "auto",
                                right: "auto",
                                margin: "-15px",
                                background: "#fff !important",
                                border: "1px solid #ccc",
                                zIndex:"999999",
                                    borderRadius: "50px",
                                padding:"5px !important" }} onClick={() => {
                            onClose();
                            setOverlayValue(false);
                        }}>
                            <CloseIcon sx={{zoom:"1",color:"#000"}} />
                        </CustomIconButton>
                        )}
                    </div>
                    <PerfectScrollbar
					key={`scrollbar-${needsScrolling}`}
					ref={scrollbarRef}
					style={{ maxHeight: "400px" }}
					options={{
						suppressScrollY: !needsScrolling,
						suppressScrollX: true,
						wheelPropagation: false,
						swipeEasing: true,
						minScrollbarLength: 20,
						scrollingThreshold: 1000,
						scrollYMarginOffset: 0
					}}
				>
                    <Box style={{
                        padding: singleButtonWithNoContent
                            ? "0"
                            : hasOnlyTextContent
                                ? "8px 12px"
                                : canvasProperties?.Padding || "4px",
                        height: sectionHeight
                    }}>
                        {/* Only render the content Box if there's meaningful image or text content to display */}
                        {hasValidContent && (
                            <Box sx={{
                                width: hasOnlyTextContent ? "auto !important" : "100%"
                            }}>
                                <Box
                                    display="flex"
                                    flexDirection="column"
                                    alignItems={hasOnlyTextContent ? "flex-start" : "center"}
                                >
                                    {/* Image Section */}
                                    <Box display="flex" justifyContent="center" flexWrap="wrap" gap="16px">
                                        {imageProperties?.map((property: any, index: number) => (
                                            property?.CustomImage?.map((image: any, imgIndex: number) => (
                                                image?.Url && typeof image.Url === "string" && image.Url.trim() ? (
                                                    textFieldProperties?.Hyperlink ? (
                                                        <Box
                                                            key={`image-${index}-${imgIndex}`}
                                                            component="img"
                                                            src={image.Url}
                                                            alt={image.AltText || "Announcement"}
                                                            sx={imageStyle}
                                                            onClick={() => {
                                                                const targetUrl = textFieldProperties.Hyperlink;
                                                                if (openInNewTab) {
                                                                    window.open(targetUrl, "_blank", "noopener noreferrer");
                                                                } else {
                                                                    window.location.href = targetUrl;
                                                                }
                                                            }}
                                                        />
                                                    ) : (
                                                        <Box
                                                            key={`image-${index}-${imgIndex}`}
                                                            component="img"
                                                            src={image.Url}
                                                            alt={image.AltText || "Announcement"}
                                                            sx={imageStyle}
                                                        />
                                                    )
                                                ) : null
                                            ))
                                        ))}
                                    </Box>

                                    {textFieldProperties[0]?.Text && (
                                        <Box
                                            sx={{
                                                padding: "4px",
                                                whiteSpace: "pre-wrap",
                                                wordBreak:"break-word",
                                                "& p": {
                                                    margin: "0"
                                                },
                                                // Adjust styling for text-only content
                                                ...(hasOnlyTextContent && {
                                                    width: "auto !important",
                                                    maxWidth: "none !important"
                                                })
                                            }}
                                            dangerouslySetInnerHTML={{
                                                __html: processTextWithLinks(
                                                    textFieldProperties
                                                        ?.map((field: any) =>
                                                            `<span style="${renderTextStyle(
                                                                field.TextProperties,
                                                                field.Alignment
                                                            )}">${field.Text}</span>`
                                                        )
                                                        ?.join("<br>") // Combine text fields with line breaks
                                                ),
                                            }}
                                        />
                                    )}
                                </Box>
                            </Box>
                        )}

                        {/* Buttons Section */}
                        {Object.keys(groupedButtons).map((containerId) => (
                            <Box
                                key={containerId}
                                sx={{
                                    display: "flex",
                                    justifyContent: getAlignment(groupedButtons[containerId][0]?.Alignment),
                                    flexWrap: "wrap",
                                    backgroundColor: groupedButtons[containerId][0]?.BackgroundColor,
                                    // gap: "10px", // Space between buttons
                                    padding: "5px 0", // Optional container padding
                                }}
                            >
                                {groupedButtons[containerId].map((button: any, index: number) => (
                                    <Button
                                        key={index}
                                        onClick={() => handleButtonAction(button.ButtonAction,button.ButtonName)}
                                        variant="contained"
                                        sx={{
                                            backgroundColor: button.ButtonProperties?.ButtonBackgroundColor || "#5F9EA0 !important",
                                            color: button.ButtonProperties?.ButtonTextColor || "#fff",
                                            border: `${button.ButtonProperties?.ButtonBorderColor || "transparent"}`,
                                            fontSize: button.ButtonProperties?.FontSize || "14px",
                                            margin: "0 5px",
                                            width: button.ButtonProperties?.Width || "auto",
                                            padding: "4px 8px",
                                            textTransform: "none",
                                            borderRadius: "8px",
                                            lineHeight:"normal",
                                            '&:hover': {
                                                backgroundColor: button.ButtonProperties?.ButtonBackgroundColor || '#5F9EA0',
                                            }
                                        }}



                                    >
                                        {button.ButtonName}
                                    </Button>
                                ))}
                            </Box>
                        ))}
                    </Box>

                    {/* {(data?.GuideType === 'Announcement' || data?.GuideType === 'Tooltip') && totalSteps > 1 && (
                        <>
                            <Box>
                                <LinearProgress variant="determinate" value={progress} />
                            </Box>
                            <Box display="flex" justifyContent="initial" >
                                <Typography variant="body2">
                                    {currentStep}/{totalSteps} Steps
                                </Typography>
                            </Box>
                        </>
                    )} */}
                  
                        </PerfectScrollbar>
                    { data?.GuideType === "Tour" && (
                    <Box>
                {/* Render Step Progress */}
                                {totalSteps >= 1 && enableProgress ? (
                                    <>
                        {renderProgress()}
                    </>
                ) : (
                    null
                )}
                </Box>
                   )}


                </Popover>

            )}

        </div>

    );

};

export default HotspotPopup;