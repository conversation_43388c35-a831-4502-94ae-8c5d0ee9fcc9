import { apiTime } from "../../service/APIService";
let intervalId: NodeJS.Timeout | null = null; // Track interval
const userUrl = process.env.REACT_APP_USER_API;

const intervalTime = isNaN(Number(apiTime)) ? 50000 : Number(apiTime);
export const engagementList: any[] = []; // Ensure this is an array
export const trackUserEngagement = (
  eventType: "button-click" | "guide-view" | "hyperLink-click" | "hotspot-click" | "hotspot-hover" | "on-hover",
  UserDetails: any,
  GuideDetails: any,
  browser: string,
  browserVersion: string,
  eventData: string,
  StepDetails: string,
  timeSpentOnStep: number,
  timeSpentOnGuide: number,
) => {
  // Construct the engagement data object
  const userDetails = localStorage.getItem('userStats') || '{}';
  const engagementData = {
    eventType,
    UserDetails: userDetails,
    GuideDetails,
    browser,
    browserVersion,
    eventData,
    StepDetails,
    timeSpentOnStep,
    timeSpentOnGuide,
  };
  // Push engagement data to the list
  engagementList.push(engagementData);
 
  // Start the interval only if it's not running
  if (!intervalId) {
    intervalId = setInterval(async () => {
      if (engagementList.length > 0) {
        try {
          const response = await fetch(`${userUrl}/EndUserGuide/SaveActivity`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(engagementList),
          });
          if (response.ok) {
           
            engagementList.length = 0; // Clear list after success
          } else {
            
          }
        } catch (error) {
        }
      }
    }, intervalTime); // Runs every 10 seconds
  }
};
// Optional: Function to stop the interval when needed
export const stopTracking = () => {
  if (intervalId) {
    clearInterval(intervalId);
    intervalId = null;
  }
};
export   const userData = {
    id: "user123",
    name: "John Doe",
    email: "<EMAIL>",
};
const getBrowserType = () => {
    const userAgent = navigator.userAgent;
    if (userAgent.includes("Chrome") && !userAgent.includes("Edg/") && !userAgent.includes("OPR/")) {
        return "Chrome";
    }
    if (userAgent.includes("Firefox")) {
        return "Firefox";
    }
    if (userAgent.includes("Safari") && !userAgent.includes("Chrome")) {
        return "Safari";
    }
    if (userAgent.includes("Edg/")) {
        return "Edge";
    }
    if (userAgent.includes("OPR/") || userAgent.includes("Opera")) {
        return "Opera";
    }
    return "Unknown";
};
const getBrowserVersion = () => {
    const userAgent = navigator.userAgent;
    if (userAgent.includes("Chrome") && !userAgent.includes("Edg/") && !userAgent.includes("OPR/")) {
        return userAgent.match(/Chrome\/([\d.]+)/)?.[1] || "Unknown";
    }
    if (userAgent.includes("Firefox")) {
        return userAgent.match(/Firefox\/([\d.]+)/)?.[1] || "Unknown";
    }
    if (userAgent.includes("Safari") && !userAgent.includes("Chrome")) {
        return userAgent.match(/Version\/([\d.]+)/)?.[1] || "Unknown";
    }
    if (userAgent.includes("Edg/")) {
        return userAgent.match(/Edg\/([\d.]+)/)?.[1] || "Unknown";
    }
    if (userAgent.includes("OPR/") || userAgent.includes("Opera")) {
        return userAgent.match(/OPR\/([\d.]+)/)?.[1] || userAgent.match(/Opera\/([\d.]+)/)?.[1] || "Unknown";
    }
    return "Unknown";
};
const getIPAddress = async (): Promise<string> => {
    try {
        const response = await fetch("https://api64.ipify.org?format=json");
        const data = await response.json();
        return data.ip;
    } catch (error) {
        console.error("Error fetching IP address:", error);
        return "Unknown";
    }
};
export const browser = getBrowserType();
export const version = getBrowserVersion();
