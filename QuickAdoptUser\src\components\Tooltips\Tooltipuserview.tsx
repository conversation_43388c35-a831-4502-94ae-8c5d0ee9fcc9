import React, { useState, useMemo,useEffect } from "react";
import TooltipGuide from "../Tooltips/Tooltips";
import { IReponse,useFetch } from "../../hooks/useFetch";
import { Button } from "@mui/material";
import { browser,  trackUserEngagement, userData, version } from "../UserEngagement/userEngagementTracking";
import { useUrlChange } from "../../hooks/useUrlChange";
import { useGuideDetails } from "../../context/GuideDetailsContext";
 interface Guide {
		GuideId: string;
	 DontShowAgain: boolean;
	 GuideType: string;
}
let isGuideClosed: boolean = false;
 const TooltipUserview = () => {
	const [showTooltip, setShowTooltip] = useState(true);
	const [currentStepForUserIntraction,setCurrentStepForUserIntraction] = useState("step 0");
	let initialTime = Date.now();

	 // Use shared guideDetails from context
	 const { guideDetails } = useGuideDetails();

	 const [response, setResponse] = useState<IReponse>({
		 data: [],
		 loading: false,
		 error: {
			 message: "",
			 isError: false,
		 },
	 });

	 useEffect(() => {
		 if (guideDetails && guideDetails.data && guideDetails.data.length > 0) {
			 const tooltipData = guideDetails.data.filter((x: any) => x.GuideType === "Tooltip");
			 if (tooltipData.length > 0) {
				 const storedGuides: Guide[] = JSON.parse(localStorage.getItem('closedGuides_/') || '[]');
				isGuideClosed = storedGuides.some((guide) => guide.GuideId === tooltipData[0].GuideId && guide.DontShowAgain === true && tooltipData[0].GuideType.toLowerCase() === "tooltip");
				if (!isGuideClosed) {
					setResponse((prev) => ({
						...prev,
						data: tooltipData,
						loading: guideDetails.loading,
						error: guideDetails.error,
					}));
				 }
			 }
		 }
	 }, [guideDetails]);

		useEffect(() => {
			const handleStorageChange = () => {
				const storedGuides: Guide[] = JSON.parse(localStorage.getItem("closedGuides_/") || "[]");
				if (response?.data && response?.data?.length > 0) {
					isGuideClosed = storedGuides.some(
						(guide) => guide.GuideId === response?.data[0]?.GuideId && guide?.DontShowAgain === true && response?.data[0]?.GuideType.toLowerCase() === "tooltip"
					);
					if (response && response?.data[0]?.GuideStep?.length > 0 && isGuideClosed) {
						setShowTooltip(false);
					} else if (response?.data[0]?.GuideStep?.length > 0) {
						setShowTooltip(true);
					}
				}
			};

			handleStorageChange();
			window.addEventListener("storage", handleStorageChange);
			return () => {
				window.removeEventListener("storage", handleStorageChange);
			};
		}, [response, response?.data[0]?.GuideId, response?.data[0]?.GuideStep?.length]);



		const [hasViewed, setHasViewed] = useState(false);
		useEffect(() => {
			if (!hasViewed && data?.GuideStep &&data?.GuideType === "Tooltip") {
				let timeDiff = Date.now() - initialTime;
        		timeDiff = timeDiff/1000;
				let tourClosed = localStorage.getItem('closedGuides_/');
				let tooltipExists = false;
			if(tourClosed!=null){
				const guideArray: Guide[] = JSON.parse(tourClosed);
				 tooltipExists = guideArray.some((x) => x.GuideType === "Tooltip" && x.GuideId === response?.data[0]?.GuideId);
			}
			if(!tooltipExists)
			  trackUserEngagement("guide-view", userData, guideDetails.data[0], browser,version,"tooltipview",data?.GuideStep?.[0]?.StepTitle,timeDiff,0);
			  setHasViewed(true);
			}
		});
	 	const data = guideDetails.data[0];


		// Check if GuideStep exists before calling map
		const steps = (response?.data[0]?.GuideStep || []).map((step: any) => ({
			xpath: step.ElementPath,
			content: step.TextFieldProperties?.[0]?.Text || "",
			imageUrl: step.ImageProperties?.[0]?.CustomImage?.[0]?.Url || "",
			buttonData: step.ButtonSection?.[0]?.CustomButtons || [],
			targetUrl: step.StepTargetURL || response?.data[0]?.TargetUrl || "", // Use individual step URL if available, fallback to guide URL
			overlay: step.Overlay,
			positionXAxisOffset: step.Position.XAxisOffset,
			positionYAxisOffset: step.Position.YAxisOffset,
			canvas: step.Canvas,
			modal: step.Modal,
			imageproperties: step.ImageProperties?.[0]?.CustomImage?.[0] || "",
			autoposition: step.AutoPosition,
			elementclick: step.Design?.GotoNext,
			PossibleElementPath: step.PossibleElementPath,
			stepTitle: step?.StepTitle,
		}));

		const tooltipConfig = response?.data[0]?.GuideStep?.[0]?.Tooltip || {};

		const handleCloseTooltip = () => {

			let timeDiff = Date.now() - initialTime;

			timeDiff =timeDiff /1000;
			trackUserEngagement("button-click", userData, data, browser,version,"close",data?.GuideStep?.[0]?.StepTitle,0,timeDiff);

			setShowTooltip(false);
			const storedGuides: Guide[] = JSON.parse(localStorage.getItem("closedGuides_/") || "[]");
			var istooltipCloseSaved = storedGuides.some(
				(guide) => guide.GuideId === response?.data[0]?.GuideId && guide.DontShowAgain === true && response?.data[0]?.GuideType.toLowerCase() === 'tooltip'
			);
			if (!istooltipCloseSaved)
			{
				const updatedGuides = [
					...storedGuides,
					{
						GuideId: response?.data[0]?.GuideId,
						DontShowAgain: true,
						GuideType: response?.data[0]?.GuideType
					},
				];
				localStorage.setItem("closedGuides_/", JSON.stringify(updatedGuides));
			}
			isGuideClosed = true;

		};
		const getCurrentStep = (data:string) =>{
			setCurrentStepForUserIntraction(data);
		}
		// Handle restart action
		 const handleRestart = () => {
		 console.log('🔄 Tooltipuserview: handleRestart called');

		 // Clear multi-page tooltip session
		 sessionStorage.removeItem('activeTooltipSession');
		 sessionStorage.removeItem('currentTooltipStep');
		 console.log('🔄 Cleared tooltip session for restart');

		 // Get the first step's URL
		 const firstStepUrl = steps && steps.length > 0 ? steps[0]?.targetUrl : null;
		 const currentUrl = window.location.href;

		 console.log('🔄 First step URL:', firstStepUrl);
		 console.log('🔄 Current URL:', currentUrl);

		 // Check if we need to navigate to a different page
		 if (firstStepUrl && firstStepUrl.trim() !== currentUrl.trim()) {
			 // Multi-page: Navigate to the first step's URL
			 console.log('🔄 Restarting multi-page tooltip, navigating to:', firstStepUrl);
			 window.location.href = firstStepUrl;
		 } else {
			 // Single-page: Just reset to step 0
			 console.log('🔄 Restarting single-page tooltip, resetting to step 0');
			 setCurrentStepForUserIntraction("step 0");
		 }
	 }

		return (
			<div>
				{steps && steps.length > 0 && showTooltip && !isGuideClosed && (
					<TooltipGuide
						key={""}
						steps={steps}
						onClose={handleCloseTooltip}
						tooltipConfig={tooltipConfig}
						currentStep={currentStepForUserIntraction}
						setCurrentStep={setCurrentStepForUserIntraction}
						data={data}
						getCurrentStep={getCurrentStep}
						isFromAi={false}//Handled For Tour Tooltips, so setting default false here
            onRestart={handleRestart}
					/>
				)}
			</div>
		);
 };

 export default TooltipUserview;