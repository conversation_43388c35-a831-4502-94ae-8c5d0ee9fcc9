import React, { useState, useEffect, useRef } from "react";
import { Popover, Button, Typography, Box, LinearProgress, DialogActions, MobileStepper } from "@mui/material";
import { CustomIconButton } from "../../components/Button";
import CloseIcon from "@mui/icons-material/Close";
import { PopoverOrigin } from "@mui/material";
import { browser, trackUserEngagement, userData, version } from "../UserEngagement/userEngagementTracking";
import PerfectScrollbar from 'react-perfect-scrollbar';
import 'react-perfect-scrollbar/dist/css/styles.css';

// Helper function to convert hex color to rgba with opacity
const hexToRgba = (hex: string, opacity: number): string => {
	// Remove # if present
	hex = hex.replace('#', '');

	// Parse hex values
	const r = parseInt(hex.substring(0, 2), 16);
	const g = parseInt(hex.substring(2, 4), 16);
	const b = parseInt(hex.substring(4, 6), 16);

	return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};
interface PopupProps {
    anchorEl: null | HTMLElement;
    onClose: () => void;
    onPrevious: () => void;
    onContinue: () => void;
    onRestart?: () => void; // Added onRestart prop
    title: string;
    text: string;
    imageUrl?: string;
    videoUrl?: string;
    previousButtonLabel: string;
    continueButtonLabel: string;
    previousButtonStyles?: {
        backgroundColor?: string;
        textColor?: string;
        borderColor?: string;
    };
    continueButtonStyles?: {
        backgroundColor?: string;
        textColor?: string;
        borderColor?: string;
    };
    data: any;
    currentStep: number;
    totalSteps: number;
    onDontShowAgain: () => void;
    progress: number;
    textFieldProperties?: any;
    imageProperties?: any;
    customButton?: any;
    modalProperties?: { InteractionWithPopup?: boolean; IncludeRequisiteButtons?: boolean; DismissOption?: boolean; ModalPlacedOn?: string };
    canvasProperties?: {
        Position?: string;
        Padding?: string;
        Radius?: string;
        BorderSize?: string;
        BorderColor?: string;
        BackgroundColor?: string;
        Width?: string;
    };
    htmlSnippet: string;
    OverlayValue: boolean;
    hotspotProperties: any;
    rectData: any;
    rectDataLeft: any;
    selectedOption: any;
    enableProgress: any;
}

const AnnouncementPopup: React.FC<PopupProps> = ({
    rectDataLeft,
    rectData,
    anchorEl,
    onClose,
    onPrevious,
    onContinue,
    onRestart,
    title,
    text,
    imageUrl,
    videoUrl,
    previousButtonLabel,
    continueButtonLabel,
    currentStep,
    totalSteps,
    onDontShowAgain,
    progress,
    textFieldProperties,
    imageProperties,
    customButton,
    modalProperties,
    canvasProperties,
    htmlSnippet,
    previousButtonStyles,
    continueButtonStyles,
    OverlayValue,
    hotspotProperties,
    data,
    selectedOption,
    enableProgress
}) => {
    const stepData = data && data?.GuideStep?.[currentStep]?.StepTitle;
    const [Overlayvalue, setOverlayValue] = useState(false);
    const [currentStepNew, setCurrentStep] = useState(currentStep);
    const primaryColor = "#5F9EA0";
    const progressColor = data?.GuideStep?.[currentStep-1]?.Modal?.ProgressColor === "var(--primarycolor)" ? primaryColor : data?.GuideStep?.[currentStep-1]?.Modal?.ProgressColor;
    let initialTime = Date.now();
    useEffect(() => {
        if (OverlayValue && anchorEl !== null) {
            setOverlayValue(true)
        } else {
            setOverlayValue(false);
        }
    }, [OverlayValue,currentStep]);
    const imageFit =  imageProperties &&  imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.Fit || 'contain';

    // const imageFill = imageProperties?.UploadedImages?.Fill || 'contain';
    const steps : any = data?.GuideStep;
    const getAnchorAndTransformOrigins = (position: string): { anchorOrigin: PopoverOrigin; transformOrigin: PopoverOrigin } => {
        switch (position) {
            case "top-left":
                return { anchorOrigin: { vertical: "top", horizontal: "left" }, transformOrigin: { vertical: "bottom", horizontal: "right" } };
            case "top-right":
                return { anchorOrigin: { vertical: "top", horizontal: "right" }, transformOrigin: { vertical: "bottom", horizontal: "left" } };
            case "bottom-left":
                return { anchorOrigin: { vertical: "bottom", horizontal: "left" }, transformOrigin: { vertical: "top", horizontal: "right" } };
            case "bottom-right":
                return { anchorOrigin: { vertical: "bottom", horizontal: "right" }, transformOrigin: { vertical: "center", horizontal: "left" } };
            case "center-center":
                return { anchorOrigin: { vertical: "center", horizontal: "center" }, transformOrigin: { vertical: "center", horizontal: "center" } };
            case "top-center":
                return { anchorOrigin: { vertical: "top", horizontal: "center" }, transformOrigin: { vertical: "bottom", horizontal: "center" } };
            case "left-center":
                return { anchorOrigin: { vertical: "center", horizontal: "left" }, transformOrigin: { vertical: "center", horizontal: "right" } };
            case "bottom-center":
                return { anchorOrigin: { vertical: "bottom", horizontal: "center" }, transformOrigin: { vertical: "center", horizontal: "center" } };
            case "right-center":
                return { anchorOrigin: { vertical: "center", horizontal: "right" }, transformOrigin: { vertical: "center", horizontal: "left" } };
            default:
                return { anchorOrigin: { vertical: "center", horizontal: "center" }, transformOrigin: { vertical: "center", horizontal: "center" } };
        }
    };
    const getPopoverPositionStyle = (position: string = "center-center") => {
        // Constants
        const EDGE_PADDING = 16; // Padding from screen edges (in px)

        // Basic reset for all positioning properties
        const baseStyle = {
            position: 'fixed',
            top: 'auto !important',
            right: 'auto',
            bottom: 'auto',
            left: 'auto !important',
            transform: 'none'
        };

        // Apply specific positioning based on selected position
        switch (position) {
            case "top-left":
                return {
                    ...baseStyle,
                    top: `${EDGE_PADDING}px !important`,
                    left: `${EDGE_PADDING}px !important`
                };
            case "top-center":
                return {
                    ...baseStyle,
                    top: `${EDGE_PADDING}px !important`,
                    left: '50% !important',
                    transform: 'translateX(-50%)'
                };
            case "top-right":
                return {
                    ...baseStyle,
                    top: `${EDGE_PADDING}px !important`,
                    right: `${EDGE_PADDING}px !important`
                };
           // case "left-center":
            case "left-center":
                return {
                    ...baseStyle,
                    top: '50% !important',
                    left: `${EDGE_PADDING}px !important`,
                    transform: 'translateY(-50%)'
                };
            //case "center-center":
            case "center-center":
                return {
                    ...baseStyle,
                    top: '50% !important',
                    left: '50% !important',
                    transform: 'translate(-50%, -50%)'
                };
            //case "right-center":
            case "right-center":
                return {
                    ...baseStyle,
                    top: '50% !important',
                    right: `${EDGE_PADDING}px !important`,
                    transform: 'translateY(-50%)'
                };
            case "bottom-left":
                return {
                    ...baseStyle,
                    bottom: `${EDGE_PADDING}px !important`,
                    left: `${EDGE_PADDING}px !important`
                };
            case "bottom-center":
                return {
                    ...baseStyle,
                    bottom: `${EDGE_PADDING}px !important`,
                    left: '50% !important',
                    transform: 'translateX(-50%)'
                };
            case "bottom-right":
                return {
                    ...baseStyle,
                    bottom: `${EDGE_PADDING}px !important`,
                    right: `${EDGE_PADDING}px !important`
                };
            default:
                return {
                    ...baseStyle,
                    top: '50% !important',
                    left: '50% !important',
                    transform: 'translate(-50%, -50%)'
                };
        }
    };


    const { anchorOrigin, transformOrigin } = getAnchorAndTransformOrigins(canvasProperties?.Position || "center-center");

    const renderTextStyle = (textProperties: any, alignment: string) => {
          const isRTL = document.body.classList.contains("rtl");

    let textAlign = alignment || "left";
    if (isRTL) {
        if (textAlign === "left") textAlign = "right";
        else if (textAlign === "right") textAlign = "left";
    }

    const styles = {
        fontWeight: textProperties?.Bold ? "bold" : "normal",
        fontStyle: textProperties?.Italic ? "italic" : "normal",
        color: textProperties?.TextColor || "#000000",
        textAlign,
    };

        // Convert object to inline style string
        return Object.entries(styles)
            .map(([key, value]) => `${key.replace(/[A-Z]/g, (match) => `-${match.toLowerCase()}`)}: ${value};`)
            .join(" ");
    };
    const processTextWithLinks = (text: string) => {
        // Regex to find anchor tags and add target="_blank"
        return text.replace(
            /<a\s+([^>]*?)>/g,
            (match, attributes) => `<a ${attributes} target="_blank" rel="noopener noreferrer">`
        );
    };
    const renderHtmlSnippet = (snippet: string) => {
        return snippet.replace(/(<a\s+[^>]*href=")([^"]*)("[^>]*>)/g, (match, p1, p2, p3) => {
            return `${p1}${p2}" target="_blank"${p3}`;
        });
    };

    const imageStyle = {
        maxHeight: imageProperties?.MaxImageHeight || "500px",
        // padding: `${imageProperties?.Padding?.Top || 0}px ${imageProperties?.Padding?.Right || 0}px ${imageProperties?.Padding?.Bottom || 0}px ${imageProperties?.Padding?.Left || 0}px`,
        textAlign: imageProperties?.Alignment || "center",
        objectFit: imageFit || "contain",
        width: "100%",
        margin: 0,
		padding: 0,
		borderRadius: "0",
        height: imageProperties?`${imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.SectionHeight || 250}px`:"250px",
        background: imageProperties ?imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.BackgroundColor || "#ffffff" :"#ffffff",
    };

    // const imageStyle = {
    //     backgroundImage: imageUrl ? `url(${imageUrl})` : undefined,
    //     backgroundSize: imageFit, // adjust based on `imageFit` (e.g., 'cover', 'contain')
    //     backgroundPosition: 'center',
    //     backgroundRepeat: 'no-repeat',
    //     maxHeight: imageProperties?.MaxImageHeight || "auto",
    //     textAlign: imageProperties?.Alignment || "center",
    //     width: "100%",
    //     height: "auto",
    // };

    const canvasStyle = {
        position: canvasProperties?.Position || "center-center",
        borderRadius: canvasProperties?.Radius ? `${canvasProperties.Radius}px` : "8px",
        borderWidth: `${canvasProperties?.BorderSize}px`|| "0px",
        borderColor: canvasProperties?.BorderColor || "transparent",
        borderStyle: "solid",
        backgroundColor: canvasProperties?.BackgroundColor || "white",
        width: canvasProperties?.Width ? `${canvasProperties?.Width}px`: "500px"
    };
    function getProgressTemplate(selectedOption: any) {
		if (selectedOption === 1) {
			return "dots";
		} else if (selectedOption === 2) {
			return "linear";
		} else if (selectedOption === 3) {
			return "BreadCrumbs";
		}
        else if (selectedOption === 4) {
			return "breadcrumbs";
		}

		return data?.GuideStep?.[0]?.Tooltip?.ProgressTemplate || "dots";
	}
    const progressTemplate = getProgressTemplate(Number(selectedOption));
    const renderProgress = () => {
		if (!enableProgress) return null;

		if (progressTemplate === "dots") {
			return (
				<MobileStepper
					variant="dots"
					steps={steps?.length}
					position="static"
					activeStep={currentStep - 1}
					sx={{ backgroundColor: "transparent", padding:"8px 0 0 0 !important",  "& .MuiMobileStepper-dotActive": {
                        backgroundColor: progressColor, // Active dot
                      }, }}
					backButton={<Button style={{ visibility: "hidden" }} />}
					nextButton={<Button style={{ visibility: "hidden" }} />}
				/>
			);
		}
        if (progressTemplate === "BreadCrumbs") {
			return (
                <Box sx={{display: "flex",
					alignItems: "center",
					placeContent: "center",
					gap: "4px",padding:"8px"}}>
                  {/* Custom Step Indicators */}

                    {Array.from({ length: steps.length }).map((_, index) => (
                      <div
                        key={index}
                        style={{
                          width: '14px',
                          height: '4px',
                          backgroundColor: index === currentStep - 1 ? progressColor : hexToRgba(progressColor, 0.45), // Active color and inactive color
                          borderRadius: '100px',
                        }}
                      />
                    ))}

                </Box>
              );
		}

		if (progressTemplate === "breadcrumbs") {
			return (
				<Box sx={{display: "flex",
				alignItems: "center",
				placeContent: "flex-start",padding:"8px"
				}}>
					<Typography variant="body2"  sx={{color : progressColor, fontSize:"12px"}}>
                    Step {currentStep} of {steps?.length}
					</Typography>
				</Box>
			);
		}

		if (progressTemplate === "linear") {
			return (
				<Box>
					<Typography variant="body2">
						<LinearProgress
							variant="determinate"
							value={progress}
                            sx={{
                                height: "6px",
								borderRadius: "20px",
								margin: "6px 10px",
								backgroundColor: hexToRgba(progressColor, 0.45),
                                '& .MuiLinearProgress-bar': {
                                backgroundColor: progressColor, // progress bar color
                              },}}
						/>
					</Typography>
				</Box>
			);
		}

		return null;
    };

    const handleContinue = () => {
        if (data?.GuideType !== "Tour") {
            if (currentStep < totalSteps) {
                setCurrentStep(currentStep + 1);
                onContinue();
                renderNextPopup(currentStep < totalSteps)

            }
        }
        else {
            setCurrentStep(currentStep + 1);

        }
    };
    const renderNextPopup = (shouldRenderNextPopup: boolean) => {
        return shouldRenderNextPopup ? (
            <AnnouncementPopup
                rectData=""
                rectDataLeft=""
                data={data}
                anchorEl={anchorEl}
                onClose={onClose}
                onPrevious={onPrevious}
                onContinue={onContinue}
                onRestart={onRestart}
                title={title}
                text={text}
                imageUrl={imageUrl}
                previousButtonLabel={previousButtonLabel}
                continueButtonLabel={continueButtonLabel}
                currentStep={currentStep + 1}
                totalSteps={totalSteps}
                onDontShowAgain={onDontShowAgain}
                progress={progress}
                textFieldProperties={data?.GuideStep?.[currentStep]?.TextFieldProperties}
                imageProperties={data?.GuideStep?.[currentStep]?.ImageProperties}
                customButton={
                    data?.GuideStep?.[currentStep]?.ButtonSection
                        ?.map((section:any) =>
                            section.CustomButtons.map((button:any) => ({
                                ...button,
                                ContainerId: section.Id, // Attach the container ID for grouping
                            }))
                        )
                        ?.reduce((acc: string | any[], curr: any) => acc.concat(curr), []) || []
                }
                modalProperties={modalProperties}
                canvasProperties={canvasProperties}
                htmlSnippet={htmlSnippet}
                OverlayValue={OverlayValue}
                hotspotProperties=""
                selectedOption={selectedOption}
                enableProgress={enableProgress}
            />
        ) : null;
    };

    const handlePrevious = () => {
        if (data?.GuideType !== "Tour") {

            if (currentStep > 1) {
                setCurrentStep(currentStep - 1);
                onPrevious();
            }
        }
        else {
            setCurrentStep(currentStep - 1);

        }
    };
    const dissmissIconColor = "red";
    const ActionButtonBackgroundcolor = "#f0f0f0";
    const overlay: boolean = Overlayvalue;
    const sectionHeight = imageProperties?.UploadedImages?.[currentStep - 1]?.SectionHeight || "auto";
    const openInNewTab = true;
    const handleButtonAction = (action: any,buttonName:string) => {
        let timeDiff = Date.now() - initialTime;
        timeDiff = timeDiff/1000;
        if (action.Action === "open-url" || action.Action === "open"|| action.Action === "openurl") {
            trackUserEngagement("button-click", userData, data, browser,version, buttonName,title,timeDiff,0);
            const targetUrl = action.TargetUrl;
            if (action.ActionValue === "same-tab") {
                // Open the URL in the same tab
                window.location.href = targetUrl;
            } else {
                // Open the URL in a new tab
                window.open(targetUrl, "_blank", "noopener noreferrer");
            }

        }
else if (action.Action === "close") {
            onClose();
            setOverlayValue(false);
        } else if (action === undefined || null) {
            trackUserEngagement("button-click", userData, data, browser,version, buttonName,data?.GuideStep?.[currentStep-1]?.StepTitle,timeDiff,0);
            onClose();
            setOverlayValue(false);
        }
        else if (action.Action.toLowerCase() === "restart") {
            trackUserEngagement("button-click", userData, data, browser,version,buttonName,data?.GuideStep?.[currentStep-1]?.StepTitle,timeDiff,0);
            // Navigate to the first step
            if (onRestart) {
                onRestart(); // Use the dedicated restart handler if available
                // The parent component will handle the actual navigation to step 0
            } else {
                setCurrentStep(0); // Keep this as 1 for the local component state
            }
            setOverlayValue(false);
        }
        else if (action.Action.toLowerCase() === "previous") {
            trackUserEngagement("button-click", userData, data, browser,version,buttonName,data?.GuideStep?.[currentStep-1]?.StepTitle,timeDiff,0);
            onPrevious();
            setOverlayValue(false);
            }

            else if (action.Action.toLowerCase() === "next") {
                trackUserEngagement("button-click", userData, data, browser,version,buttonName,data?.GuideStep?.[currentStep-1]?.StepTitle,timeDiff,0);
                onContinue();
                setOverlayValue(false);
            }

else {
            trackUserEngagement("button-click", userData, data, browser,version, buttonName,data?.GuideStep?.[currentStep-1]?.StepTitle,timeDiff,0);
            onClose();
            setOverlayValue(false);
        }


    };
    function getAlignment(alignment: string) {
        switch (alignment) {
          case "start":
            return "flex-start";
          case "end":
            return "flex-end";
          case "center":
          default:
            return "center";
        }
      }

    const groupedButtons = customButton.reduce((acc: any, button: any) => {
        const containerId = button.ContainerId || "default"; // Use a ContainerId or fallback
        if (!acc[containerId]) {
          acc[containerId] = [];
        }
        acc[containerId].push(button);
        return acc;
    }, {});
    const getCanvasPosition = (position: string = "center-center") => {
        switch (position) {
            case "bottom-left":
                return { top: "auto !important" };
            case "bottom-right":
                return { top: "auto !important" };
            case "bottom-center":
                return { top: "51% !important" };
            case "center-center":
                return { top: "10% !important" };
            case "left-center":
                return { top: "28% !important"};
            case "right-center":
                return { top: "25% !important" };

        }
    };
    const interactWithPage = data?.GuideStep?.[currentStep - 1]?.Tooltip?.InteractWithPage;
    const position = canvasProperties?.Position || "center-center";
    const positionStyle = getPopoverPositionStyle(position);
    // State to track if scrolling is needed
    const [needsScrolling, setNeedsScrolling] = useState(false);
    const contentRef = useRef<HTMLDivElement>(null);
    const scrollbarRef = useRef<any>(null);
    // Check if content needs scrolling with improved detection
	useEffect(() => {
		const checkScrollNeeded = () => {
			if (contentRef.current) {
				// Force a reflow to get accurate measurements
				contentRef.current.style.height = 'auto';
				const contentHeight = contentRef.current.scrollHeight;
				const containerHeight = 320; // max-height value
				const shouldScroll = contentHeight > containerHeight;


				setNeedsScrolling(shouldScroll);

				// Force update scrollbar
				if (scrollbarRef.current) {
					// Try multiple methods to update the scrollbar
					if (scrollbarRef.current.updateScroll) {
						scrollbarRef.current.updateScroll();
					}
					// Force re-initialization if needed
					setTimeout(() => {
						if (scrollbarRef.current && scrollbarRef.current.updateScroll) {
							scrollbarRef.current.updateScroll();
						}
					}, 10);
				}
			}
		};

		
		checkScrollNeeded();

		
		const timeouts = [
			setTimeout(checkScrollNeeded, 50),
			setTimeout(checkScrollNeeded, 100),
			setTimeout(checkScrollNeeded, 200),
			setTimeout(checkScrollNeeded, 500)
		];

		
		let resizeObserver: ResizeObserver | null = null;
		let mutationObserver: MutationObserver | null = null;

		if (contentRef.current && window.ResizeObserver) {
			resizeObserver = new ResizeObserver(() => {
				setTimeout(checkScrollNeeded, 10);
			});
			resizeObserver.observe(contentRef.current);
		}

		
		if (contentRef.current && window.MutationObserver) {
			mutationObserver = new MutationObserver(() => {
				setTimeout(checkScrollNeeded, 10);
			});
			mutationObserver.observe(contentRef.current, {
				childList: true,
				subtree: true,
				attributes: true,
				attributeFilter: ['style', 'class']
			});
		}

		return () => {
			timeouts.forEach(clearTimeout);
			if (resizeObserver) {
				resizeObserver.disconnect();
			}
			if (mutationObserver) {
				mutationObserver.disconnect();
			}
		};
	}, [currentStep]);
	return (
		<div>
			{overlay && data?.GuideType!=="Tour"&&(
				<div
					style={{
						position: "fixed",
						top: 0,
						left: 0,
						right: 0,
						bottom: 0,
						backgroundColor: "rgba(0, 0, 0, 0.5)",
						zIndex: 999,
					}}
				/>
			)}
			{data && data.GuideStep && data.GuideStep[currentStep-1] && (
                <Popover
                className={
                    data?.GuideType === "Tour"
                        ? "qadpt-turendusr qadpt-annendusr"
                        : "qadpt-annendusr"
                    }
                    open={Boolean(anchorEl)}
					anchorEl={anchorEl}
					onClose={undefined}
					anchorOrigin={anchorOrigin}
					transformOrigin={transformOrigin}
					disableScrollLock={true}
					sx={{
						position:data?.GuideType==="Tour"?"absolute !important": interactWithPage ? "absolute !important" : "",
						zIndex:data?.GuideType==="Tour"?"auto !important": interactWithPage ? "auto !important" : "",
						// position: 'inherit !important',
						// "& .MuiBackdrop-root": {
						//     position: 'relative !important', // Ensures higher specificity
						// },
						// "& .MuiPopover-root": {
						//     position: 'inherit !important',
						// },
						pointerEvents: "auto",
						"& .MuiPaper-root:not(.MuiMobileStepper-root)": {
							zIndex: 1000,
							pointerEvents: "auto",
                            ...canvasStyle,
                            ...positionStyle,
                            transform: `${positionStyle.transform} !important`,
							//...getCanvasPosition(canvasProperties?.Position || "center-center"),
							// bottom:
							// 	canvasProperties?.Position === "bottom-left"
							// 		? " 10px !important"
							// 		: canvasProperties?.Position === "bottom-right"
							// 		? "10px !important"
                            //         : canvasProperties?.Position === "bottom-center"
                            //                 ? "10px !important" : "",
                            overflow:"visible"
							// top: data?.GuideType === "Hotspot" && rectData ? `${rectData}px !important` : "100px",
							// left: data?.GuideType === "Hotspot" && rectDataLeft ? `${rectDataLeft}px !important` : "100px",
						},
					}}
				>
					<div style={{ placeContent: "end", display: "flex" }}>
						{modalProperties?.DismissOption && (
							<CustomIconButton
								sx={{
									position: "fixed",
								boxShadow: "rgba(0, 0, 0, 0.06) 0px 4px 8px",
								left: "auto",
								right: "auto",
								margin: "-15px",
								background: "#fff !important",
								border: "1px solid #ccc",
								zIndex: "999999",
								borderRadius: "50px",
								padding: "5px !important",
								}}
								onClick={() => {
									onClose();
									setOverlayValue(false);
								}}
							>
								<CloseIcon sx={{ zoom: "1", color: "#000" }} />
							</CustomIconButton>
						)}
                    </div>
                    <PerfectScrollbar
				key={`scrollbar-${needsScrolling}`}
				ref={scrollbarRef}
				style={{
  maxHeight: "550px",
  ...(enableProgress
    ? {
        borderTopLeftRadius: canvasProperties?.Radius ? `${canvasProperties.Radius}px` : "8px",
        borderTopRightRadius: canvasProperties?.Radius ? `${canvasProperties.Radius}px` : "8px",
        borderBottomLeftRadius: "0px",
        borderBottomRightRadius: "0px",
      }
    : {
        borderRadius: canvasProperties?.Radius ? `${canvasProperties.Radius}px` : "8px",
      }),
}}
				options={{
					suppressScrollY: !needsScrolling,
					suppressScrollX: true,
					wheelPropagation: false,
					swipeEasing: true,
					minScrollbarLength: 20,
					scrollingThreshold: 1000,
					scrollYMarginOffset: 0
				}}
			>
   
                        <Box ref={contentRef}
                            style={{ padding: `${canvasProperties?.Padding}px` || "0px", height: sectionHeight }}>
                        {/* Image Section */}
                        <Box display="flex"
						flexDirection="column"
						flexWrap="wrap"
						justifyContent="center">
                            {imageProperties?.map((property: any, index: number) => (
                                property?.CustomImage?.map((image: any, imgIndex: number) => (
                                    image?.Url && typeof image.Url === "string" && image.Url.trim() ? (
                                        textFieldProperties?.Hyperlink ? (
                                            <Box
                                                key={`image-${index}-${imgIndex}`}
                                                component="img"
                                                src={image.Url}
                                                alt={image.AltText || "Announcement"}
                                                sx={{
                                                    maxHeight: property.MaxImageHeight || image.MaxImageHeight || "500px",
                                                textAlign: property.Alignment || "center",
                                                objectFit: image.Fit || "contain",
                                                background: image.BackgroundColor || "#ffffff",
                                                width: "100%",
                                                height: `${image.SectionHeight || 250}px`,
                                                //height: "100%",
                                                margin: 0,
                                                padding: 0,
                                                borderRadius: "0",
                                            }}
                                                onClick={() => {
                                                    const targetUrl = textFieldProperties.Hyperlink;
                                                    if (openInNewTab) {
                                                        window.open(targetUrl, "_blank", "noopener noreferrer");
                                                    } else {
                                                        window.location.href = targetUrl;
                                                    }
                                                }}
                                            />
                                        ) : (
                                            <Box
                                                key={`image-${index}-${imgIndex}`}
                                                component="img"
                                                src={image.Url}
                                                alt={image.AltText || "Announcement"}
                                                sx={{
                                                    maxHeight: property.MaxImageHeight || image.MaxImageHeight || "500px",
                                                textAlign: property.Alignment || "center",
                                                objectFit: image.Fit || "contain",
                                                background: image.BackgroundColor || "#ffffff",
                                                width: "100%",
                                                height: `${image.SectionHeight || 250}px`,
                                                //height: "100%",
                                                margin: 0,
                                                padding: 0,
                                                borderRadius: "0",
                                            }}
                                            />
                                        )
                                    ) : null
                                ))
                            ))}
                        </Box>



                        {/* Text Section */}
                        {textFieldProperties?.length ? (
                            <Box
                                mt={2}
                                sx={{ wordBreak:"break-word",whiteSpace: "pre-wrap",padding:"5px" }}
                                dangerouslySetInnerHTML={{
                                    __html: processTextWithLinks(
                                        textFieldProperties
                                            ?.map((field: any) =>
                                                `<span style="${renderTextStyle(
                                                    field.TextProperties,
                                                    field.Alignment
                                                )}">${field.Text}</span>`
                                            )
                                            ?.join("<br>") // Combine text fields with line breaks
                                    ),
                                }}
                                onClick={(e) => {

                                }}
                            />
                        ) : null}

                        {/* Buttons Section */}
                        {Object.keys(groupedButtons).map((containerId) => (
                            <Box
                                key={containerId}
                                sx={{
                                    display: "flex",
                                    justifyContent: getAlignment(groupedButtons[containerId][0]?.Alignment),
                                    flexWrap: "wrap",
                                    backgroundColor: groupedButtons[containerId][0]?.BackgroundColor,
                                    // gap: "10px", // Space between buttons
                                    padding: "10px", // Optional container padding
                                }}
                            >
                                {groupedButtons[containerId].map((button: any, index: number) => (
                                    <Button
                                        key={index}
                                        onClick={() => handleButtonAction(button.ButtonAction,button.ButtonName)}
                                        variant="contained"
                                        sx={{
                                            backgroundColor: button.ButtonProperties?.ButtonBackgroundColor || "#5F9EA0 !important",
                                            color: button.ButtonProperties?.ButtonTextColor || "#fff",
                                            border: `2px solid ${button.ButtonProperties?.ButtonBorderColor || "transparent"}`,
                                            fontSize: button.ButtonProperties?.FontSize || "14px",
                                            margin: "0 5px",
                                            width: button.ButtonProperties?.Width || "auto",
                                            padding: "4px 8px",
                                            lineHeight:"normal",
                                            textTransform: "none",
                                            borderRadius: "8px",
                                            boxShadow: "none !important",
                                            '&:hover': {
                                                backgroundColor: button.ButtonProperties?.ButtonBackgroundColor || '#5F9EA0',
                                            }
                                        }}



                                    >
                                        {button.ButtonName}
                                    </Button>
                                ))}
                            </Box>
                        ))}
                    </Box>

                  
                  </PerfectScrollbar>
                    {(data?.GuideType === 'Announcement'|| data?.GuideType === 'Tour'|| data?.GuideType==="Tooltip") && totalSteps >= 1 && (
                        <>
                            {renderProgress()}
                        </>
                    )}

                </Popover>
            )}
        </div>
    );
};

export default AnnouncementPopup;