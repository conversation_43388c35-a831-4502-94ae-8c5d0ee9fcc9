import React from "react";

const VideoPlayer = ({ videoFile ,isMaximized}: { videoFile: string,isMaximized :any }) => {
  if (!videoFile) return null;

  return (
    <div>
      <video
        width="100%"
        height="100%"
        controls
        style={{ borderRadius: "8px", objectFit: isMaximized?"contain":"cover" }}
      >
        <source src={videoFile} type="video/mp4" />
        Your browser does not support the video tag.
      </video>
    </div>
  );
};

export default VideoPlayer;
