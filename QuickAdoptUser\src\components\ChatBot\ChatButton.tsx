import React from 'react';
import { Box, Fab, Tooltip } from '@mui/material';
import ChatIcon from '@mui/icons-material/Chat';
import { ChatButtonStyle, ChatButtonWrapper } from './ChatBot.style';
import robot from "../../assets/icons/robot.png";

interface ChatButtonProps {
  onClick: () => void;
}

const ChatButton: React.FC<ChatButtonProps> = ({ onClick }) => {
  return (
    <Box sx={{ position: "fixed",
      right: "20px",
      bottom: "20px",
      zIndex: 1200,
      background: "rgb(54 89 165 / 60%)",
      borderRadius: "50%",
      padding: "10px",
      cursor: "pointer",
    }}>
      <Tooltip
        title="Dona"
        placement="left"
        componentsProps={{
          tooltip: {
            sx: {
              fontSize: "12px",
              fontFamily: "Gotham Pro",
            }
          }
        }}
      >
        {/* <Fab 
          //color="primary" 
          aria-label="chat"
         
          // sx={{ backgroundColor: "#5f9ea0", // Cadet blue color
          // color: "white",
          // "&:hover": {
          //   backgroundColor: "#4f8e90", // Slightly darker shade for hover
          // },
          // width: "56px",
          // height: "56px",
          // boxShadow: "0px 3px 5px -1px rgba(0,0,0,0.2), 0px 6px 10px 0px rgba(0,0,0,0.14), 0px 1px 18px 0px rgba(0,0,0,0.12)"}}
        > */}
          <img 
          src={robot}
           onClick={onClick}
            alt="Chat" 
            style={{
              width: "46px",
              height: "46px"
            }}
          />
        
        {/* </Fab> */}
      </Tooltip>
    </Box>
  );
};

export default ChatButton;
