import { adminApiService, userApiService } from "./APIservice";

export const GetGuideDetailsByGuideId = async (GuideId: any) => {
	try {
		const response = await userApiService.get(`/EndUserGuide/GetGuideDetails?guideId=${GuideId}`);
		if (response) {
			return response.data
		} else {
			console.error("Failed to update guide");
		}
	} catch (error) {
		console.error("Error update guide:", error);
	} finally {
	}
};
