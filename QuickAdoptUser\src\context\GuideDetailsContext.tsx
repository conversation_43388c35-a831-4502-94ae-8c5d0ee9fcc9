import React, { createContext, useContext, useMemo } from "react";
import { useFetch, IReponse } from "../hooks/useFetch";
import { useUrlChange } from "../hooks/useUrlChange";

interface GuideDetailsContextType {
    guideDetails: IReponse;
    refetch: () => void;
}

const GuideDetailsContext = createContext<GuideDetailsContextType | undefined>(undefined);

export const GuideDetailsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const currentUrl = useUrlChange();
    let accountId = localStorage.getItem('AccountId') ?? "";
    let domainAccountId = localStorage.getItem('DomainAccountId') ?? "";
    const [guideDetails, refetch] = useFetch({
        url: `/EndUserGuide/GetGuideListByTargetUrl?targetUrl=${encodeURIComponent(currentUrl)}&accountId=${accountId}&domainAccountId=${domainAccountId}`,
        dependencies: [currentUrl],
        //timeout: 30000,
        //hardTimeout: 120000
    });

    const value = useMemo(() => ({ guideDetails, refetch }), [guideDetails, refetch]);
    return (
        <GuideDetailsContext.Provider value={value}>
            {children}
        </GuideDetailsContext.Provider>
    );
};

export const useGuideDetails = () => {
    const context = useContext(GuideDetailsContext);
    if (!context) {
        throw new Error("useGuideDetails must be used within a GuideDetailsProvider");
    }
    return context;
};
