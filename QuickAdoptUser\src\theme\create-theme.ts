import { Theme, createTheme as createThemeMUI } from "@mui/material/styles";
import { customShadows, shadows, typography, colorSchemes } from "./core";
import { ThemeColorScheme } from "./types";
export function createTheme(themeMode: ThemeColorScheme): Theme {
	const initialTheme = {
		colorSchemes,
		shadows: shadows(themeMode),
		customShadows: customShadows(themeMode),
		shape: { borderRadius: 4 },
		typography,
	};

	const theme = createThemeMUI(initialTheme);

	return theme;
}
