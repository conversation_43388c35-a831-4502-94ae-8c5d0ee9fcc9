import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typo<PERSON>,
  Button,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  Chip,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  ExpandMore,
  PlayArrow,
  Refresh,
  Visibility,
  Code,
  BugReport,
  Science
} from '@mui/icons-material';
import workAgentSignalRService from '../../services/SignalRService';
import { runDomExtractionTest, logCurrentPageFormElements, addTestFormToPage, removeTestFormFromPage } from '../../utils/domExtractorTest';

interface ExtractedElement {
  key: string;
  value: string;
  labelName: string;
  xpath: string;
  type: string;
}

const DomDataExtractor: React.FC = () => {
  const [extractedData, setExtractedData] = useState<ExtractedElement[]>([]);
  const [isExtracting, setIsExtracting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastExtractionTime, setLastExtractionTime] = useState<Date | null>(null);
  const [testResults, setTestResults] = useState<string | null>(null);

  const handleExtractData = async () => {
    setIsExtracting(true);
    setError(null);
    
    try {
      console.log('🚀 Starting manual DOM data extraction...');
      
      // Access the private method through reflection (for testing purposes)
      const service = workAgentSignalRService as any;
      const extractedDataMap = await service.extractDomData();
      
      // Convert the extracted data map to array format for display
      const elementsArray: ExtractedElement[] = [];
      
      // Since we can't access private methods directly, we'll simulate the extraction
      // by calling the public method that would be triggered by SignalR
      console.log('📊 Extracted data from SignalR service:', extractedDataMap);
      
      // For demonstration, let's manually extract some data
      const formElements = document.querySelectorAll('input, textarea, select, [contenteditable="true"]');
      const extractedElements: ExtractedElement[] = [];
      
      formElements.forEach((element, index) => {
        const htmlElement = element as HTMLElement;
        
        // Check if element is visible
        const rect = htmlElement.getBoundingClientRect();
        const style = window.getComputedStyle(htmlElement);
        const isVisible = (
          rect.width > 0 &&
          rect.height > 0 &&
          style.display !== 'none' &&
          style.visibility !== 'hidden' &&
          style.opacity !== '0'
        );
        
        if (isVisible) {
          const elementData = extractElementInfo(htmlElement, index);
          if (elementData) {
            extractedElements.push(elementData);
          }
        }
      });
      
      setExtractedData(extractedElements);
      setLastExtractionTime(new Date());
      
      console.log('✅ DOM data extraction completed successfully');
      console.log('📋 Extracted elements:', extractedElements);
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('❌ DOM data extraction failed:', err);
    } finally {
      setIsExtracting(false);
    }
  };

  const handleRunTest = async () => {
    setError(null);
    setTestResults(null);

    try {
      console.log('🧪 Running DOM extraction test...');
      const result = await runDomExtractionTest();

      if (result.success) {
        setTestResults(`✅ Test passed! Extracted ${Object.keys(result.extractedData).length} fields.`);
      } else {
        setTestResults(`❌ Test failed: ${result.errors.join(', ')}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Test failed';
      setError(errorMessage);
      setTestResults(`❌ Test error: ${errorMessage}`);
    }
  };

  const handleLogElements = () => {
    logCurrentPageFormElements();
    setTestResults('📋 Form elements logged to console. Check browser console for details.');
  };

  const handleAddTestForm = () => {
    try {
      addTestFormToPage();
      setTestResults('✅ Test form added to page. Scroll down to see it.');
    } catch (error) {
      setError('Failed to add test form');
    }
  };

  const handleRemoveTestForm = () => {
    try {
      removeTestFormFromPage();
      setTestResults('🧹 Test form removed from page.');
    } catch (error) {
      setError('Failed to remove test form');
    }
  };

  const extractElementInfo = (element: HTMLElement, index: number): ExtractedElement | null => {
    try {
      const tagName = element.tagName.toLowerCase();
      const type = getElementType(element);
      const value = getElementValue(element);
      const labelName = findElementLabel(element);
      const xpath = generateSimpleXPath(element);
      const key = generateElementKey(element, labelName, index);
      
      // Include ALL elements, even those with empty values
      const finalValue = value ? value.trim() : '';

      return {
        key,
        value: finalValue,
        labelName,
        xpath,
        type
      };
    } catch (error) {
      console.warn('Error extracting element info:', error);
      return null;
    }
  };

  const getElementType = (element: HTMLElement): string => {
    const tagName = element.tagName.toLowerCase();
    
    if (tagName === 'input') {
      const inputElement = element as HTMLInputElement;
      return inputElement.type || 'text';
    }
    
    if (tagName === 'select') {
      const selectElement = element as HTMLSelectElement;
      return selectElement.multiple ? 'multiselect' : 'dropdown';
    }
    
    if (tagName === 'textarea') {
      return 'textarea';
    }
    
    if (element.contentEditable === 'true') {
      return 'contenteditable';
    }
    
    return tagName;
  };

  const getElementValue = (element: HTMLElement): string => {
    const tagName = element.tagName.toLowerCase();
    
    if (tagName === 'input') {
      const inputElement = element as HTMLInputElement;
      
      if (inputElement.type === 'checkbox' || inputElement.type === 'radio') {
        return inputElement.checked ? (inputElement.value || 'true') : 'false';
      }
      
      return inputElement.value || '';
    }
    
    if (tagName === 'select') {
      const selectElement = element as HTMLSelectElement;
      
      if (selectElement.multiple) {
        const selectedOptions = Array.from(selectElement.selectedOptions);
        return selectedOptions.map(option => option.text || option.value).join(', ');
      } else {
        const selectedOption = selectElement.selectedOptions[0];
        return selectedOption ? (selectedOption.text || selectedOption.value) : '';
      }
    }
    
    if (tagName === 'textarea') {
      const textareaElement = element as HTMLTextAreaElement;
      return textareaElement.value || '';
    }
    
    if (element.contentEditable === 'true') {
      return element.textContent || element.innerText || '';
    }
    
    return element.getAttribute('value') || element.textContent || '';
  };

  const findElementLabel = (element: HTMLElement): string => {
    // Try to find label
    if (element.id) {
      const label = document.querySelector(`label[for="${element.id}"]`);
      if (label && label.textContent) {
        return label.textContent.trim();
      }
    }
    
    // Check if inside label
    const parentLabel = element.closest('label');
    if (parentLabel && parentLabel.textContent) {
      return parentLabel.textContent.trim();
    }
    
    // Check aria-label
    if (element.getAttribute('aria-label')) {
      return element.getAttribute('aria-label')!.trim();
    }
    
    // Check placeholder
    if (element.getAttribute('placeholder')) {
      return element.getAttribute('placeholder')!.trim();
    }
    
    // Check name
    if (element.getAttribute('name')) {
      return element.getAttribute('name')!.trim();
    }
    
    // Use id or class as fallback
    return element.id || element.className || 'unknown';
  };

  const generateSimpleXPath = (element: HTMLElement): string => {
    if (element.id) {
      return `//*[@id="${element.id}"]`;
    }
    
    const tagName = element.tagName.toLowerCase();
    const className = element.className;
    
    if (className) {
      return `//${tagName}[@class="${className}"]`;
    }
    
    return `//${tagName}`;
  };

  const generateElementKey = (element: HTMLElement, labelName: string, index: number): string => {
    if (labelName && labelName !== 'unknown') {
      return labelName.toLowerCase().replace(/[^a-z0-9]/g, '_');
    }
    
    if (element.id) {
      return element.id;
    }
    
    const name = element.getAttribute('name');
    if (name) {
      return name;
    }
    
    return `element_${index + 1}`;
  };

  const highlightElement = (xpath: string) => {
    try {
      const element = document.evaluate(
        xpath,
        document,
        null,
        XPathResult.FIRST_ORDERED_NODE_TYPE,
        null
      ).singleNodeValue as HTMLElement;
      
      if (element) {
        const originalStyle = element.style.cssText;
        element.style.cssText += `
          outline: 3px solid #ff6b6b !important;
          outline-offset: 2px !important;
          background-color: rgba(255, 107, 107, 0.1) !important;
          transition: all 0.3s ease !important;
        `;
        
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        
        setTimeout(() => {
          element.style.cssText = originalStyle;
        }, 3000);
      }
    } catch (error) {
      console.warn('Could not highlight element:', error);
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'text':
      case 'email':
      case 'password':
        return 'primary';
      case 'textarea':
        return 'secondary';
      case 'dropdown':
      case 'select':
        return 'success';
      case 'multiselect':
        return 'info';
      case 'checkbox':
      case 'radio':
        return 'warning';
      case 'date':
      case 'daterange':
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <Paper
      sx={{
        position: 'fixed',
        top: 20,
        left: 20,
        width: 450,
        maxHeight: '80vh',
        overflow: 'auto',
        zIndex: 10000,
        p: 2
      }}
      elevation={8}
    >
      <Box sx={{ mb: 2 }}>
        <Typography variant="h6" component="h2" gutterBottom>
          DOM Data Extractor
        </Typography>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          Extract form field data from the current page
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      <Box sx={{ mb: 2 }}>
        <Button
          variant="contained"
          startIcon={<PlayArrow />}
          onClick={handleExtractData}
          disabled={isExtracting}
          sx={{ mr: 1 }}
        >
          {isExtracting ? 'Extracting...' : 'Extract Data'}
        </Button>
        <Button
          variant="outlined"
          startIcon={<Refresh />}
          onClick={() => {
            setExtractedData([]);
            setError(null);
            setLastExtractionTime(null);
            setTestResults(null);
          }}
        >
          Clear
        </Button>
      </Box>

      <Box sx={{ mb: 2 }}>
        <Typography variant="subtitle2" gutterBottom>
          Test Utilities:
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          <Button
            variant="outlined"
            size="small"
            startIcon={<Science />}
            onClick={handleRunTest}
          >
            Run Test
          </Button>
          <Button
            variant="outlined"
            size="small"
            startIcon={<BugReport />}
            onClick={handleLogElements}
          >
            Log Elements
          </Button>
          <Button
            variant="outlined"
            size="small"
            onClick={handleAddTestForm}
          >
            Add Test Form
          </Button>
          <Button
            variant="outlined"
            size="small"
            onClick={handleRemoveTestForm}
          >
            Remove Test Form
          </Button>
        </Box>
      </Box>

      {lastExtractionTime && (
        <Typography variant="caption" color="text.secondary" sx={{ mb: 2, display: 'block' }}>
          Last extraction: {lastExtractionTime.toLocaleTimeString()}
        </Typography>
      )}

      {testResults && (
        <Alert
          severity={testResults.includes('✅') ? 'success' : testResults.includes('❌') ? 'error' : 'info'}
          sx={{ mb: 2 }}
          onClose={() => setTestResults(null)}
        >
          {testResults}
        </Alert>
      )}

      {extractedData.length > 0 && (
        <Accordion defaultExpanded>
          <AccordionSummary expandIcon={<ExpandMore />}>
            <Typography variant="subtitle1">
              Extracted Data ({extractedData.length} elements)
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <List dense>
              {extractedData.map((item, index) => (
                <ListItem key={index} sx={{ px: 0 }}>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                        <Typography variant="subtitle2" component="span">
                          {item.labelName}
                        </Typography>
                        <Chip 
                          label={item.type} 
                          size="small" 
                          color={getTypeColor(item.type) as any}
                        />
                        <Tooltip title="Highlight element">
                          <IconButton 
                            size="small" 
                            onClick={() => highlightElement(item.xpath)}
                          >
                            <Visibility fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.primary">
                          <strong>Value:</strong> {item.value}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          <strong>XPath:</strong> {item.xpath}
                        </Typography>
                      </Box>
                    }
                  />
                </ListItem>
              ))}
            </List>
          </AccordionDetails>
        </Accordion>
      )}

      {extractedData.length === 0 && !isExtracting && (
        <Alert severity="info">
          No form data extracted yet. Click "Extract Data" to scan the current page.
        </Alert>
      )}
    </Paper>
  );
};

export default DomDataExtractor;
