import React, { useEffect, useMemo, useState } from 'react';
import { IReponse } from '../../hooks/useFetch';
import ChecklistPopup from "./ChecklistPopup";
import { chkcloseicon, chkicn1 } from '../../assets/icons/icons';
import { useUrlChange } from '../../hooks/useUrlChange';
import { useGuideDetails } from "../../context/GuideDetailsContext";

interface Guide {
  GuideId: string;
  DontShowAgain: boolean;
  GuideType: string;
  CompletedCheckPoint?: boolean; // Make it optional since not all guides will have it
}

interface ClosedGuides {
  checklist?: Guide[]; // A separate checklist array inside closedGuides
}
let base64Icon: any;
const ChecklistLauncher = () => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(document.body);

  // Use our custom URL change detection hook
  const currentUrl = useUrlChange();
    
    const [response, setResponse] = useState<IReponse>({
		data: [],
		loading: false,
		error: {
			message: "",
			isError: false,
		},
	});
    let accountId = localStorage.getItem('AccountId') ?? "";
  const { guideDetails } = useGuideDetails();

    // Reset checklist state when URL changes - clear old checklists immediately
    useEffect(() => {
        console.log('🔄 ChecklistLauncher: URL changed, clearing old checklists');
        setIsOpen(false);
        setIsGuideClosed(false);
        setCompletedStatus({});
        setRemainingCount(undefined);
        setStepCheckpoint("");
        setResponse({
            data: [],
            loading: true,
            error: {
                message: "",
                isError: false,
            },
        });
    }, [currentUrl]);
    useEffect(() => {
        if (guideDetails && guideDetails.data && guideDetails.data.length > 0) {
          setResponse(prev => ({
            ...prev,
            data: guideDetails.data,
            loading: guideDetails.loading,
            error: guideDetails.error,
          }));
        }
      }, [guideDetails]);
      
    
      const checklist = useMemo(() => {
        // Only return checklist if we have fresh data and not loading
        if (guideDetails?.loading || !guideDetails?.data) {
          return {};
        }

        return (
          (guideDetails?.data as any[]).find(
            (item) => (item.GuideType === "Checklist" && item.GuideStep?.length>0) // Removing Equals condition as we have page targets
          ) || {}
        );
      }, [currentUrl, guideDetails]);

  const data = useMemo(() => checklist ? checklist : [], [checklist]);
  const GuideStep = useMemo(() => checklist?.GuideStep?.[0], [checklist]);
  const Canvas = useMemo(() => GuideStep?.Canvas, [GuideStep]);
  const Launcher = useMemo(() => GuideStep?.Launcher, [GuideStep]);
  const Checkpoints = useMemo(() => GuideStep?.Checkpoint, [GuideStep]);
  const CheckpointsList = useMemo(() => GuideStep?.Checkpoint?.CheckpointItem, [GuideStep]);
  const TitleSubTitle = useMemo(() => GuideStep?.TitleSubTitle, [GuideStep]);

  let base64Icon: any;
  const [icons, setIcons] = useState<any[]>([
    { id: 1, component: <span dangerouslySetInnerHTML={{ __html: chkicn1 }} style={{ zoom: 1 ,display:"flex"}} />, selected: true },
   
  ]);

  const encodeToBase64 = (svgString: string) => {
		return `data:image/svg+xml;base64,${btoa(svgString)}`;
  };
  const initialSelectedIcon = icons.find(icon => icon.selected);
  if (initialSelectedIcon) {
    const svgElement = initialSelectedIcon.component.props.dangerouslySetInnerHTML?.__html;

    if (svgElement) {
      base64Icon = encodeToBase64(svgElement);

    }
}
  const iconColor = Launcher?.IconColor || '#fff';
  const base64IconFinal = Launcher?.Icon? Launcher?.Icon : base64Icon ;
  
  const [isOpen, setIsOpen] = useState(Canvas?.OpenByDefault ?? false);
  const [isGuideClosed, setIsGuideClosed] = useState(false);
  const [completedStatus, setCompletedStatus] = useState<{ [key: string]: boolean }>({});
  const [isRightPanelVisible, setIsRightPanelVisible] = useState(false);
 
  useEffect(() => {
    if (Canvas?.OpenByDefault === true) {
      setIsOpen(true);
    }
  }, [Canvas?.OpenByDefault]);
   

    const [remainingCount, setRemainingCount] = useState<number>(); // Initial value
  const [stepCheckpoint, setStepCheckpoint] = useState<string>("");
   
    const handleRemainingCountUpdate = (formattedCount: number) => {
      setRemainingCount(formattedCount);
  
  };
  const handleStepCheckpoint = (foramttedstepcheckpoint: string) =>
  {
    setStepCheckpoint(foramttedstepcheckpoint);
    }

    useEffect(() => {
      const handleStorageChange = () => {
        const storedGuides: Guide[] = JSON.parse(localStorage.getItem('closedGuides_/') || '[]');
        const isGuideClosed = storedGuides.some((guide) => guide.GuideId === stepCheckpoint && guide.DontShowAgain === true);
        if (isGuideClosed) {
          setCompletedStatus((prevStatus:any) => ({
            ...prevStatus,
            [stepCheckpoint]: true,
          }));
        } else  {
          setAnchorEl(document.body);
        }

      };

    
      handleStorageChange();
      window.addEventListener('storage', handleStorageChange);
      return () => {
        window.removeEventListener('storage', handleStorageChange);
      };
    }, [CheckpointsList]);

    useEffect(() => {
      const updatedStatus: { [key: string]: boolean } = {};
    
      const storedGuides: (Guide | ClosedGuides)[] = JSON.parse(localStorage.getItem("closedGuides_/") || "[]");
    
      const checklistObject = storedGuides.find((guide) => (guide as ClosedGuides).checklist !== undefined) as ClosedGuides | undefined;
      
      const checklistGuides: Guide[] = checklistObject?.checklist || [];

      // Initialize all checkpoints with false (not completed) by default
      if (CheckpointsList?.length > 0) {
        CheckpointsList.forEach((checkpoint: any) => {
          // Check if this checkpoint is marked as completed in localStorage
          const isGuideClosed = checklistGuides.some(
            (guide) => guide.GuideId === checkpoint.Id
          );

          // Set the status based on localStorage or default to false
          updatedStatus[checkpoint.Id] = isGuideClosed;
        });
      }

      setCompletedStatus(updatedStatus);

      localStorage.setItem("closedGuides_/", JSON.stringify(storedGuides));
    }, [data, CheckpointsList]);

    // Check if all checklist items are completed by comparing with the total number of checkpoints
    const allCompleted = completedStatus &&
    Object.keys(completedStatus).length > 0 &&
    CheckpointsList?.length > 0 &&
    Object.keys(completedStatus).length === CheckpointsList?.length &&
    Object.values(completedStatus).every((status: boolean) => status === true);
    
  

const modifySVGColor = (base64SVG: any, color: any) => {
		if (!base64SVG) {
			return "";
		}

		try {
			// Check if the string is a valid base64 SVG
			if (!base64SVG.includes("data:image/svg+xml;base64,")) {
				return base64SVG; // Return the original if it's not an SVG
			}

			const decodedSVG = atob(base64SVG.split(",")[1]);

			// Check if this is primarily a stroke-based or fill-based icon
			const hasStroke = decodedSVG.includes('stroke="');
			const hasColoredFill = /fill="(?!none)[^"]+"/g.test(decodedSVG);

			let modifiedSVG = decodedSVG;

			if (hasStroke && !hasColoredFill) {
				// This is a stroke-based icon (like chkicn2-6) - only change stroke color
				modifiedSVG = modifiedSVG.replace(/stroke="[^"]+"/g, `stroke="${color}"`);
			} else if (hasColoredFill) {
				// This is a fill-based icon (like chkicn1) - only change fill color
				modifiedSVG = modifiedSVG.replace(/fill="(?!none)[^"]+"/g, `fill="${color}"`);
			} else {
				// No existing fill or stroke, add fill to make it visible
				modifiedSVG = modifiedSVG.replace(/<path(?![^>]*fill=)/g, `<path fill="${color}"`);
				modifiedSVG = modifiedSVG.replace(/<svg(?![^>]*fill=)/g, `<svg fill="${color}"`);
			}

			const modifiedBase64 = `data:image/svg+xml;base64,${btoa(modifiedSVG)}`;
			return modifiedBase64;
		} catch (error) {
			console.error("Error modifying SVG color:", error);
			return base64SVG; // Return the original if there's an error
		}
	};


  const modifiedIcon = modifySVGColor(base64IconFinal, iconColor);
  





// useEffect(() => {
//   const handleStorageChange = () => {
//     // Retrieve stored guides from localStorage
//     const storedGuides = JSON.parse(localStorage.getItem("closedGuides_/") || "[]");

//     // Find the checklist object
//     const checklistObject = storedGuides.find((guide: any) => "checklist" in guide);

//     // Extract checkpointIds from checklist
//     const checkpointIds = checklistObject?.checkpointIds || [];

//     // Check for closed checkpoints in storedGuides (excluding checklist)
//     const closedCheckpoints = checkpointIds.filter((id: string) =>
//       storedGuides.some(
//         (guide: any) => guide.GuideId === id && guide.DontShowAgain === true
//       )
//     );

//     const isAnyGuideClosed = closedCheckpoints.length > 0;

//     // Update state based on localStorage data
//     if (isAnyGuideClosed) {
//       setIsGuideClosed(true);

//       // Update completedStatus for all closed checkpoints
//       setCompletedStatus((prevStatus) => ({
//         ...prevStatus,
//         ...Object.fromEntries(closedCheckpoints.map((id: string) => [id, true]))
//       }));
//       } else {
//       setAnchorEl(document.body);
//     }

   
//   };

//   if (!isGuideClosed) {
//     handleStorageChange();

//     // Listen for changes in localStorage
//     window.addEventListener("storage", handleStorageChange);

//     return () => {
//       window.removeEventListener("storage", handleStorageChange);
//     };
//   }

//   return () => {};
// }, [isGuideClosed]);

useEffect(() => {
  const handleStorageChange = () => {
    const storedGuides = JSON.parse(localStorage.getItem("closedGuides_/") || "[]");

    // Find the checklist object
    const checklistObject = storedGuides.find((guide: any) => "checklist" in guide);
    const existingChecklist = checklistObject?.checklist || [];

    // Extract checkpointIds
    const checkpointIds = checklistObject?.checkpointIds || [];

    // Check for closed checkpoints in storedGuides
    const closedCheckpoints = checkpointIds.filter((id: string) =>
      storedGuides.some(
        (guide: any) => guide.GuideId === id && guide.DontShowAgain === true
      )
    );

    if (closedCheckpoints.length > 0) {
      setIsGuideClosed(true);

      // Update completedStatus for closed checkpoints
      setCompletedStatus((prevStatus) => ({
        ...prevStatus,
        ...Object.fromEntries(closedCheckpoints.map((id: string) => [id, true]))
      }));

      // Filter out already existing checklist items
      const newChecklistItems = closedCheckpoints
        .filter((id: any) => !existingChecklist.some((item: any) => item.GuideId === id))
        .map((id: any) => ({
          GuideId: id,
          DontShowAgain: false,
          CompletedCheckPoint: true,
          GuideType: "Checklist",
        }));

      let updatedGuides;
      if (newChecklistItems.length > 0) {
        updatedGuides = storedGuides.map((guide: any) =>
          "checklist" in guide ? { ...guide, checklist: [...existingChecklist, ...newChecklistItems] } : guide
        );
        localStorage.setItem("closedGuides_/", JSON.stringify(updatedGuides));
      }
    } else {
      setAnchorEl(document.body);
    }
  };

  if (!isGuideClosed) {
    handleStorageChange();
    window.addEventListener("storage", handleStorageChange);

    return () => {
      window.removeEventListener("storage", handleStorageChange);
    };
  }
}, [isGuideClosed]);

  //for checklist to add checkpoints in localstorage


useEffect(() => {
  const handleStorageChange = () => {
    const storedGuides = JSON.parse(localStorage.getItem("closedGuides_/") || "[]");

    const closedGuides = storedGuides.filter((guide: any) => guide.DontShowAgain === true);

    const closedCheckpointIds = CheckpointsList
      ?.filter((checkpoint: any) => closedGuides.some((guide: any) => guide.GuideId === checkpoint.Id))
      .map((checkpoint: any) => checkpoint.Id);

    if (closedCheckpointIds?.length > 0) {
      setIsGuideClosed(true);

      setCompletedStatus((prevStatus) => ({
        ...prevStatus,
        ...Object.fromEntries(closedCheckpointIds?.map((id: string) => [id, true]))
      }));

      const existingChecklistObject = storedGuides.find((guide: any) => "checklist" in guide);
      const existingChecklist = existingChecklistObject?.checklist || [];

      const updatedChecklist = [
        ...existingChecklist,
        ...closedCheckpointIds.map((id: any) => ({
          GuideId: id,
          DontShowAgain: false,
          CompletedCheckPoint: true,
          GuideType: "Checklist",
        }))
      ];

      let updatedGuides;
      if (existingChecklistObject) {
        // Update existing checklist
        updatedGuides = storedGuides.map((guide: any) =>
          "checklist" in guide ? { ...guide, checklist: updatedChecklist } : guide
        );
      } else {
        // Create new checklist object if not present
        updatedGuides = [...storedGuides, { checklist: updatedChecklist }];
      }

      localStorage.setItem("closedGuides_/", JSON.stringify(updatedGuides));
    } else {
      setAnchorEl(document.body);
    }
  };

  if (!isGuideClosed) {
    handleStorageChange();

    window.addEventListener("storage", handleStorageChange);
    return () => {
      window.removeEventListener("storage", handleStorageChange);
    };
  }

  return () => {};
}, [isGuideClosed, CheckpointsList, data]);

const isRTL = 
  document.documentElement.getAttribute('dir') === 'rtl' ||
  document.body.getAttribute('dir') === 'rtl';


  // Only show checklist launcher and popup if within publish window
  const [isWithinSchedule, setIsWithinSchedule] = useState(true);
  useEffect(() => {
    if (!data) {
      setIsWithinSchedule(false);
      return;
    }
    const currentDate = new Date();
    const publishDate = data?.PublishDate ? new Date(data.PublishDate) : null;
    const unpublishDate = data?.UnPublishDate ? new Date(data.UnPublishDate) : null;
    let shouldShow = false;
    if (publishDate && unpublishDate) {
      shouldShow = currentDate >= publishDate && currentDate <= unpublishDate;
    } else if (publishDate && !unpublishDate) {
      shouldShow = currentDate >= publishDate;
    } else if (!publishDate && !unpublishDate) {
      shouldShow = true;
    }
    setIsWithinSchedule(shouldShow);
  }, [data]);

  return (
      
    <>
{data?.GuideStep?.length>0 && isWithinSchedule && ((!(Canvas?.HideAfterCompletion && allCompleted))) && 
 (
          <div>

 
       
<div style={{
  position: 'absolute',
  bottom: `${parseInt(Canvas?.YAxisOffset || "10")}px`,
   right: Launcher?.LauncherPosition?.Left === true ? 'auto' : `${parseInt(Canvas?.XAxisOffset || "10")}px`,
              left: Launcher?.LauncherPosition?.Left === true ? `${parseInt(Canvas?.XAxisOffset || "10")}px` : "auto",
  zIndex: 1000
}}>

            {data && (
              <button
                onClick={() => {
                  setIsOpen((prev: any) => !prev);
                 // setIsRightPanelVisible(true);
                
                }}
                style={{
                  backgroundColor: Launcher?.LauncherColor,
                  borderRadius: Launcher?.Type === "Text" || Launcher?.Type === "Icon+Txt" ? "16px" : "50%", // Adjust dynamically
                  height: '54px',
                  width:( Launcher?.Type === "Text" || Launcher?.Type === "Icon+Txt")
                    ? `auto`  // Increase width dynamically
                    : '54px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  padding: '8px',
                  boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
                  transition: 'all 0.2s ease',
                  border: 'none',
                  cursor: 'pointer',
                  position: 'relative'
                }}
              >
                {isOpen ? (
      <span
        dangerouslySetInnerHTML={{ __html: chkcloseicon }}
        style={{
          borderRadius: '50%',
          padding: '8px',
          display: 'flex',
          cursor: "pointer",
          color: "white",
    stroke: "white",
    fill: "white",
        }}
      />
    ) : (
      <>
        {Launcher?.Type === "Icon" && (
          <img
            src={modifiedIcon}
            alt="icon"
            style={{ width: "20px", height: "20px" }}
                        />
        )}

        {Launcher?.Type === "Text" &&
          Launcher?.Text && (
            <span
              style={{
                fontSize: "16px",
                fontWeight: "bold",
                color: Launcher?.TextColor,
                padding: "8px",
                whiteSpace: "nowrap"
              }}
            >
              {Launcher.Text}
            </span>
          )}

        {Launcher?.Type === "Icon+Txt" &&
          Launcher?.Text && Launcher?.Icon &&(
            <span
              style={{
                display: "flex",
                alignItems: "center",
                gap: "8px",
                color: Launcher?.TextColor,
                fontSize: "16px",
                fontWeight: "bold",
                padding: "8px"
              }}
            >
              <img
                src={modifiedIcon}
                alt="icon"
                style={{ width: "20px", height: "20px" }}
              />
              {Launcher?.Text}
            </span>
          )}
      </>
    )}
    
                {/* Notification Badge */}
                {Launcher?.NotificationBadge && (
                  <div style={{
                    position: 'absolute',
                    top: '-8px',
                    right: '-8px',
                    backgroundColor: Launcher?.NotificationBadgeColor, // red-500
                    color: `${Launcher?.NotificationTextColor || Launcher?.NotificationBadgeText}`,
                    fontSize: '12px',
                    borderRadius: '9999px',
                    height: '24px',
                    width: '24px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    {remainingCount}
                  </div>
                )}
              </button>
            )}
            </div>
    
            <ChecklistPopup
              data={data}
              guideDetails={guideDetails}
              isRightPanelVisible={isRightPanelVisible}
              setIsRightPanelVisible={setIsRightPanelVisible}
            isOpen={isOpen}
              onClose={() => setIsOpen(false)}
              onRemainingCountUpdate={handleRemainingCountUpdate}
              onStepCheckpoint={handleStepCheckpoint}
              Canvas={Canvas}
              Checkpoints={Checkpoints}
              CheckpointsList={CheckpointsList}
              TitleSubTitle={TitleSubTitle}
              completedStatus={completedStatus}
            setCompletedStatus={setCompletedStatus}
            Launcher={Launcher}
            />
          </div>
        )}    
      </>
    );
};
export default ChecklistLauncher;
