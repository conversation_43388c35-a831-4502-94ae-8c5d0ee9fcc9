import { Box, Button, Typography, DialogActions, MobileStepper, LinearProgress } from "@mui/material";
import { useEffect, useMemo, useState } from "react";
import { CustomIconButton } from "../Button";
import CloseIcon from "@mui/icons-material/Close";
import { IReponse, useFetch } from "../../hooks/useFetch";
import { POST } from "../../service/APIService";
import { upload } from "@testing-library/user-event/dist/upload";
import { BannerWrapper, IconButtonSX, InnerWrapper } from "../Banner/Banner.style";
import { useLocation } from "react-router-dom";

interface TooltipGuideProps {
    data: any;
    currentStep: any;
    setCurrentStep: any
    onClose: any;
    onRestart?: () => void; // Added onRestart prop
    selectedOption: any;
    progress: any;
}
const BannerStepUserView: React.FC<TooltipGuideProps> = ({ data,currentStep,setCurrentStep,onClose,onRestart,selectedOption, progress }) => {
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(document.body);
    const [showBanner, setShowBanner] = useState(true);
      const location = useLocation();
    
    // For deployment, uncomment the following lines
const currentUrl = window.location.href;    // const [response] = useFetch({
    //   url: `/EndUserGuide/GetGuideListByTargetUrl?targetUrl=${currentUrl}`,
    // });
    //${encodeURIComponent(currentUrl)
    // For local testing
    const [response, setResponse] = useState<IReponse>({
		data: [],
		loading: false,
		error: {
			message: "",
			isError: false,
		},
	});


    // const handleCloseBanner = async () => {
    //   const newData = { ...Banner, Visited: true, VisitedDate: new Date().toISOString() };
    //   try {s
    //     await POST("/EndUserGuide/Updateguide", newData);
    //     setShowBanner(false);
    //   } catch (error) {
    //     //console.log(error, "Post error");
    //   }
    // };

    const renderHtmlSnippet = (snippet: string) => {
        return snippet.replace(/(<a\s+[^>]*href=")([^"]*)("[^>]*>)/g, (match, p1, p2, p3) => {
            return `${p1}${p2}" target="_blank"${p3}`;
        });
    };

    // const handleCloseBanner = () => {
    //   localStorage.setItem('bannerClosed', 'true'); // Set closed state persistently
    //   // localStorage.setItem('bannerPushedDown', 'true');
    //   const body = document.querySelector("body") as HTMLElement;

    //   if (body) {
    //     body.style.marginTop = "0"; // Reset the body margin
    //   }
    //   else {
    //   }
    //   // const banner = document.getElementById("div");
    //   //       banner?.remove();
    //   // const body = document.querySelector('body') as HTMLElement;
    //   // body.style.marginTop = '0px'
    //   setShowBanner(false);
    // };

	const Banner = data?.GuideStep?.[currentStep];

    //&& item.TargetUrl === window.location.href
    // Dissmiss icon property
    const Modal = Banner?.Modal;
    const isCloseDisabled = Modal?.DismissOption ?? false;
    // const data = Banner.PublishDate;
    // const formatDate = formatDateTime(data, 'dd-MM-yyyy');
    const uploadedImages = Banner?.ImageProperties[0]?.CustomImage;
    const image = uploadedImages && uploadedImages.length > 0 ? uploadedImages[0] : null;
    const isBase64 = (url: any) => {
        // Return false if url is null or undefined
        return url ? url.startsWith("data:image/") : false;
    };

    // text properties
    const textField = Banner?.TextFieldProperties[0] || {};
    const { Text: textFieldText, Alignment, Hyperlink, Emoji, TextProperties } = textField;
    const { Bold, Italic, BulletPoints, TextColor, TextFormat } = TextProperties || {};
    // Custom button properties
    const CustomButton = Banner?.ButtonSection[0]?.CustomButtons || {};
    // Access ButtonProperties and get the Padding value
    const ButtonPadding = CustomButton.ButtonProperties?.Padding;
    const FontSize = CustomButton.ButtonProperties?.FontSize;
    const ButtonWidth = CustomButton.ButtonProperties?.Width;
    const Font = CustomButton.ButtonProperties?.Font;
    const ButtonTextColor = CustomButton.ButtonProperties?.ButtonTextColor;
    const ButtonBackgroundColor = CustomButton.ButtonProperties?.ButtonBackgroundColor;

    const ButtonAlignment = CustomButton.Alignment || "center";
    const ButtonAction = CustomButton.ButtonAction || {};
    const {
        Top: PaddingTop = 0,
        Left: PaddingLeft = 0,
        Right: PaddingRight = 0,
        Bottom: PaddingBottom = 0,
    } = CustomButton.Padding || {};

    const handleClose = () =>
    {
        onClose();
        const body = document.querySelector("body") as HTMLElement;
        if (body) {

            body.style.setProperty("padding-top", "0px", "important");
            body.style.setProperty("max-height", "initial", "important");
        }
        setShowBanner(false);
    }
    // Define handler to bind action on button click
    const handleButtonClick = (action: any) => {
        if (action.Action === "open-url" || action.Action === "open"|| action.Action === "openurl") {
            window.open(action.TargetUrl);
            //onContinue();
            //console.log("targetur", action.TargetUrl);
        } else if (action.Action === "start-interaction") {
            // onContinue();
            // setOverlayValue(false);
        } else if (action.Action === "close" || action.Action === "") {
            {
                if (data?.GuideType === "Tour") {
                    onClose();
                    const body = document.querySelector("body") as HTMLElement;

                    if (body) {

                        body.style.setProperty("padding-top", "0px", "important");
                        body.style.setProperty("max-height", "initial", "important");
                    }
                    let element = document.getElementById("dynamic-banner");
                    if (element) {
                        element.remove();
                    }
                    setShowBanner(false);
                }
                else {
                    handleCloseBanner();

                }
            }
            // onClose();
            // setOverlayValue(false);
        }
        else if (action.Action.toLowerCase() === "restart") {
            setTimeout(() => {
                const styleTag = document.getElementById("dynamic-banner");
                if (styleTag) {
                    styleTag.remove();
                }
                document.body.classList.remove("dynamic-banner");
            }, 100);
            if (onRestart) {
                onRestart(); // Use the dedicated restart handler if available
            } else {
                setCurrentStep(0); // Fallback to setting current step directly
            }
            // Make sure the banner is still visible after restart
            setShowBanner(true);
        }
        else if (action.Action.toLowerCase() === "next") {
            setTimeout(() => {
                const styleTag = document.getElementById("dynamic-banner");
                if (styleTag) {
                    styleTag.remove();
                }
                document.body.classList.remove("dynamic-banner");
            }, 100);
                setCurrentStep(currentStep + 1);
            }
        else
        {
            setTimeout(() => {
                const styleTag = document.getElementById("dynamic-banner");
                if (styleTag) {
                    styleTag.remove();
                }
                document.body.classList.remove("dynamic-banner");
            }, 100);


            setCurrentStep(currentStep - 1);
            }
    };

    // Design properties
    const designProps = Banner?.Design || {};
    const BannerWidth = designProps.ViewPortWidth || "100%";
    const BackdropShadow = "0px 1px 15px rgba(0, 0, 0, 0.7)";
    const IconColor = designProps.IconColor || "#000";
    const IconOpacity = designProps.QuietIcon ? 0.5 : 1.0; // Reduce opacity if QuietIcon is true

    // advanced properties
    const showAfter = Banner?.Advanced?.ShowAfter || "0s";
    const showDelay = parseInt(showAfter) * 1000;
    useEffect(() => {
        if (Banner) {
            const currentDate = new Date();
            const publishDate = Banner?.PublishDate ? new Date(Banner.PublishDate) : null;
            const unpublishDate = Banner?.UnPublishDate ? new Date(Banner.UnPublishDate) : null;
            if (publishDate && unpublishDate) {
                if (currentDate >= publishDate && currentDate <= unpublishDate) {
                    const timer = setTimeout(() => {
                        const banner = document.getElementById("quickAdopt_banner") as HTMLElement | null;
                        const bannerClosed = localStorage.getItem("bannerClosed");
                        if (banner) {
                            addDynamicTag();


                        }

                        setShowBanner(true);
                    }, showDelay);
                    return () => clearTimeout(timer);
                }
            } else if (publishDate && currentDate >= publishDate) {
                const timer = setTimeout(() => {
                    //setShowBanner(true);
                    const banner = document.getElementById("quickAdopt_banner") as HTMLElement | null;

                    const bannerClosed = localStorage.getItem("bannerClosed");
                    if (banner) {
                        addDynamicTag();


                    }
                    setShowBanner(true);
                }, showDelay);
                return () => clearTimeout(timer);
            } else if (!publishDate && !unpublishDate) {
                setShowBanner(true);
                addDynamicTag();
            }
        } else {
            // const banner = document.getElementById("div");
            // banner?.remove();
            // const body = document.querySelector('body') as HTMLElement;
            // body.style.marginTop = '0px'
        }
    }, [response, showDelay, Banner]);
    interface Guide {
        GuideId: string;
        DontShowAgain: boolean;
        GuideType: string;
    }

    const addDynamicTag = () => {
        let styleTag = document.getElementById("dynamic-banner") as HTMLStyleElement;
        document.body.classList.add("dynamic-banner");
        if (!styleTag) {
            styleTag = document.createElement("style");
            styleTag.id = "dynamic-banner";
            if (!isCoverTop) {
                const height = 31 + parseInt(Padding)*2 + parseInt(BorderSize)*2 + (Array.isArray(CustomButton) ? 9 : 0 ) + (enableProgress ? progressTop : 0);
                const styles = `
                .dynamic-banner {
                    padding-top: ${height}px !important;
                    max-height: calc(100% - 55px);
                }
                    .dynamic-body-style header {
						top: ${height}px !important;
					}
					
                        		.dynamic-body-style .page-sidebar {
						padding-top: ${height}px !important;
					}
            `;
            styleTag.innerHTML = styles;
            }
            document.head.appendChild(styleTag);
        }

    }
    useEffect(() => {
        const handleStorageChange = () => {
            const storedGuides: Guide[] = JSON.parse(localStorage.getItem("closedGuides_/") || "[]");
            const isGuideClosed = storedGuides.some(
                (guide) => guide.GuideId === Banner?.GuideId && guide?.DontShowAgain === true && guide?.GuideType?.toLowerCase()==="tour"
            );
            if (response && Banner?.length > 0 && isGuideClosed) {
                setShowBanner(false);
            } else if (Banner?.length > 0) {
                setShowBanner(true);
                addDynamicTag();
            }
        };

        handleStorageChange();
        window.addEventListener("storage", handleStorageChange);
        return () => {
            window.removeEventListener("storage", handleStorageChange);
        };
    }, [response, Banner?.GuideId, Banner?.length]);
    

    const handleCloseBanner = () => {
        const storedGuides = JSON.parse(localStorage.getItem("closedGuides_/") || "[]");
        const updatedGuides = [
            ...storedGuides,
            {
                GuideId: Banner?.GuideId,
                DontShowAgain: true,
                GuideType:Banner?.GuideType
            },
        ];
        localStorage.setItem("closedGuides_/", JSON.stringify(updatedGuides));
        const body = document.querySelector("body") as HTMLElement;
        if (body) {

            body.style.setProperty("padding-top", "0px", "important");
            body.style.setProperty("max-height", "initial", "important");
        }
        setShowBanner(false);
    };
    ////console.log(`Banner will display after ${showAfterDuration} milliseconds`);

    //canvas properties
    const canvas = Banner?.Canvas || {};
    const BackgroundColor = canvas.BackgroundColor || "#f1f1f7";
    const Width = canvas.Width || "100%"; // Default to 100%
    const Radius = canvas.Radius || "0"; // Default radius
    const Padding = canvas.Padding || "10"; // Default padding
    const BorderSize = canvas.BorderSize || "2"; // Default border size
    const BorderColor = canvas.BorderColor || "#f1f1f7"; // Default border color
    const Position =  "absolute";
    const zindex = canvas.Zindex || "999999";
    const isCoverTop =
        !canvas ||                            // canvas is undefined or null
        Object.keys(canvas).length === 0 ||   // canvas is empty
        !canvas.Position ||                   // position is missing or empty
        canvas.Position === "Cover Top";      // position matches expected
    // html snippet property
    const htmlSnippet = Banner?.HtmlSnippet || "";
    const steps = data?.GuideStep;
    const primaryColor = "#5F9EA0";
    const progressColor = data?.GuideStep?.[currentStep]?.Modal?.ProgressColor=== "var(--primarycolor)" ? primaryColor : data?.GuideStep?.[currentStep]?.Modal?.ProgressColor;

    const enableProgress = data?.GuideStep?.[currentStep]?.Tooltip?.EnableProgress || false;
    let progressTop = 0;
	function getProgressTemplate(selectedOption: any) {
        if (selectedOption === "1") {
            progressTop = 10;
			return "dots";
        } else if (selectedOption === "2") {
            progressTop = 7;
			return "linear";
        } else if (selectedOption === "3") {
            progressTop = 7;
			return "BreadCrumbs";
		}
        else if (selectedOption === "4") {
            progressTop = 14;
			return "breadcrumbs";
		}

		return data?.GuideStep?.[currentStep]?.Tooltip?.ProgressTemplate || "dots";
	}
    const progressTemplate = getProgressTemplate(selectedOption);
	const renderProgress = () => {
        if (!enableProgress) return null;
        
		if (progressTemplate === "dots") {
			return (
				<MobileStepper
					variant="dots"
					steps={steps.length}
					position="static"
					activeStep={currentStep}
					sx={{ backgroundColor: "transparent", padding:"8px 0 0 0 !important",  "& .MuiMobileStepper-dotActive": {
                        backgroundColor: progressColor, // Active dot
                      }, }}
					backButton={<Button style={{ visibility: "hidden" }} />}
					nextButton={<Button style={{ visibility: "hidden" }} />}
				/>
			);
		}
        if (progressTemplate === "BreadCrumbs") {
			return (
                <Box sx={{padding:"8px 0 0 0 !important",display: "flex",
					alignItems: "center",
					placeContent: "center",
					gap: "5px"}}>
                  {/* Custom Step Indicators */}

                    {Array.from({ length: steps.length }).map((_, index) => (
                      <div
                        key={index}
                        style={{
                          width: '14px',
                          height: '4px',
                          backgroundColor: index === currentStep  ? progressColor : '#e0e0e0', // Active color and inactive color
                          borderRadius: '100px',
                        }}
                      />
                    ))}

                </Box>
              );
		}
		if (progressTemplate === "breadcrumbs") {
			return (
				<Box sx={{padding:"8px !important",display: "flex",
				alignItems: "center",
				placeContent: "flex-start",
				}}>
					<Typography sx={{color : progressColor, fontSize:"12px"}} >
                    Step {currentStep+1} of {steps.length}
					</Typography>
				</Box>
			);
		}

		if (progressTemplate === "linear") {
			return (
				<Box sx={{    width: "calc(50% - 410px)",
					padding: "8px 0 0 0"}}>
					<Typography variant="body2">
						<LinearProgress
							variant="determinate"
							value={progress}
                            sx={{'& .MuiLinearProgress-bar': {
                                backgroundColor: progressColor, // progress bar color
                              },}}
						/>
					</Typography>
				</Box>
			);
		}

		return null;
    };
    useEffect(() => {
		if (canvas?.Position === "Cover Top") {
			document.body.style.overflow = "hidden";
		} else {
			document.body.style.overflow = "";
		}

		// Cleanup function to restore overflow when component unmounts
		return () => {
			document.body.style.overflow = "";
		};
	}, [canvas?.Position]); // Re-run when canvas position changes
    return (
        <div
            className="qadpt-turendusr qadpt-banendusr">
            {showBanner && (

                <Box
                    sx={{
                        ...BannerWrapper,
                        maxWidth: BannerWidth,
                        boxShadow: isCoverTop ? BackdropShadow : "none",
                        backgroundColor: `${BackgroundColor ? BackgroundColor : '#f1f1f7'} !important`,
                        // width: Width,
                        //borderRadius: Radius,
                        padding: `${Padding}px`,
                        // border: `${BorderSize}px solid ${BorderColor}`,
                        borderTop:
							 `${BorderSize}px solid ${BorderColor} !important`
								,
								borderRight:
								 `${BorderSize}px solid ${BorderColor} !important`
								,
								borderLeft:
						  `${BorderSize}px solid ${BorderColor} !important`,

					borderBottom: `${BorderSize}px solid ${BorderColor} !important`
							,
                        position: Position,
                        zIndex: zindex,
                    }}
                >
                    <Box
                        flex={1}
                        sx={InnerWrapper}
                    >

                        {htmlSnippet.includes("<") && htmlSnippet.includes(">") ? (
                            <Box
                                sx={{ marginTop: 2,whiteSpace: "pre-wrap",
                                wordBreak: "break-word", }}
                                dangerouslySetInnerHTML={{ __html: htmlSnippet }}
                            />
                        ) : (
                            <Box sx={{ marginTop: 2,whiteSpace: "pre-wrap",
                            wordBreak: "break-word", margin:"0px"}}>{htmlSnippet}</Box>
                        )}
                        {Emoji && (
                            <Typography
                                component="span"
                                sx={{
                                    fontWeight: Bold ? "bold" : "normal",
                                    fontStyle: Italic ? "italic" : "normal",
                                    color: TextColor,
                                    textAlign: Alignment,
                                    marginTop: 1,
                                }}
                            >
                                {Emoji}
                            </Typography>
                        )}

                            <Box key={Banner?.StepId} sx={{display:"flex",alignItems:"center",width:"100%"}}>
                                {Banner?.TextFieldProperties?.map((textField:any) => (
                                    <Box
                                        key={textField.Id}
                                        sx={{
                                            color: textField.TextProperties?.TextColor || "inherit",
                                            textAlign: textField.Alignment || "center",
                                            width: "100%",
                                            margin: "0px",
                                            padding:"5px 2px"                                            // mt: 1,
                                        }}
                                    >
                                        {textField.Hyperlink ? (
                                            <a
                                                href={textField.Hyperlink}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                style={{
                                                    color: textField.TextProperties?.TextColor || "inherit",
                                                    textDecoration: "underline",
                                                }}
                                                dangerouslySetInnerHTML={{
                                                    __html: textField.Text,
                                                }}
                                            />
                                        ) : (
                                                <Typography
                                                style={{
                                                    fontSize: "14px",
                                                    }}
                                                   // component="div"
                                                    sx={{
                                                        "& p": {
                                                            margin:"0px"
                                                        }
                                                        ,
                                                        "& a": {
                                                            color: textField.TextProperties?.TextColor || "inherit",
                                                            textDecoration: "underline",
                                                        },
                                                        "& a[target='_blank']": {
                                                            cursor: "pointer",
                                                        },
                                                        whiteSpace: "pre-wrap",
									                    wordBreak: "break-word",
                                                    }}
                                                    onClick={(e) => {
                                                        const target = e.target as HTMLAnchorElement;
                                                        if (target.tagName === "A" && target.href) {
                                                            window.open(target.href, "_blank");
                                                        }
                                                    }}
                                                dangerouslySetInnerHTML={{
                                                    __html: textField.Text,
                                                }}
                                            />
                                        )}
                                    </Box>
                                ))}
                             {Array.isArray(CustomButton) &&
 CustomButton.some((button: any) => button.ButtonName && button.ButtonName.trim() !== "") && (
                                        <DialogActions
                                        sx={{
                                        padding: "0 !important",
                                        height: "40px",
                                            }}

     >
         {CustomButton.map((button: any, index: any) => (
             <Button
                 key={index}
                 onClick={() => handleButtonClick(button.ButtonAction)}
                 variant="contained"
                 style={{
                     color: button.ButtonProperties?.ButtonTextColor || "#fff",
                     backgroundColor: button.ButtonProperties?.ButtonBackgroundColor || "#007bff",
                     border: button.ButtonProperties?.ButtonBorderColor ?`1px solid ${button.ButtonProperties?.ButtonBorderColor}` : "none",
                     margin: "2px 5px",
                     fontSize: button.ButtonProperties?.FontSize || 15,
                     width: button.ButtonProperties?.Width || "auto",
                     padding: "8px 12px",
                     textTransform: "none",
                     borderRadius: "20px",
                     lineHeight:"normal"
                 }}
                 sx={{
                     "&:hover": {
                         filter: "brightness(1.2)",
                     },
                 }}
             >
                 {button.ButtonName}
             </Button>
         ))}
     </DialogActions>
 )}

                                {/* Render Image if available */}
                                {image && (
                                    <Box
                                        component="a"
                                        href={Banner?.ImageProperties?.Hyperlink || "#"}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        sx={{
                                            display: "block",
                                            textAlign: Banner?.ImageProperties?.Alignment || "center",
                                        }}
                                    >
                                        <Box
                                            component="img"
                                            src={isBase64(image.Url) ? image.Url : image.Url}
                                            sx={{
                                                maxHeight: Banner?.ImageProperties?.MaxImageHeight || "auto",
                                                padding: `${Banner?.ImageProperties?.Padding?.Top || 0}px ${Banner?.ImageProperties?.Padding?.Right || 0
                                                    }px ${Banner?.ImageProperties?.Padding?.Bottom || 0}px ${Banner?.ImageProperties?.Padding?.Left || 0
                                                    }px`,
                                                objectFit: Banner?.ImageProperties?.UploadedImages?.[0]?.Fit || "contain",
                                                display: "block",
                                                margin: "0 auto",
                                            }}
                                        />
                                    </Box>
                                )}
                            </Box>


                    {isCloseDisabled && (
                        <CustomIconButton
                        sx={{
                            boxShadow: "rgba(0, 0, 0, 0.15) 0px 4px 8px",
                            margin: "2px 5px",
                background: "#fff !important",
                border: "1px solid #ccc",
                zIndex:"999999",
                    borderRadius: "50px",
                padding:"5px !important",
                            opacity: IconOpacity,
                        }}
                        onClick={handleCloseBanner}
                    >
                        <CloseIcon
                           sx={{zoom:"0.7",color:"#000"}}
                        />
                    </CustomIconButton>
                    )}
                    </Box>
                    <Box sx={{
  ...(progressTemplate === "linear" && {
	display: "flex",
	placeContent: "center",
	alignItems:"center"
  }),
}}>
                    {steps.length >= 1 && enableProgress  ? (
                                <>
                                    {renderProgress()}
                                </>
                            ) : (
                                null
                            )}
                            </Box>
                    </Box>


            )}

        </div>
    );
};

export default BannerStepUserView;