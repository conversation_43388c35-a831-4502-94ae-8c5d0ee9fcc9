import axios, { AxiosRequestConfig, AxiosError } from "axios";
export const adminUrl = process.env.REACT_APP_ADMIN_API;
export const userUrl = process.env.REACT_APP_USER_API;
export const idsUrl = process.env.REACT_APP_IDS_API;
const speakModal = process.env.REACT_APP_SpeakModal || "ElevenLabs";
export const donaEnabled=process.env.REACT_APP_DONA_ENABLED;


const adminApiService = axios.create({
  baseURL: adminUrl,
});

const userApiService = axios.create({
  baseURL: userUrl,
});

export { userApiService, adminApiService ,speakModal };