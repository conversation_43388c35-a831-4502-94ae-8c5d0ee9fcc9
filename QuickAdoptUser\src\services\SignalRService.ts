import * as signalR from '@microsoft/signalr';

export interface WorkAgentMessage {
  ResponseType?: string;
  Message: string;
  Guide?: any;
  ChatHistoryId?: string;
}
  interface RadioGroupData {
  xpath: string;
  value: string;
}
export interface UserInputRequest {
  Message: string;
  FieldName: string;
  IsRequired: boolean;
  FieldType: string;
}

export interface DomDataExtractionRequest {
  Selectors: Record<string, string>;
  Message: string;
}

export interface DomDataBindingRequest {
  Data: Array<{
    xpath: string;
    labelName: string;
    type: string;
    value: any;
    name: string;
    selector: string;
    cssSelector: string;
  }>;
  Message: string;
}

export interface ElementExtractionData {
  xpath: string;
  labelName: string;
  type: string;
  value: string | null;
  key: string;
}

export interface WorkAgentCallbacks {
  onMessage: (message: WorkAgentMessage | string) => void;
  onError: (error: string) => void;
  onUserInputRequest?: (inputRequest: UserInputRequest) => void;
  onRequestConfirmation?: (confirmationRequest: { Message: string; Data: Record<string, string> }) => void;
  onNavigationConfirmation?: (navigationconfirmation: {success: boolean, message: string}) => void;
  onNavigationRequest?: (navigationRequest: { Url: string; Id:string; Message: string }) => void;
  
  onDomDataExtractionRequest?: (extractionRequest: DomDataExtractionRequest) => void;
  onDomDataBindingRequest?: (bindingRequest: DomDataBindingRequest) => void;
  onBindingDataReady?: (data: Array<{labelName: string; value: any; xpath: string; type: string; key: string; name: string; selector: string; cssSelector: string}>) => void;
  onConnectionEstablished?: (message: string) => void;
  onConnectionStateChanged?: (state: any) => void;
}

class WorkAgentSignalRService {
  private static instance: WorkAgentSignalRService;
  private connection: signalR.HubConnection | null = null;
  private isConnecting = false;
  private isInitialized = false;
  private activeCallbacks: Set<WorkAgentCallbacks> = new Set();
  private lastConnectionId: string | null = null;
  private allElementsData: Array<ElementExtractionData> = [];

  // New properties to track confirmation conditions
  private formSubmittedSuccessfully = false;
  private workAgentCompleted = false;
  private pendingConfirmationRequest: { Message: string; Data: Record<string, string> } | null = null;

  // Singleton pattern to ensure single connection
  public static getInstance(): WorkAgentSignalRService {
    if (!WorkAgentSignalRService.instance) {
      WorkAgentSignalRService.instance = new WorkAgentSignalRService();
    }
    return WorkAgentSignalRService.instance;
  }
  public getFormSubmissionStatus(): boolean {
    return this.formSubmittedSuccessfully;
  }
  
  private constructor() {
    // Private constructor for singleton
  }

  // Generate or retrieve a consistent user ID for sticky sessions
  private getOrCreateUserId(): string {
    const storageKey = 'workagent-user-id';
    let userId = localStorage.getItem(storageKey);

    if (!userId) {
      // Generate a unique user ID for this browser session
      userId = `user-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
      localStorage.setItem(storageKey, userId);
      console.log('Generated new WorkAgent user ID for sticky sessions:', userId);
    }

    return userId;
  }



private initializeConnection() {
  if (this.isInitialized) return;

  const adminApiUrl = process.env.REACT_APP_USER_API || 'http://localhost:60552/api';
  const baseUrl = adminApiUrl.replace('/api', '');

    const userId = this.getOrCreateUserId();

  const hubUrl = `${baseUrl}/WorkAgentHub?userId=${encodeURIComponent(userId)}`;

  console.log('Initializing WorkAgent SignalR connection to:', hubUrl);

  this.connection = new signalR.HubConnectionBuilder()
    .withUrl(hubUrl, {
      skipNegotiation: false,
      transport: signalR.HttpTransportType.WebSockets,
      accessTokenFactory: () => {
        return localStorage.getItem('authToken') || '';
      },
       headers: {
          'X-MS-SignalR-Sticky': 'true',
          'X-MS-SignalR-User-Id': userId
        }
    })
    .withAutomaticReconnect({
      nextRetryDelayInMilliseconds: (retryContext: any) => {
        if (retryContext.previousRetryCount === 0) {
          return 1000;
        }
        return Math.min(1000 * Math.pow(2, retryContext.previousRetryCount), 30000);
      }
    })
    .configureLogging(signalR.LogLevel.Information)
    .build();


  this.setupEventHandlers();
  this.isInitialized = true;
}


  private setupEventHandlers() {
    if (!this.connection) return;

    // Handle connection established event from WorkAgentHub
    this.connection.on('ConnectionEstablished', (message: string) => {
      console.log('WorkAgent Connection Established:', message);
      this.activeCallbacks.forEach(callback => {
        if (callback.onConnectionEstablished) {
          callback.onConnectionEstablished(message);
        }
      });
    });

    // Handle incoming messages from WorkAgentHub
    this.connection.on('ReceiveMessage', (response: WorkAgentMessage | string) => {
      console.log('WorkAgent Message Received:', response);

      // Handle both string messages (legacy) and WorkAgentMessage objects
      let messageString: string;
      if (typeof response === 'string') {
        messageString = response;
        response = {
          ResponseType: "message",
          Message: response,
          Guide: null
        }
      } else {
        messageString = response.Message;
      }

      // Check if this is the "Form Submitted Successfully" message
      if (messageString === "Form submitted successfully!") {
        console.log('🎯 Form Submitted Successfully message received');
        this.formSubmittedSuccessfully = true;
        this.checkAndTriggerConfirmation();
      }

      this.activeCallbacks.forEach(callback => {
        callback.onMessage(response);
      });
    });

    // Handle user input requests from WorkAgentHub
    this.connection.on('RequestUserInput', (inputRequest: UserInputRequest) => {
      console.log('WorkAgent User Input Request:', inputRequest);
      this.activeCallbacks.forEach(callback => {
        if (callback.onUserInputRequest) {
          callback.onUserInputRequest(inputRequest);
        }
      });
    });

    // Handle confirmation requests from WorkAgentHub
    this.connection.on('RequestConfirmation', (confirmationRequest: { Message: string; Data: Record<string, string> }) => {
      console.log('WorkAgent RequestConfirmation event received:', confirmationRequest);
      console.log('Active callbacks count:', this.activeCallbacks.size);
      console.log('🔄 Storing confirmation request - waiting for work agent completion and form submission success');

      // Store the confirmation request instead of immediately triggering it
      this.pendingConfirmationRequest = confirmationRequest;
      this.workAgentCompleted = true; // Mark that work agent has completed its tasks
      this.checkAndTriggerConfirmation();
    });


    this.connection.on('RequestNavigation', (navigationRequest: { Url: string; Id:string; Message: string }) => {
      console.log('WorkAgent Navigation Request:', navigationRequest);
      this.activeCallbacks.forEach(callback => {
        if (callback.onNavigationRequest) {
          callback.onNavigationRequest(navigationRequest);
        }
      });
    });

   









this.connection.on('ExtractDomData', async (extractionRequest: DomDataExtractionRequest) => {
  console.log('WorkAgent DOM Data Extraction Request:', extractionRequest);

  try {
    const selectorsArray = Array.isArray(extractionRequest.Selectors)
      ? extractionRequest.Selectors
      : [];

    const updatedData = await this.extractDomData(selectorsArray);
    console.log('Updated Selector Data:', updatedData);

    await this.connection?.invoke('ProcessDomData', updatedData);
    console.log('DOM data sent to server successfully');
  } catch (error) {
    console.error('Error sending DOM data:', error);
    try {
      await this.connection?.invoke('ProcessDomData', []);
      console.log('Fallback: Empty DOM data sent to server');
    } catch (fallbackError) {
      console.error('Error sending fallback DOM data:', fallbackError);
    }
  }

  this.activeCallbacks.forEach(callback => {
    if (callback.onDomDataExtractionRequest) {
      callback.onDomDataExtractionRequest(extractionRequest);
    }
  });
});


   // Handle DOM data binding requests from WorkAgentHub
this.connection.on('BindDomData', async (bindingRequest: DomDataBindingRequest) => {
  console.log('WorkAgent DOM Data Binding Request received:', bindingRequest);
  console.log('Data to bind (raw from backend):', bindingRequest.Data);

  // ✅ Transform backend data format to frontend format
  const mappedData = bindingRequest.Data.map((item: any) => ({
    name: item.Name,
    xpath: item.Xpath,
    selector: item.Selector,
    labelName: item.LabelName,
    cssSelector: item.CSSSelector,
    type: item.Type,
    value: item.Value,
  }));

  console.log('✅ Mapped data to frontend format:', mappedData);

  // Create BindingDomXpath data using mapped data
  const bindingDomXpathData = this.createBindingDomXpathData(mappedData);
  console.log('BindingDomXpath:', bindingDomXpathData);

    // Immediately acknowledge receipt and allow server to proceed to next step
  try {
    await this.connection?.invoke('ProcessDomDataBindingConfirmation', {
      Success: true,
      Message: 'DOM data binding acknowledged - worker agent starting automatically'
    });
    console.log('DOM data binding acknowledgment sent to server immediately');
  } catch (error) {
    console.error('Error sending DOM data binding acknowledgment to server:', error);
  }

  

  // Notify callbacks about binding data being ready - this will automatically start worker agent
  console.log('🚀 Binding data ready - notifying callbacks to start worker agent automatically');
  console.log('📊 Active callbacks count:', this.activeCallbacks.size);
  console.log('📊 Binding data to send:', bindingDomXpathData);

  let callbackIndex = 0;
  this.activeCallbacks.forEach(callback => {
    callbackIndex++;
    console.log(`📞 Calling callback ${callbackIndex}/${this.activeCallbacks.size}`);
    if (callback.onBindingDataReady) {
      console.log('✅ Calling onBindingDataReady callback');
      callback.onBindingDataReady(bindingDomXpathData);
    } else {
      console.log('⚠️ onBindingDataReady callback not found');
    }
    if (callback.onDomDataBindingRequest) {
      callback.onDomDataBindingRequest(bindingRequest);
    }
  });

  console.log('✅ All callbacks notified - worker agent should start automatically');
});

   
    // Handle agent errors from WorkAgentHub
    this.connection.on('AgentError', (error: string) => {
      console.error('WorkAgent Error:', error);
      this.activeCallbacks.forEach(callback => {
        callback.onError(error);
      });
    });

    // Handle connection state changes
    this.connection.onreconnecting((error:any) => {
      console.log('WorkAgent SignalR Reconnecting:', error);
      this.activeCallbacks.forEach(callback => {
        if (callback.onConnectionStateChanged) {
          callback.onConnectionStateChanged(signalR.HubConnectionState.Reconnecting);
        }
      });
    });

    this.connection.onreconnected((connectionId:any) => {
      console.log('🔄 WorkAgent SignalR Reconnected with Connection ID:', connectionId);
      console.log('🏷️ User ID for sticky sessions:', this.getOrCreateUserId());
      this.activeCallbacks.forEach(callback => {
        if (callback.onConnectionStateChanged) {
          callback.onConnectionStateChanged(signalR.HubConnectionState.Connected);
        }
      });
    });

    this.connection.onclose((error:any) => {
      console.log('WorkAgent SignalR Connection Closed:', error);
      this.activeCallbacks.forEach(callback => {
        if (callback.onConnectionStateChanged) {
          callback.onConnectionStateChanged(signalR.HubConnectionState.Disconnected);
        }
      });
    });



    
    
  }




  public async ensureConnection(): Promise<void> {
    if (!this.isInitialized) {
      this.initializeConnection();
    }

    if (!this.connection || this.isConnecting) return;

    if (this.connection.state === signalR.HubConnectionState.Connected) {
      return;
    }

    this.isConnecting = true;

    try {
      await this.connection.start();
      const currentConnectionId = this.connection.connectionId;

      console.log('🔗 WorkAgent SignalR Connected with Connection ID:', currentConnectionId);
      console.log('🏷️ User ID for sticky sessions:', this.getOrCreateUserId());

      // Check if connection ID changed (indicating server switch)
      if (this.lastConnectionId && this.lastConnectionId !== currentConnectionId) {
        console.warn('⚠️ CONNECTION ID CHANGED! Possible server switch detected:');
        console.warn(`   Previous: ${this.lastConnectionId}`);
        console.warn(`   Current:  ${currentConnectionId}`);
        console.warn('   This may cause execution context loss!');
      }

      this.lastConnectionId = currentConnectionId;

      this.activeCallbacks.forEach(callback => {
        if (callback.onConnectionStateChanged) {
          callback.onConnectionStateChanged(signalR.HubConnectionState.Connected);
        }
      });
    } catch (error) {
      console.error('WorkAgent SignalR Connection Error:', error);
      this.activeCallbacks.forEach(callback => {
        callback.onError(`Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      });
      throw error;
    } finally {
      this.isConnecting = false;
    }
  }

  public async disconnect(): Promise<void> {
    if (!this.connection) return;

    try {
      // Reset connection state
      this.isInitialized = false;

      // Reset workflow state on disconnect
      this.resetWorkflowState();

      await this.connection.stop();
      console.log('WorkAgent SignalR Disconnected');
      this.activeCallbacks.clear(); // Clear all callbacks on disconnect
    } catch (error) {
      console.error('WorkAgent SignalR Disconnect Error:', error);
    }
  }

  public async processPrompt(prompt: string, callbacks: WorkAgentCallbacks): Promise<void> {
    const threadId = localStorage.getItem("ThreadId") || "";
    const accountId = localStorage.getItem("AccountId") || "";
    const chatHistoryId = localStorage.getItem("chatHistoryId");
    const currentDomain = window.location.origin; // Get current domain URL

    // Reset workflow state when starting a new process
    this.resetWorkflowState();

    // Ensure connection is established
    await this.ensureConnection();

    if (!this.connection) {
      throw new Error('WorkAgent SignalR connection not initialized');
    }

    // Add callbacks to active set
    this.activeCallbacks.add(callbacks);

    try {
      console.log('Sending prompt to WorkAgentHub:', prompt);
      const dto = {
        message: prompt,
        threadId: threadId,
        accountId: accountId,
        chatHistoryId: chatHistoryId || null,
        domainUrl: currentDomain // Add domain URL to the DTO
      };
      // Send prompt to the WorkAgentHub using ProcessPrompt method
      await this.connection.invoke('ProcessPrompt', dto);

      console.log('Prompt sent successfully to WorkAgentHub');
    } catch (error) {
      console.error('Error sending prompt to WorkAgentHub:', error);
      // Remove callbacks on error
      this.activeCallbacks.delete(callbacks);

      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      callbacks.onError(`Failed to send prompt: ${errorMessage}`);
      throw error;
    }
  }


  // Method to add callbacks
  public addCallbacks(callbacks: WorkAgentCallbacks): void {
    this.activeCallbacks.add(callbacks);
  }

  // Method to remove callbacks when component unmounts
  public removeCallbacks(callbacks: WorkAgentCallbacks): void {
    this.activeCallbacks.delete(callbacks);
  }

  // Method to check if we should disconnect when no components are using the service
  public shouldDisconnectWhenIdle(): boolean {
    return this.activeCallbacks.size === 0;
  }

  // Method to force disconnect if no components are using the service
  public async disconnectIfIdle(): Promise<void> {
    if (this.shouldDisconnectWhenIdle() && this.connection) {
      console.log('WorkAgent SignalR: No active components, disconnecting...');
      await this.disconnect();
    }
  }

  public getConnectionState(): signalR.HubConnectionState {
    return this.connection?.state || signalR.HubConnectionState.Disconnected;
  }

  public isConnected(): boolean {
    return this.connection?.state === signalR.HubConnectionState.Connected;
  }

  public getActiveCallbacksCount(): number {
    return this.activeCallbacks.size;
  }

  // Method to check if we should keep the connection alive
  public hasActiveCallbacks(): boolean {
    return this.activeCallbacks.size > 0;
  }

  // Method to submit field values to WorkAgentHub
  public async submitFieldValues(fieldValues: Record<string, string>): Promise<void> {
    await this.ensureConnection();

    if (!this.connection || this.connection.state !== signalR.HubConnectionState.Connected) {
      throw new Error('WorkAgent SignalR connection not established');
    }

    try {
      await this.connection.invoke('SubmitFieldValues', fieldValues);
      console.log('Field values submitted to WorkAgentHub:', fieldValues);
    } catch (error) {
      console.error('Error submitting field values:', error);
      throw error;
    }
  }

  // Method to submit a single field value response
  public async submitFieldValue(fieldName: string, value: string): Promise<void> {
    const fieldValues = { [fieldName]: value };
    await this.submitFieldValues(fieldValues);
  }

  // Method to confirm submission (for future use with WorkAgentHub)
  public async confirmSubmission(confirmed: boolean): Promise<void> {
    await this.ensureConnection();

    if (!this.connection || this.connection.state !== signalR.HubConnectionState.Connected) {
      throw new Error('WorkAgent SignalR connection not established');
    }

    try {
      await this.connection.invoke('ConfirmSubmission', confirmed);
      console.log('Submission confirmation sent to WorkAgentHub');
    } catch (error) {
      console.error('Error confirming submission:', error);
      throw error;
    }
  }

  // Method to confirm navigation completion
  public async confirmNavigation(success: boolean, message?: string): Promise<void> {
    await this.ensureConnection();

    if (!this.connection || this.connection.state !== signalR.HubConnectionState.Connected) {
      throw new Error('WorkAgent SignalR connection not established');
    }

    try {
      await this.connection.invoke('ConfirmNavigation', { Success: success, Message: message || '' });
      console.log('Navigation confirmation sent to WorkAgentHub:', { success, message });
    } catch (error) {
      console.error('Error confirming navigation:', error);
      throw error;
    }
  }

  // Method to send DOM data to WorkAgentHub
  public async sendDomData(domData: Record<string, string>): Promise<void> {
    await this.ensureConnection();

    if (!this.connection || this.connection.state !== signalR.HubConnectionState.Connected) {
      throw new Error('WorkAgent SignalR connection not established');
    }

    try {
      await this.connection.invoke('ProcessDomData', domData);
      console.log('DOM data sent to WorkAgentHub:', domData);
    } catch (error) {
      console.error('Error sending DOM data:', error);
      throw error;
    }
  }

  // Method to send DOM data binding confirmation to WorkAgentHub
  public async confirmDomDataBinding(success: boolean, message?: string): Promise<void> {
    await this.ensureConnection();

    if (!this.connection || this.connection.state !== signalR.HubConnectionState.Connected) {
      throw new Error('WorkAgent SignalR connection not established');
    }

    try {
      await this.connection.invoke('ProcessDomDataBindingConfirmation', {
        Success: success,
        Message: message || ''
      });
      console.log('DOM data binding confirmation sent to WorkAgentHub:', { success, message });
    } catch (error) {
      console.error('Error sending DOM data binding confirmation:', error);
      throw error;
    }
  }

  // Method to check if both conditions are met and trigger confirmation dialog
  private checkAndTriggerConfirmation(): void {
    console.log('🔍 Checking confirmation conditions:', {
      formSubmittedSuccessfully: this.formSubmittedSuccessfully,
      workAgentCompleted: this.workAgentCompleted,
      hasPendingRequest: !!this.pendingConfirmationRequest
    });

    if (this.formSubmittedSuccessfully && this.workAgentCompleted && this.pendingConfirmationRequest) {
      console.log('✅ Both conditions met - triggering confirmation dialog');

      // Trigger the confirmation dialog
      this.activeCallbacks.forEach(callback => {
        if (callback.onRequestConfirmation) {
          callback.onRequestConfirmation(this.pendingConfirmationRequest!);
        }
      });

      // Reset the state after triggering
      this.resetConfirmationState();
    } else {
      console.log('⏳ Waiting for both conditions to be met before showing confirmation dialog');
    }
  }

  // Method to reset confirmation state
  private resetConfirmationState(): void {
    console.log('🔄 Resetting confirmation state');
    this.formSubmittedSuccessfully = false;
    this.workAgentCompleted = false;
    this.pendingConfirmationRequest = null;
  }

  // Public method to reset confirmation state (can be called when starting new workflow)
  public resetWorkflowState(): void {
    console.log('🔄 Resetting workflow state for new process');
    this.resetConfirmationState();
  }

  // Method to extract DOM data from the current page
 // Method to extract DOM data from the current page
private async extractDomData(selectorsFromBackend: Array<{
  Name: string;
  Selector: string;
}>): Promise<any[]> {
  console.log('🔍 Starting DOM data extraction...');

  this.allElementsData = [];

  try {
    const radioGroups = this.extractRadioGroupsData();
    const multiSelects = this.extractMultiSelectDropdowns();
    const formElements = this.findFormElements();

    this.allElementsData.push(...radioGroups, ...multiSelects);

    for (const element of formElements) {
      const elementInfo = this.extractElementInfo(element);
      if (elementInfo) {
        this.allElementsData.push(elementInfo);
      }
    }

    console.log('✅ DOM data extraction completed:', this.allElementsData.length);

    // Step 2: Match extracted data to backend selectors and update values
    const updatedSelectors = selectorsFromBackend.map(backendSelector => {
      const match = this.allElementsData.find(domData =>
        domData.labelName?.trim() === backendSelector.Name?.trim() ||
        domData.xpath === backendSelector.Selector
      );

      if (match) {
        return {
          ...backendSelector,
          Value: match.value
        };
      }
      return backendSelector;
    });

    return updatedSelectors;
  } catch (error) {
    console.error('❌ Error during DOM data extraction:', error);
    throw error;
  }
}


private findLabelForElement(element: HTMLElement): string | null {
  // 1. Match <label for="...">
  if (element.id) {
    const label = document.querySelector(`label[for="${element.id}"]`);
    if (label) {
      return label.textContent?.trim() || null;
    }
  }

  // 2. Check if element is inside a label
  const parentLabel = element.closest('label');
  if (parentLabel) {
    return parentLabel.textContent?.trim() || null;
  }

  // 3. Try to find a preceding sibling label (in same container)
  const container = element.closest('div, td, tr, section');
  if (container) {
    const possibleLabels = container.querySelectorAll('label');
    for (const label of Array.from(possibleLabels)) {
      if (label.contains(element)) continue; // skip labels wrapping this element
      return label.textContent?.trim() || null;
    }
  }

  return null;
}

  // Method to create BindingDomXpath data by using the provided array directly
  private createBindingDomXpathData(bindingRequestData: Array<{
    xpath: string;
    name: string;
    type: string;
    labelName: string;
    cssSelector: string;
    selector: string;
    value: any;
  }>): Array<{
    labelName: string;
    name: string;
    cssSelector: string;
    selector: string;
    value: any;
    xpath: string;
    type: string;
    key: string;
  }> {
    const bindingDomXpathData: Array<{
      labelName: string;
       name: string;
      cssSelector: string;
      selector: string;
      value: any;
      xpath: string;
      type: string;
      key: string;
    }> = [];

    console.log('🔍 Starting BindingDomXpath mapping...');
    console.log('📋 Binding request data:', bindingRequestData);

    // Since the data is already in the correct format with xpath, labelName, type, and value,
    // we just need to transform it to include the key field
    for (const bindingItem of bindingRequestData) {
      console.log(`🔍 Processing binding item:`, bindingItem);

      bindingDomXpathData.push({
        labelName: bindingItem.labelName,
         name: bindingItem.name,
        cssSelector: bindingItem.cssSelector,
        selector: bindingItem.selector,
           value: bindingItem.value,
        xpath: bindingItem.xpath,
        type: bindingItem.type,
        key: bindingItem.labelName // Set key to match the labelName
      });
    }

    console.log('✅ BindingDomXpath mapping completed. Total mapped fields:', bindingDomXpathData.length);
    console.log('📊 Final BindingDomXpath data:', bindingDomXpathData);
    return bindingDomXpathData;
  }

  // Method to find all form elements on the page
private findFormElements(): HTMLElement[] {
  const elements: HTMLElement[] = [];
  const processedElements = new Set<HTMLElement>();

  const selectors = [
    'input[type="text"]',
    'input[type="email"]',
    'input[type="password"]',
    'input[type="number"]',
    'input[type="tel"]',
    'input[type="url"]',
    'input[type="search"]',
    'input[type="date"]',
    'input[type="datetime-local"]',
    'input[type="time"]',
    'input[type="week"]',
    'input[type="month"]',
    'input[type="range"]',
    'input[type="color"]',
    'input[type="checkbox"]',
    'textarea',
    'select:not([multiple])', // only include single-select dropdowns here
    'input[type="file"]',
    '[contenteditable="true"]',
    'input[id*="date" i]',
    'input[class*="date" i]',
    'input[placeholder*="date" i]',
    '[role="combobox"]',
    '[role="listbox"]',
    '.dropdown',
   '.select',
    // 'ejs-multiselect' ⛔️ removed
    'ejs-dropdownlist',
    'ejs-combobox'
  ];

  selectors.forEach(selector => {
    try {
      const foundElements = document.querySelectorAll(selector);
      foundElements.forEach(el => {
        const htmlEl = el as HTMLElement;

        if (processedElements.has(htmlEl)) return;

        const parentDropdown = htmlEl.closest(
          'ejs-dropdownlist, ejs-combobox, [role="combobox"], .dropdown, .select'
        );

        if (parentDropdown && parentDropdown !== htmlEl) {
          if (
            !processedElements.has(parentDropdown as HTMLElement) &&
            this.isElementVisible(parentDropdown as HTMLElement)
          ) {
            processedElements.add(parentDropdown as HTMLElement);
            elements.push(parentDropdown as HTMLElement);
            console.log(`🔽 Found dropdown container: ${(parentDropdown as HTMLElement).tagName} with type 'dropdown'`);
          }

          processedElements.add(htmlEl);
          return;
        }

        if (this.isElementVisible(htmlEl) && !processedElements.has(htmlEl)) {
          processedElements.add(htmlEl);
          elements.push(htmlEl);
        }
      });
    } catch (error) {
      console.warn(`Error with selector ${selector}:`, error);
    }
  });

  console.log(`🔍 Found ${elements.length} form elements (after deduplication)`);
  return elements;
}






  // Method to extract all data from a single element using the interface
  // This method returns RAW data without any business logic filtering
  private extractElementInfo(element: HTMLElement): ElementExtractionData | null {
    try {
      const xpath = this.generateXPath(element);
      const labelName = this.findElementLabel(element);
      const type = this.getElementType(element);
      const value = this.getElementValueRaw(element); // Use raw value extraction
      const key = this.generateElementKey(element, labelName);

     

      return {
        xpath,
        labelName,
        type,
        value,
        key
      };
    } catch (error) {
      console.warn('Error extracting element info:', error);
      return null;
    }
  }

  // Method to extract data from a single element
  private extractElementData(element: HTMLElement): { key: string; value: string; labelName: string; xpath: string; type: string } | null {
    try {
      const elementInfo = this.extractElementInfo(element);
      if (!elementInfo) {
        return null;
      }

      const { xpath, labelName, type, key } = elementInfo;

      // Get the PROCESSED value (with business logic filtering)
      const processedValue = this.getElementValue(element);

      // Special handling for radio buttons - include ALL radio buttons (selected and unselected)
      if (type === 'radio') {
        let inputElement: HTMLInputElement | null = null;
        let groupName = '';

        // Handle both standard radio buttons and ejs-radiobutton components
        if (element.tagName.toLowerCase() === 'input' && (element as HTMLInputElement).type === 'radio') {
          inputElement = element as HTMLInputElement;
          groupName = inputElement.name;
        } else if (element.tagName.toLowerCase() === 'ejs-radiobutton' || element.closest('ejs-radiobutton')) {
          const ejsRadioButton = element.tagName.toLowerCase() === 'ejs-radiobutton'
            ? element
            : element.closest('ejs-radiobutton');
          if (ejsRadioButton) {
            inputElement = ejsRadioButton.querySelector('input[type="radio"]') as HTMLInputElement;
            if (inputElement) {
              groupName = inputElement.name;
            }
          }
        }

        if (inputElement && groupName) {
          const checkedRadio = document.querySelector(`input[type="radio"][name="${groupName}"]:checked`) as HTMLInputElement;

          // Check if this element represents the checked radio button
          const isThisElementChecked = (inputElement === checkedRadio) ||
            (element.tagName.toLowerCase() === 'ejs-radiobutton' && element.contains(checkedRadio));

          let radioValue = '';
          if (isThisElementChecked && checkedRadio) {
            // Get the actual value of the checked radio button using processed value
            radioValue = processedValue || checkedRadio.value || checkedRadio.getAttribute('value') || 'true';
          }
          // For unselected radio buttons, radioValue remains empty string

          // Get a meaningful label for the radio group or individual radio button
          const radioGroupLabel = this.findRadioGroupLabel(groupName) || labelName || groupName;
console.log(groupName,"groupName");

          return {
            key: `${groupName}`,
            value: radioValue,
            labelName: radioGroupLabel,
            xpath,
            type
          };
        }

        // Fallback for radio buttons without proper group name
        return {
          key,
          value: '',
          labelName,
          xpath,
          type
        };
      }

      // For other elements, include ALL elements (even with empty values)
      // Convert null/undefined to empty string for consistency
      const finalValue = processedValue !== null && processedValue !== undefined ? String(processedValue) : '';

      return {
        key,
        value: finalValue,
        labelName,
        xpath,
        type
      };
    } catch (error) {
      console.warn('Error extracting data from element:', error);
      return null;
    }
  }

  // Method to find the label for an element
  private findElementLabel(element: HTMLElement): string {
    // Special handling for ejs-radiobutton components
    if (element.tagName.toLowerCase() === 'ejs-radiobutton' || element.closest('ejs-radiobutton')) {
      return this.findEjsRadioButtonLabel(element);
    }

    // Special handling for standard radio buttons
    if (element.tagName.toLowerCase() === 'input' && (element as HTMLInputElement).type === 'radio') {
      return this.findRadioButtonLabel(element as HTMLInputElement);
    }

    // Special handling for ejs-dropdownlist components
    if (element.tagName.toLowerCase() === 'ejs-dropdownlist' || element.closest('ejs-dropdownlist')) {
      return this.findEjsDropdownLabel(element);
    }

    // Try different methods to find the label

    // 1. Check for explicit label element
    if (element.id) {
      const label = document.querySelector(`label[for="${element.id}"]`);
      if (label && label.textContent) {
        return label.textContent.trim();
      }
    }

    // 2. Check if element is inside a label
    const parentLabel = element.closest('label');
    if (parentLabel && parentLabel.textContent) {
      return parentLabel.textContent.trim();
    }

    // 3. Check for aria-label
    if (element.getAttribute('aria-label')) {
      return element.getAttribute('aria-label')!.trim();
    }

    // 4. Check for aria-labelledby
    const labelledBy = element.getAttribute('aria-labelledby');
    if (labelledBy) {
      const labelElement = document.getElementById(labelledBy);
      if (labelElement && labelElement.textContent) {
        return labelElement.textContent.trim();
      }
    }

    // 5. Check for placeholder
    if (element.getAttribute('placeholder')) {
      return element.getAttribute('placeholder')!.trim();
    }

    // 6. Check for title
    if (element.getAttribute('title')) {
      return element.getAttribute('title')!.trim();
    }

    // 7. Check for name attribute
    if (element.getAttribute('name')) {
      return element.getAttribute('name')!.trim();
    }

    // 8. Look for nearby text (previous sibling, parent, etc.)
    const nearbyText = this.findNearbyText(element);
    if (nearbyText && this.isValidLabelText(nearbyText)) {
      return nearbyText;
    }

    // 9. Use element type and id/class as fallback
    const id = element.id || '';
    const className = element.className || '';
    const type = this.getElementType(element);

    // Try to extract meaningful text from id or class
    let fallbackLabel = '';
    if (id) {
      fallbackLabel = id.replace(/[_-]/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    } else if (className) {
      fallbackLabel = className.split(' ')[0].replace(/[_-]/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }

    if (fallbackLabel && this.isValidLabelText(fallbackLabel)) {
      return fallbackLabel;
    }

    return `${type}_${id || className || 'unknown'}`.replace(/\s+/g, '_');
  }

  // Method to find label for ejs-radiobutton components
  private findEjsRadioButtonLabel(element: HTMLElement): string {
    // Find the ejs-radiobutton element if we're dealing with a child element
    const ejsRadioButton = element.tagName.toLowerCase() === 'ejs-radiobutton'
      ? element
      : element.closest('ejs-radiobutton');

    if (!ejsRadioButton) {
      return 'radio';
    }

    // Find the input element inside the ejs-radiobutton to get the group name
    const radioInput = ejsRadioButton.querySelector('input[type="radio"]') as HTMLInputElement;
    if (!radioInput) {
      return 'radio';
    }

    const groupName = radioInput.name;
    if (!groupName) {
      return 'radio';
    }

    // Use the group name as the label (e.g., "Oneway or Round Trip")
    // Clean up the group name to make it more readable
    let groupLabel = groupName;

    // Convert common patterns to more readable format
    groupLabel = groupLabel.replace(/([a-z])([A-Z])/g, '$1 $2'); // camelCase to spaces
    groupLabel = groupLabel.replace(/[_-]/g, ' '); // underscores and dashes to spaces
    groupLabel = groupLabel.replace(/\s+/g, ' ').trim(); // normalize spaces

    // Capitalize first letter of each word
    groupLabel = groupLabel.replace(/\b\w/g, l => l.toUpperCase());

    if (this.isValidLabelText(groupLabel)) {
      return groupLabel;
    }

    // Fallback: try to find a label using the radio group label method
    const radioGroupLabel = this.findRadioGroupLabel(groupName);
    if (radioGroupLabel && this.isValidLabelText(radioGroupLabel)) {
      return radioGroupLabel;
    }

    // Final fallback
    return groupName || 'radio';
  }

  // Method to find label for radio buttons (special handling)
  private findRadioButtonLabel(radioElement: HTMLInputElement): string {
    const groupName = radioElement.name;

    // 1. Try to find a fieldset legend for the radio group
    const fieldset = radioElement.closest('fieldset');
    if (fieldset) {
      const legend = fieldset.querySelector('legend');
      if (legend && legend.textContent) {
        return legend.textContent.trim();
      }
    }

    // 2. Look for a common parent with a label-like element
    const radioGroup = document.querySelectorAll(`input[type="radio"][name="${groupName}"]`);
    if (radioGroup.length > 1) {
      // Find common ancestor
      let commonParent = radioElement.parentElement;
      while (commonParent) {
        const hasAllRadios = Array.from(radioGroup).every(radio =>
          commonParent!.contains(radio)
        );
        if (hasAllRadios) {
          // Look for label-like text in this parent
          const labelText = this.findGroupLabel(commonParent);
          if (labelText) {
            return labelText;
          }
        }
        commonParent = commonParent.parentElement;
      }
    }

    // 3. Fall back to individual radio button label
    return this.findIndividualRadioLabel(radioElement);
  }

  // Method to find a meaningful label for a radio button group by name
  private findRadioGroupLabel(groupName: string): string {
    // Find all radio buttons in this group
    const radioButtons = document.querySelectorAll(`input[type="radio"][name="${groupName}"]`);
    if (radioButtons.length === 0) {
      return '';
    }

    // Try to find a common parent container for all radio buttons in the group
    let commonParent: HTMLElement | null = null;
    const firstRadio = radioButtons[0] as HTMLElement;

    // Start with the parent of the first radio button and work up
    let currentParent = firstRadio.parentElement;
    while (currentParent) {
      const hasAllRadios = Array.from(radioButtons).every(radio =>
        currentParent!.contains(radio)
      );
      if (hasAllRadios) {
        commonParent = currentParent;
        break;
      }
      currentParent = currentParent.parentElement;
    }

    if (commonParent) {
      // Look for a fieldset legend first
      const fieldset = commonParent.closest('fieldset') || commonParent.querySelector('fieldset');
      if (fieldset) {
        const legend = fieldset.querySelector('legend');
        if (legend && legend.textContent) {
          const legendText = legend.textContent.trim();
          if (this.isValidLabelText(legendText)) {
            return legendText;
          }
        }
      }

      // Look for label-like elements in the common parent
      const groupLabel = this.findGroupLabel(commonParent);
      if (groupLabel && this.isValidLabelText(groupLabel)) {
        return groupLabel;
      }
    }

    // Fallback: try to get label from the first radio button
    const firstRadioLabel = this.findIndividualRadioLabel(firstRadio as HTMLInputElement);
    if (firstRadioLabel && this.isValidLabelText(firstRadioLabel)) {
      // If it's a specific radio label, try to generalize it
      return this.generalizeRadioLabel(firstRadioLabel);
    }

    return groupName;
  }

  // Method to generalize a specific radio button label to a group label
  private generalizeRadioLabel(specificLabel: string): string {
    // Remove common radio-specific words to get the general category
    const generalizedLabel = specificLabel
      .replace(/\b(male|female|yes|no|true|false|on|off|selected|checked)\b/gi, '')
      .replace(/\s+/g, ' ')
      .trim();

    if (generalizedLabel && this.isValidLabelText(generalizedLabel)) {
      return generalizedLabel;
    }

    return specificLabel;
  }

  // Method to find group label for radio buttons
  private findGroupLabel(container: HTMLElement): string {
    // Look for elements that might contain the group label
    const labelSelectors = [
      'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
      '.label', '.form-label', '.field-label',
      'label', 'span', 'div'
    ];

    for (const selector of labelSelectors) {
      const elements = container.querySelectorAll(selector);
      for (let i = 0; i < elements.length; i++) {
        const element = elements[i];
        const text = element.textContent?.trim();
        if (text && text.length > 0 && text.length < 100) {
          // Check if this element doesn't contain radio buttons (likely a label)
          const hasRadios = element.querySelectorAll('input[type="radio"]').length > 0;
          if (!hasRadios) {
            return text;
          }
        }
      }
    }

    return '';
  }

  // Method to find individual radio button label
  private findIndividualRadioLabel(radioElement: HTMLInputElement): string {
    // Check for explicit label
    if (radioElement.id) {
      const label = document.querySelector(`label[for="${radioElement.id}"]`);
      if (label && label.textContent) {
        return label.textContent.trim();
      }
    }

    // Check if inside a label
    const parentLabel = radioElement.closest('label');
    if (parentLabel && parentLabel.textContent) {
      return parentLabel.textContent.trim();
    }

    // Look for nearby text
    const nearbyText = this.findNearbyText(radioElement);
    if (nearbyText) {
      return nearbyText;
    }

    // Use name as fallback
    return radioElement.name || 'radio_unknown';
  }

  // Method to find label for ejs-dropdownlist components
  private findEjsDropdownLabel(element: HTMLElement): string {
    // Find the ejs-dropdownlist element if we're dealing with a child element
    const ejsDropdown = element.tagName.toLowerCase() === 'ejs-dropdownlist'
      ? element
      : element.closest('ejs-dropdownlist');

    if (!ejsDropdown) {
      return 'dropdownlist';
    }

    // 1. Check for explicit label element using the dropdown's ID
    const dropdownId = ejsDropdown.id;
    if (dropdownId) {
      const label = document.querySelector(`label[for="${dropdownId}"]`);
      if (label && label.textContent) {
        const labelText = label.textContent.trim().replace(/\s*\*\s*$/, ''); // Remove trailing asterisk
        if (this.isValidLabelText(labelText)) {
          return labelText;
        }
      }
    }

    // 2. Check if dropdown is inside a label
    const parentLabel = ejsDropdown.closest('label');
    if (parentLabel && parentLabel.textContent) {
      const labelText = parentLabel.textContent.trim().replace(/\s*\*\s*$/, '');
      if (this.isValidLabelText(labelText)) {
        return labelText;
      }
    }

    // 3. Check for aria-label
    const ariaLabel = ejsDropdown.getAttribute('aria-label');
    if (ariaLabel) {
      const labelText = ariaLabel.trim().replace(/\s*\*\s*$/, '');
      if (this.isValidLabelText(labelText)) {
        return labelText;
      }
    }

    // 4. Check for aria-labelledby
    const labelledBy = ejsDropdown.getAttribute('aria-labelledby');
    if (labelledBy) {
      const labelElement = document.getElementById(labelledBy);
      if (labelElement && labelElement.textContent) {
        const labelText = labelElement.textContent.trim().replace(/\s*\*\s*$/, '');
        if (this.isValidLabelText(labelText)) {
          return labelText;
        }
      }
    }

    // 5. Look for nearby text elements that might be labels
    const nearbyLabel = this.findNearbyDropdownLabel(ejsDropdown as HTMLElement);
    if (nearbyLabel && this.isValidLabelText(nearbyLabel)) {
      return nearbyLabel;
    }

    // 6. Check for placeholder or title attributes
    const placeholder = ejsDropdown.getAttribute('placeholder') || ejsDropdown.getAttribute('data-placeholder');
    if (placeholder) {
      const labelText = placeholder.trim().replace(/\s*\*\s*$/, '');
      if (this.isValidLabelText(labelText)) {
        return labelText;
      }
    }

    const title = ejsDropdown.getAttribute('title');
    if (title) {
      const labelText = title.trim().replace(/\s*\*\s*$/, '');
      if (this.isValidLabelText(labelText)) {
        return labelText;
      }
    }

    // 7. Use ID as fallback, but clean it up
    if (dropdownId) {
      const cleanedId = dropdownId.replace(/dropdown|list|select/gi, '').trim();
      if (cleanedId && this.isValidLabelText(cleanedId)) {
        return cleanedId;
      }
    }

    return 'dropdown';
  }


  // Method to validate if a text is a good label (not just numbers or meaningless text)
  private isValidLabelText(text: string): boolean {
    if (!text || text.trim().length === 0) {
      return false;
    }

    const trimmedText = text.trim();

    // Reject if it's just a number
    if (/^\d+$/.test(trimmedText)) {
      return false;
    }

    // Reject if it's too short (less than 2 characters) unless it's a meaningful abbreviation
    if (trimmedText.length < 2) {
      return false;
    }

    // Reject common meaningless patterns
    const meaninglessPatterns = [
      /^(div|span|input|select|option|label)$/i,
      /^(element|field|control|widget)$/i,
      /^(undefined|null|empty)$/i,
      /^[^a-zA-Z]*$/  // Only special characters or numbers
    ];

    for (const pattern of meaninglessPatterns) {
      if (pattern.test(trimmedText)) {
        return false;
      }
    }

    // Accept if it contains at least one letter and seems like a meaningful label
    return /[a-zA-Z]/.test(trimmedText) && trimmedText.length >= 2;
  }

  // Method to find nearby label for dropdown components
  private findNearbyDropdownLabel(element: HTMLElement): string {
    // Look for label-like elements near the dropdown
    const labelSelectors = [
      'label', '.label', '.form-label', '.field-label',
      '.control-label', 'span', 'div', 'p'
    ];

    // Check previous siblings
    let sibling = element.previousElementSibling;
    while (sibling) {
      for (const selector of labelSelectors) {
        if (sibling.matches && sibling.matches(selector)) {
          const text = sibling.textContent?.trim();
          if (text && text.length > 0 && text.length < 100) {
            const cleanedText = text.replace(/\s*\*\s*$/, ''); // Remove trailing asterisk
            if (this.isValidLabelText(cleanedText)) {
              return cleanedText;
            }
          }
        }
      }
      sibling = sibling.previousElementSibling;
    }

    // Check parent container for label text
    const parent = element.parentElement;
    if (parent) {
      // Look for direct text content in parent (excluding the dropdown itself)
      const parentText = parent.textContent?.trim() || '';
      const elementText = element.textContent?.trim() || '';
      let labelText = parentText.replace(elementText, '').trim();

      // Clean up common patterns
      labelText = labelText.replace(/\s*\*\s*$/, ''); // Remove trailing asterisk
      labelText = labelText.replace(/^\s*:\s*|\s*:\s*$/g, ''); // Remove colons

      if (labelText && labelText.length > 0 && labelText.length < 100 && this.isValidLabelText(labelText)) {
        return labelText;
      }

      // Look for label elements within the parent
      for (const selector of labelSelectors) {
        const labelElement = parent.querySelector(selector);
        if (labelElement && labelElement !== element && !element.contains(labelElement)) {
          const text = labelElement.textContent?.trim();
          if (text && text.length > 0 && text.length < 100) {
            const cleanedText = text.replace(/\s*\*\s*$/, '');
            if (this.isValidLabelText(cleanedText)) {
              return cleanedText;
            }
          }
        }
      }
    }

    return '';
  }

  // Method to find nearby text that might serve as a label
  private findNearbyText(element: HTMLElement): string {
    // Check previous sibling elements for text
    let sibling = element.previousElementSibling;
    while (sibling) {
      const text = sibling.textContent?.trim();
      if (text && text.length > 0 && text.length < 100 && this.isValidLabelText(text)) {
        return text;
      }
      sibling = sibling.previousElementSibling;
    }

    // Check next sibling elements for text (sometimes labels come after)
    sibling = element.nextElementSibling;
    while (sibling) {
      const text = sibling.textContent?.trim();
      if (text && text.length > 0 && text.length < 100 && this.isValidLabelText(text)) {
        return text;
      }
      sibling = sibling.nextElementSibling;
    }

    // Check parent element's text (excluding the element itself)
    const parent = element.parentElement;
    if (parent) {
      const parentText = parent.textContent?.trim() || '';
      const elementText = element.textContent?.trim() || '';
      let labelText = parentText.replace(elementText, '').trim();

      // Clean up common patterns
      labelText = labelText.replace(/^\s*:\s*|\s*:\s*$/g, ''); // Remove colons
      labelText = labelText.replace(/\s*\*\s*$/g, ''); // Remove asterisks

      if (labelText && labelText.length > 0 && labelText.length < 100 && this.isValidLabelText(labelText)) {
        return labelText;
      }
    }

    return '';
  }

private findCommonAncestor(a: HTMLElement, b: HTMLElement): HTMLElement | null {
  const aAncestors = [];
  let current: HTMLElement | null = a;
  while (current) {
    aAncestors.push(current);
    current = current.parentElement;
  }

  current = b;
  while (current) {
    if (aAncestors.includes(current)) return current;
    current = current.parentElement;
  }

  return null;
}

private extractRadioGroupsData(): ElementExtractionData[] {
  const radioGroupsMap = new Map<string, HTMLInputElement[]>();
  const results: ElementExtractionData[] = [];

  const radios = Array.from(document.querySelectorAll('input[type="radio"]'));

  // Group radios by name
  for (const radio of radios) {
    const name = radio.getAttribute('name')?.trim();
    if (!name) continue;

    if (!radioGroupsMap.has(name)) {
      radioGroupsMap.set(name, []);
    }
    radioGroupsMap.get(name)!.push(radio as HTMLInputElement);
  }

  for (const [groupName, groupRadios] of Array.from(radioGroupsMap.entries())) {
    // 1. Get selected radio (if any)
    const selectedRadio = groupRadios.find(r => r.checked);
    const selectedValue = selectedRadio?.value || '';

    // 2. Find common container of all radios in group
    let commonContainer: HTMLElement = groupRadios[0];
    for (const el of groupRadios) {
      const candidate = this.findCommonAncestor(commonContainer, el);
      if (candidate) {
        commonContainer = candidate;
      }
    }

    // 3. Get XPath for group container
    const xpath = this.generateXPath(commonContainer);

    // 4. Get label for the group
    const label = this.findRadioGroupLabel(groupName) || groupName;

    // 5. Push final result
    results.push({
      xpath,
      value: selectedValue,
      type: 'radio',
      labelName: groupName,
      key: groupName
    });
  }

  return results;
}


private extractMultiSelectDropdowns(): ElementExtractionData[] {
  const results: ElementExtractionData[] = [];
  const seenXPaths = new Set<string>();

  // Native <select multiple>
  const selectElements = Array.from(document.querySelectorAll('select[multiple]')) as HTMLSelectElement[];

  selectElements.forEach(select => {
    const selectedOptions = Array.from(select.selectedOptions);
    const value = selectedOptions.map(opt => opt.value || opt.text).join(', ');
    const xpath = this.generateXPath(select);

    if (seenXPaths.has(xpath)) return;

    const label = this.findLabelForElement(select) || select.name || 'Unnamed MultiSelect';

    results.push({
      xpath,
      value,
      labelName: label,
      type: 'multiselect',
      key: select.name || label.toLowerCase().replace(/[^a-z0-9]/gi, '_')
    });

    seenXPaths.add(xpath);
  });

  // // Syncfusion ejs-multiselect
  // const ejsMultiselects = Array.from(document.querySelectorAll('ejs-multiselect')) as HTMLElement[];

  // ejsMultiselects.forEach(container => {
  //   // 👇 Find the clickable dropdown trigger span
  //   const clickable = container.querySelector('div > div > span') as HTMLElement | null;

  //   if (!clickable) return;

  //   const xpath = this.generateXPath(clickable); // 👈 Use clickable span XPath
  //   if (seenXPaths.has(xpath)) return;

  //   const value = this.getEjsDropdownValue(container) || '';
  //   const label = this.findLabelForElement(container) || container.getAttribute('name') || 'Unnamed Select';

  //   results.push({
  //     xpath,
  //     value,
  //     labelName: label,
  //     type: 'select',
  //     key: container.getAttribute('name') || label.toLowerCase().replace(/[^a-z0-9]/gi, '_')
  //   });

  //   seenXPaths.add(xpath);
  // });

  return results;
}


private generateXPath(element: HTMLElement): string {
  const tagName = element.tagName.toLowerCase();

  // ✅ ejs-multiselect logic
  if (tagName === 'ejs-multiselect') {
    if (element.id) {
      return `//*[@id="${element.id}"]`;
    }

    const input = element.querySelector('input');
    if (input && input.id) {
      return `//*[@id="${input.id}"]/ancestor::ejs-multiselect[1]`;
    }

    const dataId = element.getAttribute('data-id');
    if (dataId) {
      return `//ejs-multiselect[@data-id="${dataId}"]`;
    }

    const className = element.className;
    if (className) {
      const uniqueClasses = className
        .split(' ')
        .filter(cls => cls && !cls.startsWith('e-') && cls.length > 2);
      if (uniqueClasses.length > 0) {
        return `//ejs-multiselect[contains(@class, "${uniqueClasses[0]}")]`;
      }
    }

    return this.generatePositionalXPath(element);
  }

  // ✅ ejs-dropdownlist logic (updated)
  const dropdownContainer = tagName === 'ejs-dropdownlist'
    ? element
    : element.closest('ejs-dropdownlist');

  if (dropdownContainer instanceof HTMLElement) {
    const dropdownId = dropdownContainer.id;
    const input = dropdownContainer.querySelector('input');

    if (dropdownId && input) {
      // Construct full path from dropdown -> input
      return `//ejs-dropdownlist[@id="${dropdownId}"]/div/input`;
    }

    // fallback
    return this.generatePositionalXPath(dropdownContainer);
  }

  // ✅ default: use ID if available
  if (element.id) {
    return `//*[@id="${element.id}"]`;
  }

  // ✅ fallback: positional
  return this.generatePositionalXPath(element);
}

  private generatePositionalXPath(element: HTMLElement): string {
    const path: string[] = [];
    let current: Element | null = element;

    while (current && current.nodeType === Node.ELEMENT_NODE) {
      let selector = current.nodeName.toLowerCase();

      if (current.id) {
        selector += `[@id="${current.id}"]`;
        path.unshift(selector);
        break;
      } else {
        // Find the position among siblings of the same tag
        let sibling = current.previousElementSibling;
        let position = 1;
        while (sibling) {
          if (sibling.nodeName.toLowerCase() === selector) {
            position++;
          }
          sibling = sibling.previousElementSibling;
        }

        if (position > 1) {
          selector += `[${position}]`;
        }

        path.unshift(selector);
      }

      current = current.parentElement;
    }

    return '//' + path.join('/');
  }


  // Method to get the type of an element
  private getElementType(element: HTMLElement): string {
    const tagName = element.tagName.toLowerCase();

    // Check for ejs-radiobutton first
    if (tagName === 'ejs-radiobutton' || element.closest('ejs-radiobutton')) {
      return 'radio';
    }

    if (tagName === 'input') {
      const inputElement = element as HTMLInputElement;
      const type = inputElement.type || 'text';

      // Special handling for date range inputs - CHECK THIS FIRST before dropdown detection
      if (type === 'text' && (
        element.id.toLowerCase().includes('date') ||
        element.className.toLowerCase().includes('date') ||
        (element.getAttribute('placeholder') || '').toLowerCase().includes('date')
      )) {
        return 'daterange';
      }

      // Check if this input is inside a dropdown component - if so, return 'dropdown'
      if (element.closest('ejs-dropdownlist, ejs-combobox, ejs-multiselect, [role="combobox"]')) {
        return 'dropdown';
      }

      return type;
    }

    // Special handling for ejs-dropdownlist components - ALWAYS return 'dropdown'
    if (tagName === 'ejs-dropdownlist' || element.closest('ejs-dropdownlist')) {
      return 'dropdown';
    }

    if (tagName === 'select') {
      const selectElement = element as HTMLSelectElement;
      return selectElement.multiple ? 'multiselect' : 'dropdown';
    }

    if (tagName === 'textarea') {
      return 'textarea';
    }

    if (element.contentEditable === 'true') {
      return 'contenteditable';
    }

    // Check for custom dropdown/select components
    if (element.getAttribute('role') === 'combobox' ||
        element.getAttribute('role') === 'listbox' ||
        element.className.includes('dropdown') ||
        element.className.includes('select')) {
      return 'dropdown';
    }

    return tagName;
  }

  // Method to get the RAW value of an element (no business logic filtering)
  private getElementValueRaw(element: HTMLElement): string | null {
    const tagName = element.tagName.toLowerCase();

    // Handle ejs-radiobutton components
    if (tagName === 'ejs-radiobutton' || element.closest('ejs-radiobutton')) {
      return this.getEjsRadioButtonValueRaw(element);
    }

    if (tagName === 'input') {
      const inputElement = element as HTMLInputElement;

      // Special handling for date inputs - CHECK THIS FIRST before dropdown detection
      const type = inputElement.type || 'text';
      if (type === 'text' && (
        element.id.toLowerCase().includes('date') ||
        element.className.toLowerCase().includes('date') ||
        (element.getAttribute('placeholder') || '').toLowerCase().includes('date')
      )) {
        return inputElement.value || '';
      }

      // If this input is inside a dropdown, get the dropdown value instead
      if (element.closest('ejs-dropdownlist, ejs-combobox, [role="combobox"]')) {
        return this.getEjsDropdownValue(element);
      }

      if (inputElement.type === 'checkbox') {
        return inputElement.checked ? (inputElement.value || 'true') : 'false';
      }

      if (inputElement.type === 'radio') {
        // For RAW extraction, return THIS specific radio button's value (not the selected one from group)
        return inputElement.value || inputElement.getAttribute('value') || '';
      }

      return inputElement.value || '';
    }

    // Special handling for ejs-dropdownlist components
    if (tagName === 'ejs-dropdownlist' || element.closest('ejs-dropdownlist')) {
      return this.getEjsDropdownValue(element);
    }

    if (tagName === 'select') {
      const selectElement = element as HTMLSelectElement;

      if (selectElement.multiple) {
        // Multi-select: return selected options as comma-separated values
        const selectedOptions = Array.from(selectElement.selectedOptions);
        return selectedOptions.map(option => option.value || option.text).join(', ');
      } else {
        // Single select: return selected option VALUE first, then text as fallback
        const selectedOption = selectElement.selectedOptions[0];
        if (selectedOption) {
          // Prioritize value over text to get "PL" instead of "PL Leave Type"
          return selectedOption.value || selectedOption.text || '';
        }
        return '';
      }
    }

    if (tagName === 'textarea') {
      const textareaElement = element as HTMLTextAreaElement;
      return textareaElement.value || '';
    }

    if (element.contentEditable === 'true') {
      return element.textContent || element.innerText || '';
    }

    // For other custom components, try to get value from common attributes
    const value = element.getAttribute('value') ||
                  element.getAttribute('data-value') ||
                  element.textContent ||
                  element.innerText;

    return value ? value.trim() : '';
  }

  // Method to get the PROCESSED value of an element (with business logic filtering)
  private getElementValue(element: HTMLElement): string | null {
    const tagName = element.tagName.toLowerCase();

    // Handle ejs-radiobutton components
    if (tagName === 'ejs-radiobutton' || element.closest('ejs-radiobutton')) {
      return this.getEjsRadioButtonValue(element);
    }

    if (tagName === 'input') {
      const inputElement = element as HTMLInputElement;

      // Special handling for date inputs - CHECK THIS FIRST before dropdown detection
      const type = inputElement.type || 'text';
      if (type === 'text' && (
        element.id.toLowerCase().includes('date') ||
        element.className.toLowerCase().includes('date') ||
        (element.getAttribute('placeholder') || '').toLowerCase().includes('date')
      )) {
        return inputElement.value || '';
      }

      // If this input is inside a dropdown, get the dropdown value instead
      if (element.closest('ejs-dropdownlist, ejs-combobox, ejs-multiselect, [role="combobox"]')) {
        return this.getEjsDropdownValue(element);
      }

      if (inputElement.type === 'checkbox') {
        return inputElement.checked ? (inputElement.value || 'true') : 'false';
      }

      if (inputElement.type === 'radio') {
        // Return the value of THIS specific radio button (not the currently selected one from the group)
        // This allows targeting specific radio buttons for interaction
        return inputElement.value || inputElement.getAttribute('value') || '';
      }

      return inputElement.value || '';
    }

    // Special handling for ejs-dropdownlist components
    if (tagName === 'ejs-dropdownlist' || element.closest('ejs-dropdownlist')) {
      return this.getEjsDropdownValue(element);
    }

    if (tagName === 'select') {
      const selectElement = element as HTMLSelectElement;

      if (selectElement.multiple) {
        // Multi-select: return selected options as comma-separated values
        const selectedOptions = Array.from(selectElement.selectedOptions);
        return selectedOptions.map(option => option.value || option.text).join(', ');
      } else {
        // Single select: return selected option VALUE first, then text as fallback
        const selectedOption = selectElement.selectedOptions[0];
        if (selectedOption) {
          // Prioritize value over text to get "PL" instead of "PL Leave Type"
          return selectedOption.value || selectedOption.text || '';
        }
        return '';
      }
    }

    if (tagName === 'textarea') {
      const textareaElement = element as HTMLTextAreaElement;
      return textareaElement.value || '';
    }

    if (element.contentEditable === 'true') {
      return element.textContent || element.innerText || '';
    }

    // For other custom components, try to get value from common attributes
    const value = element.getAttribute('value') ||
                  element.getAttribute('data-value') ||
                  element.textContent ||
                  element.innerText;

    return value ? value.trim() : '';
  }

  // Method to get RAW value from ejs-radiobutton components (no filtering)
  private getEjsRadioButtonValueRaw(element: HTMLElement): string {
    // Find the ejs-radiobutton element if we're dealing with a child element
    const ejsRadioButton = element.tagName.toLowerCase() === 'ejs-radiobutton'
      ? element
      : element.closest('ejs-radiobutton');

    if (!ejsRadioButton) {
      return '';
    }

    // Find the input element inside the ejs-radiobutton
    const radioInput = ejsRadioButton.querySelector('input[type="radio"]') as HTMLInputElement;
    if (!radioInput) {
      return '';
    }

    // Return the raw value of this specific radio button (regardless of checked state)
    return radioInput.value || radioInput.getAttribute('value') || '';
  }

  // Method to get PROCESSED value from ejs-radiobutton components (with filtering)
  private getEjsRadioButtonValue(element: HTMLElement): string {
    // Find the ejs-radiobutton element if we're dealing with a child element
    const ejsRadioButton = element.tagName.toLowerCase() === 'ejs-radiobutton'
      ? element
      : element.closest('ejs-radiobutton');

    if (!ejsRadioButton) {
      return '';
    }

    // Find the input element inside the ejs-radiobutton
    const radioInput = ejsRadioButton.querySelector('input[type="radio"]') as HTMLInputElement;
    if (!radioInput) {
      return '';
    }

    const groupName = radioInput.name;
    if (!groupName) {
      return '';
    }

    // Find the checked radio button in this group
    const checkedRadio = document.querySelector(`input[type="radio"][name="${groupName}"]:checked`) as HTMLInputElement;
    if (checkedRadio) {
      // Return the value of the checked radio button
      return checkedRadio.value || checkedRadio.getAttribute('value') || 'true';
    }

    // If this specific radio is checked, return its value
    if (radioInput.checked) {
      return radioInput.value || radioInput.getAttribute('value') || 'true';
    }

    // For unchecked radio buttons, return empty string
    return '';
  }

  // Method to get value from ejs-dropdownlist components
  private getEjsDropdownValue(element: HTMLElement): string {
    // Find the ejs-dropdownlist element if we're dealing with a child element
    const ejsDropdown = element.tagName.toLowerCase() === 'ejs-dropdownlist'
      ? element
      : element.closest('ejs-dropdownlist');

    if (!ejsDropdown) {
      return '';
    }

    // Try to get the value from various attributes that ejs-dropdownlist might use
    const possibleValueAttributes = [
      'value',
      'data-value',
      'ng-reflect-value',
      'aria-valuenow'
    ];

    for (const attr of possibleValueAttributes) {
      const value = ejsDropdown.getAttribute(attr);
      if (value && value.trim() !== '') {
        return value.trim();
      }
    }

    // Try to find hidden input that might store the actual value
    const hiddenInput = ejsDropdown.querySelector('input[type="hidden"]') as HTMLInputElement;
    if (hiddenInput && hiddenInput.value) {
      return hiddenInput.value;
    }

    // Try to find any input within the dropdown
    const anyInput = ejsDropdown.querySelector('input') as HTMLInputElement;
    if (anyInput && anyInput.value) {
      return anyInput.value;
    }

    // Look for data attributes on the selected item
    const selectedItem = ejsDropdown.querySelector('.e-list-item.e-active, .e-list-item[aria-selected="true"], .selected');
    if (selectedItem) {
      const itemValue = selectedItem.getAttribute('data-value') ||
                       selectedItem.getAttribute('value') ||
                       selectedItem.getAttribute('data-uid');
      if (itemValue) {
        return itemValue;
      }
    }

    // As a last resort, try to extract value from the display text
    // Look for patterns like "PL Leave Type" and extract "PL"
    const displayText = ejsDropdown.textContent?.trim() || '';
    if (displayText) {
      // Try to extract the first part before space (common pattern for value + description)
      const parts = displayText.split(/\s+/);
      if (parts.length > 1 && parts[0].length <= 5) { // Assuming value codes are short
        return parts[0];
      }
    }

    return displayText;
  }

  // Method to generate a unique key for an element
  private generateElementKey(element: HTMLElement, labelName: string): string {
    // Use label name as primary key if available
    if (labelName && labelName !== '' && !labelName.includes('_unknown')) {
      return labelName.toLowerCase().replace(/[^a-z0-9]/g, '_').replace(/_+/g, '_');
    }

    // Use id if available
    if (element.id) {
      return element.id.toLowerCase().replace(/[^a-z0-9]/g, '_');
    }

    // Use name attribute if available
    const name = element.getAttribute('name');
    if (name) {
      return name.toLowerCase().replace(/[^a-z0-9]/g, '_');
    }

    // Generate based on element type and position
    const type = this.getElementType(element);
    const position = this.getElementPosition(element);

    return `${type}_${position}`;
  }

  // Method to get element position among similar elements
  private getElementPosition(element: HTMLElement): number {
    const tagName = element.tagName.toLowerCase();
    const allSimilar = document.querySelectorAll(tagName);

    for (let i = 0; i < allSimilar.length; i++) {
      if (allSimilar[i] === element) {
        return i + 1;
      }
    }

    return 1;
  }

  // Method to check if element is visible
  private isElementVisible(element: HTMLElement): boolean {
    const rect = element.getBoundingClientRect();
    const style = window.getComputedStyle(element);

    return (
      rect.width > 0 &&
      rect.height > 0 &&
      style.display !== 'none' &&
      style.visibility !== 'hidden' &&
      style.opacity !== '0' &&
      element.offsetParent !== null
    );
  }

  // Public method to extract element information using the interface
  public getElementExtractionData(element: HTMLElement): ElementExtractionData | null {
    return this.extractElementInfo(element);
  }

  // Public method to extract multiple elements information
  public getMultipleElementsExtractionData(elements: HTMLElement[]): ElementExtractionData[] {
    const extractedElements: ElementExtractionData[] = [];

    for (const element of elements) {
      if (this.isElementVisible(element)) {
        const elementInfo = this.extractElementInfo(element);
        if (elementInfo) {
          extractedElements.push(elementInfo);
        }
      }
    }

    return extractedElements;
  }

  // Public method to get consolidated elements data (for testing)
  public async getConsolidatedElementsData(): Promise<Array<{
    rawElementInfo: ElementExtractionData;
    processedElementData: {
      key: string;
      value: string;
      labelName: string;
      xpath: string;
      type: string;
    } | null;
  }>> {
    const allElementsData: Array<{
      rawElementInfo: ElementExtractionData;
      processedElementData: {
        key: string;
        value: string;
        labelName: string;
        xpath: string;
        type: string;
      } | null;
    }> = [];

    const formElements = this.findFormElements();

    for (const element of formElements) {
      const elementInfo = this.extractElementInfo(element);
      if (elementInfo) {
        const elementData = this.extractElementData(element);

        allElementsData.push({
          rawElementInfo: elementInfo,
          processedElementData: elementData
        });
      }
    }

    return allElementsData;
  }

  // Public method to extract all form elements on the page using the interface
  public getAllFormElementsExtractionData(): ElementExtractionData[] {
    const formElements = this.findFormElements();
    return this.getMultipleElementsExtractionData(formElements);
  }

  // Utility method to demonstrate the new interface usage
  public demonstrateElementExtraction(element: HTMLElement): void {
    console.log('🔍 Demonstrating new interface-based element extraction:');

    // Single method call to get all element information
    const elementData = this.getElementExtractionData(element);

    if (elementData) {
      console.log('📊 Complete Element Data:', elementData);

      // You can now access all properties from the interface
      const { xpath, labelName, type, value, key } = elementData;

      console.log('🏷️ Label Name:', labelName);
      console.log('🎯 XPath:', xpath);
      console.log('🔧 Type:', type);
      console.log('💾 Value:', value);
      console.log('🔑 Key:', key);
    } else {
      console.log('❌ No data extracted from element');
    }
  }

  // Test method specifically for ejs-radiobutton components
  public testEjsRadioButtonExtraction(): void {
    console.log('🧪 Testing ejs-radiobutton extraction...');

    // Find all ejs-radiobutton components
    const ejsRadioButtons = document.querySelectorAll('ejs-radiobutton');
    console.log(`Found ${ejsRadioButtons.length} ejs-radiobutton components`);

    ejsRadioButtons.forEach((ejsRadio, index) => {
      console.log(`\n📻 Testing ejs-radiobutton ${index + 1}:`);

      const elementData = this.getElementExtractionData(ejsRadio as HTMLElement);
      if (elementData) {
        console.log('✅ Extracted data:', elementData);

        // Also check the internal input element
        const internalInput = ejsRadio.querySelector('input[type="radio"]') as HTMLInputElement;
        if (internalInput) {
          console.log('🔍 Internal input details:', {
            name: internalInput.name,
            value: internalInput.value,
            checked: internalInput.checked,
            id: internalInput.id
          });
        }
      } else {
        console.log('❌ No data extracted');
      }
    });

    // Also test radio groups
    const radioGroups = new Set<string>();
    document.querySelectorAll('input[type="radio"]').forEach(radio => {
      const input = radio as HTMLInputElement;
      if (input.name) {
        radioGroups.add(input.name);
      }
    });

    console.log(`\n📊 Found ${radioGroups.size} radio groups:`, Array.from(radioGroups));

    radioGroups.forEach(groupName => {
      const checkedRadio = document.querySelector(`input[type="radio"][name="${groupName}"]:checked`) as HTMLInputElement;
      console.log(`📻 Group "${groupName}":`, {
        hasCheckedRadio: !!checkedRadio,
        checkedValue: checkedRadio?.value || 'none'
      });
    });
  }
}

// Create and export the singleton instance
const workAgentSignalRService = WorkAgentSignalRService.getInstance();

export default workAgentSignalRService;

// Export the service and types for direct use
export { workAgentSignalRService, WorkAgentSignalRService };
