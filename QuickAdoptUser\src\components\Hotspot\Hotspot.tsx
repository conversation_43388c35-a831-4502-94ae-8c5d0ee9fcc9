import React, { useCallback, useEffect, useRef, useState } from "react";
import HotspotPopup from "./HotspotPopup";
import { browser, trackUserEngagement, userData, version } from "../UserEngagement/userEngagementTracking";
interface ButtonAction {
  Action: string;
  ActionValue: string;
  TargetUrl: string;
}
interface Guide {
  GuideId: string;
  DontShowAgain: boolean;
}
interface ButtonProperties {
  Padding: number;
  Width: number;
  Font: number;
  FontSize: number;
  ButtonTextColor: string;
  ButtonBackgroundColor: string;
}

interface ButtonData {
  ButtonStyle: string;
  ButtonName: string;
  Alignment: string;
  BackgroundColor: string;
  ButtonAction: ButtonAction;
  Padding: {
    Top: number;
    Right: number;
    Bottom: number;
    Left: number;
  };
  ButtonProperties: ButtonProperties;
}
interface HotspotProperties {
  size: string;
  type: string;
  color: string;
  showUpon: string;
  showByDefault: boolean;
  stopAnimation: boolean;
  pulseAnimation: boolean;
  position: {
    XOffset: string;
    YOffset: string;
  };
}

interface Step {
  guideId: string;
  xpath: string;
  hotspotProperties: HotspotProperties;
  content: string | JSX.Element;
  targetUrl: string;
  imageUrl: string;
  buttonData: ButtonData[];
}

interface HotspotGuideProps {
  guideStep: any;
  key: any;
  guideDetails: any;
  setPopupVisibility: any;
  onPopupVisible: any;
  guide: any;
  guides: any;
  currentStep: any;
  handleClose: any;
  handleDontShowAgain: any;
  popupVisibility: any;
  setCurrentStep: any;
  onContinue: any;
  onRestart?: any;
  // Removed currentUrl from props
}

const Hotspot: React.FC<HotspotGuideProps> = ({ guideStep, key, guideDetails, setPopupVisibility, onPopupVisible, guide, guides, currentStep, handleClose, handleDontShowAgain, popupVisibility, setCurrentStep, onContinue, onRestart }) => {
  const [rectData, setRectData] = useState<number>(100);
  const [hotspotClicked, setHotspotClicked] = useState(false);

  const [rectDataLeft, setRectDataLeft] = useState<number>(100);  const [targetElement, setTargetElement] = useState<HTMLElement | null>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(document.body);
  const [elementVisibility, setElementVisibility] = useState<Record<string, boolean>>({});
  const [popupPosition, setPopupPosition] = useState<{ top: number; left: number } | null>(null);
  const [hotspotSize, setHotspotSize] = useState<number>(30); // Track hotspot size for dynamic popup positioning
  const observerRef = useRef<MutationObserver | null>(null);
  const [hasViewed, setHasViewed] = useState(false);
  let engagementTracked = false;
  let initialTime = Date.now();
  const stepData = guide?.GuideStep?.[currentStep]?.StepTitle;
 // const [baseLeft, setBaseLeft] = useState(Number);
  useEffect(() => {
    if (guideDetails?.data?.length > 0) {
        const initialVisibility = guideDetails.data.reduce((acc: Record<string, boolean>, guide: any) => {
            acc[guide.GuideId] = false; // Initialize all elements as not visible
            return acc;
        }, {});
        setElementVisibility(initialVisibility);
    }
  }, [guideDetails]);
  const generateDynamicId = () => {
		return "hotspotBlink"+ guideStep.guideId;
  };
const updateElementVisibility = (guideId: string, isVisible: boolean) => {
  setElementVisibility((prev) => ({
      ...prev,
      [guideId]: isVisible,
  }));
};
  let element: any;
  let rect: any;
  let hotspot = document.getElementById(generateDynamicId());
  const getElementByXPath = (xpath: string): HTMLElement | null => {
    const result = document.evaluate(
      xpath,
      document,
      null,
      XPathResult.FIRST_ORDERED_NODE_TYPE,
      null
    );
    const node = result.singleNodeValue;
    if (node instanceof HTMLElement) {
      return node;
    } else if (node?.parentElement) {
      return node.parentElement;
    } else {
      return null;
    }
  };
  const removeHotspot = () => {
    const hotspotId = generateDynamicId(); // Ensure this ID is consistent with creation
    const existingHotspot = document.getElementById(hotspotId);
    if (existingHotspot) {
      existingHotspot.remove();
    }
  };
  const isElementInViewport = (element: HTMLElement) => {
    const rect = element.getBoundingClientRect();
    return (
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
      rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
  };

  const validateElementPosition = (element: HTMLElement) => {
    const rect = element.getBoundingClientRect();
    return (
      rect.width > 0 &&
      rect.height > 0 &&
      rect.top !== 0 &&
      rect.left !== 0 &&
      !Number.isNaN(rect.top) &&
      !Number.isNaN(rect.left)
    );
  };

  // Function to calculate popup position below the hotspot
  const calculatePopupPosition = (elementRect: DOMRect, hotspotSize: number, xOffset: number, yOffset: number) => {
    const hotspotLeft = elementRect.x + xOffset;
    const hotspotTop = elementRect.y + yOffset;

    // Position popup below the hotspot for better user experience
    const dynamicOffsetX = hotspotSize + 5; // Align horizontally with hotspot
    const dynamicOffsetY = hotspotSize + 10; // Position below hotspot with spacing

    return {
      top: hotspotTop + window.scrollY + dynamicOffsetY,
      left: hotspotLeft + window.scrollX + dynamicOffsetX
    };
  };

  const updateHotspotPosition = useCallback(() => {
    const hotspotPropData = guideStep?.hotspotProperties;
      const xOffset = parseFloat(hotspotPropData?.position.XOffset || "4");
      const yOffset = parseFloat(hotspotPropData?.position.YOffset || "4");
      const element = getElementByXPath(guideStep?.xpath);
      // if (currentUrl !== guideStep?.targetUrl) {
      //   setTargetElement(null);
      //   updateElementVisibility(guideStep?.guideId, false);
      //   removeHotspot(); // Remove the hotspot
      //   return;
      // }
      if (!element) {
        setTargetElement(null);
        updateElementVisibility(guideStep?.guideId, false);
        removeHotspot(); // Remove the hotspot
        return;
    }
    const isValid = validateElementPosition(element);
    const isVisible = isElementInViewport(element);

    if (!isValid || !isVisible) {
      setTargetElement(null);
      updateElementVisibility(guideStep?.guideId, false);
      removeHotspot(); // Remove the hotspot
      return;
    }
      if (element) {
        setTargetElement(element);
        updateElementVisibility(guideStep?.guideId, true);
        rect = element.getBoundingClientRect();

        const top = rect?.top + window.scrollY + yOffset;
        const left = rect?.left + window.scrollX + xOffset;
        hotspot = document.getElementById("hotspotBlink"+ guideStep.guideId);

        if (hotspot) {
          hotspot.style.left = `${left}px`;
          hotspot.style.top = `${top}px`;
        }
        // observer.disconnect();
        const canvas = handlePopupProperties("canvasProperties", guide);
        setRectData(parseInt(canvas.XAxisOffset,10) + rect?.top + -5);
        setRectDataLeft(parseInt(canvas.YAxisOffset,10) + rect?.left + 30);

        // Calculate dynamic popup position
        const currentHotspotSize = parseFloat(guideStep?.hotspotProperties?.size || "30");
        setHotspotSize(currentHotspotSize);
        const popupPos = calculatePopupPosition(rect, currentHotspotSize, xOffset, yOffset);
        setPopupPosition(popupPos);
      }



  }, [guideStep?.xpath, guideStep?.hotspotProperties]);

  useEffect(() => {
    const handleDOMChanges = () => {
      requestAnimationFrame(updateHotspotPosition);
    };
    observerRef.current = new MutationObserver(handleDOMChanges);
    const targetNode = document.body;
    observerRef.current.observe(targetNode, {
      childList: true,
      subtree: true,
      attributes: true,
      characterData: true
    });
    updateHotspotPosition();
    return () => {
      observerRef.current?.disconnect();
    };
  }, [guideStep?.xpath]);

  useEffect(() => {
    const handleViewportChanges = () => {
      requestAnimationFrame(updateHotspotPosition);
    };

    window.addEventListener('scroll', handleViewportChanges);
    window.addEventListener('resize', handleViewportChanges);

    return () => {
      window.removeEventListener('scroll', handleViewportChanges);
      window.removeEventListener('resize', handleViewportChanges);
    };
  }, [guideStep?.xpath]);

  // Recalculate popup position when hotspot size changes
  useEffect(() => {
    if (guideStep?.xpath && hotspotSize) {
      const element = getElementByXPath(guideStep?.xpath);
      if (element) {
        const rect = element.getBoundingClientRect();
        const hotspotPropData = guideStep?.hotspotProperties;
        const xOffset = parseFloat(hotspotPropData?.position.XOffset || "4");
        const yOffset = parseFloat(hotspotPropData?.position.YOffset || "4");

        const popupPos = calculatePopupPosition(rect, hotspotSize, xOffset, yOffset);
        setPopupPosition(popupPos);
      }
    }
  }, [hotspotSize, guideStep?.xpath, guideStep?.hotspotProperties]);

  // Recalculate popup position on window resize
  useEffect(() => {
    const handleResize = () => {
      if (guideStep?.xpath && hotspotSize) {
        const element = getElementByXPath(guideStep?.xpath);
        if (element) {
          const rect = element.getBoundingClientRect();
          const hotspotPropData = guideStep?.hotspotProperties;
          const xOffset = parseFloat(hotspotPropData?.position.XOffset || "4");
          const yOffset = parseFloat(hotspotPropData?.position.YOffset || "4");

          const popupPos = calculatePopupPosition(rect, hotspotSize, xOffset, yOffset);
          setPopupPosition(popupPos);
        }
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [guideStep?.xpath, hotspotSize, guideStep?.hotspotProperties]);

  useEffect(() => {
    const hotspotPropData = guideStep?.hotspotProperties;
    const xOffset = parseFloat(hotspotPropData?.position.XOffset || "4");
    const yOffset = parseFloat(hotspotPropData?.position.YOffset || "4");
    element = getElementByXPath(guideStep?.xpath);
    // if (currentUrl !== guideStep?.targetUrl) {
    //   setTargetElement(null);
    //   updateElementVisibility(guideStep?.guideId, false);
    //   removeHotspot(); // Remove the hotspot

    //   return;
    // }
    if (!element) {
      setTargetElement(null);
      updateElementVisibility(guideStep?.guideId, false);
      removeHotspot(); // Remove the hotspot

      return;
    }
    const isValid = validateElementPosition(element);
    const isVisible = isElementInViewport(element);

    if (!isValid || !isVisible) {
      setTargetElement(null);
      updateElementVisibility(guideStep?.guideId, false);
      removeHotspot(); // Remove the hotspot

      return;
    }
     if (element) {
      setTargetElement(element);

      rect = element.getBoundingClientRect();
      if (rect) {
        const top = rect?.top + window.scrollY + yOffset;
        const left = rect?.left + window.scrollX + xOffset;
        // setRectData(element.offsetHeight + top + 10);
        // setRectDataLeft(rect?.left);
        updateElementVisibility(guideStep?.guideId, true);
      }
    }


    const applyHotspotStyles = () => {
    const hotspotPropData = guideStep?.hotspotProperties;
    const xOffset = parseFloat(hotspotPropData?.position.XOffset || "4");
      const yOffset = parseFloat(hotspotPropData?.position.YOffset || "4");
      // const sizeOffsetMap: Record<number, number> = {
			// 	16: 10,
			// 	20: 20,
			// 	24: 30,
			// 	28: 40,
			// 	32: 50,
			// 	38: 60,
			// 	40: 70,
			// 	44: 70,
			// 	48: 70,
			//   };
			  
			// const sizeOffset = sizeOffsetMap[Number(hotspotPropData?.Size) ?? 0] ?? 0;
			
			// const baseLefts = (rect?.left + window.scrollX + xOffset + sizeOffset|| 0);
      // setBaseLeft(baseLefts);
      // setRectDataLeft(baseLefts);
    const size = hotspotPropData?.size;
    const top = rect?.top + window.scrollY + yOffset;
    const left = rect?.left + window.scrollX + xOffset;
    // let hotspot = document.getElementById(generateDynamicId());
      if (!hotspot) {
        hotspot = document.createElement("div");
        hotspot.id = generateDynamicId();
        document.body.appendChild(hotspot);
      }

      const handleClick = () => {

          setHotspotClicked(true);
        };
        hotspot.addEventListener("click", handleClick);


      hotspot.style.position = "absolute";
      hotspot.style.left = `${left}px`;
      hotspot.style.top = `${top}px`;
      hotspot.style.width = `${size}px`;
      hotspot.style.height = `${size}px`;
      hotspot.style.backgroundColor = hotspotPropData?.color ? hotspotPropData.color : "yellow";
      hotspot.style.borderRadius = "50%";
      hotspot.style.zIndex = "1000";
      hotspot.style.transition = "none";
      hotspot.innerHTML = "";

      // Update hotspot size and calculate dynamic popup position
      const currentHotspotSize = parseFloat(size || "30");
      setHotspotSize(currentHotspotSize);
      if (rect) {
        const popupPos = calculatePopupPosition(rect, currentHotspotSize, xOffset, yOffset);
        setPopupPosition(popupPos);
      }
      if (hotspotPropData?.type === "Info" || hotspotPropData?.type === "Question") {
        const textSpan = document.createElement("span");
        textSpan.innerText = hotspotPropData.type === "Info" ? "i" : "?";
        textSpan.style.color = "white";
        textSpan.style.fontSize = "14px";
        textSpan.style.fontWeight = "bold";
        textSpan.style.fontStyle = hotspotPropData.type === "Info" ? "italic" : "normal";
        textSpan.style.display = "flex";
        textSpan.style.alignItems = "center";
        textSpan.style.justifyContent = "center";
        textSpan.style.width = "100%";
        textSpan.style.height = "100%";
        hotspot.appendChild(textSpan);
      }

      // Function to stop animation
      const stopAnimation = () => {
        if (hotspot && !hotspot.classList.contains("pulse-animation-removed")) {
          hotspot.classList.remove("pulse-animation");
          hotspot.classList.add("pulse-animation-removed");
          // Ensure the hotspot remains visible by keeping its styles
          hotspot.style.display = "flex";
          hotspot.style.opacity = "1";
          hotspot.style.transform = "scale(1)";
        }
      };

      // Apply pulse animation if enabled
      if (hotspotPropData && hotspotPropData.pulseAnimation) {
        hotspot.classList.add("pulse-animation");

        // Clear any existing event listeners
        hotspot.onclick = null;
        hotspot.onmouseover = null;
        hotspot.onmouseout = null;

        // Handle showing popup based on showByDefault setting
        if (hotspotPropData?.showByDefault) {
          setPopupVisibility((prev:any) => ({
            ...prev,
            [guideStep?.guideId]: true,
          }));
        } else {
          setPopupVisibility((prev:any) => ({
            ...prev,
            [guideStep?.guideId]: false,
          }));

          // Handle showing popup and stopping animation based on ShowUpon setting
          if (hotspotPropData?.showUpon === "Hovering Hotspot") {
            if (hotspotPropData.stopAnimation) {
              // Combined handler for hover that both shows tooltip and stops animation
              const combinedHoverHandler = () => {
                if (!engagementTracked) {
                  engagementTracked = true;
                  trackUserEngagement("hotspot-hover", userData, guide, browser, version, "hotspotBlinks1"+ guideStep.guideId, stepData, 0, 0);
                }

                setPopupVisibility((prev:any) => ({
                  ...prev,
                  [guideStep?.guideId]: true,
                }));

                // Stop animation on hover if stopAnimation is enabled
                stopAnimation();
              };

              const handleMouseOut = () => {
                setPopupVisibility((prev: any) => ({
                  ...prev,
                  [guideStep?.guideId]: true,
                }));
              };

              hotspot.addEventListener("mouseover", combinedHoverHandler);
              hotspot.addEventListener("mouseout", handleMouseOut);
            } else {
              // Regular hover handler without stopping animation
              const handleMouseOver = () => {
                if (!engagementTracked) {
                  engagementTracked = true;
                  trackUserEngagement("hotspot-hover", userData, guide, browser, version, "hotspotBlinks1"+ guideStep.guideId, stepData, 0, 0);
                }

                setPopupVisibility((prev:any) => ({
                  ...prev,
                  [guideStep?.guideId]: true,
                }));
              };

              const handleMouseOut = () => {
                setPopupVisibility((prev: any) => ({
                  ...prev,
                  [guideStep?.guideId]: true,
                }));
              };

              hotspot.addEventListener("mouseover", handleMouseOver);
              hotspot.addEventListener("mouseout", handleMouseOut);
            }
          } else if (hotspotPropData?.showUpon === "Clicking Hotspot") {
            if (hotspotPropData.stopAnimation) {
              // Combined handler for click that both shows tooltip and stops animation
              const combinedClickHandler = () => {
                trackUserEngagement("hotspot-click", userData, guide, browser, version, "hotspotBlinks2"+ guideStep.guideId, stepData, 0, 0);

                setPopupVisibility((prev:any) => ({
                  ...prev,
                  [guideStep?.guideId]: true,
                }));

                // Stop animation on click if stopAnimation is enabled
                stopAnimation();
              };

              hotspot.addEventListener("click", combinedClickHandler);
            } else {
              // Regular click handler without stopping animation
              const handleMouseClick = () => {
                trackUserEngagement("hotspot-click", userData, guide, browser, version, "hotspotBlinks2"+ guideStep.guideId, stepData, 0, 0);

                setPopupVisibility((prev:any) => ({
                  ...prev,
                  [guideStep?.guideId]: true,
                }));
              };

              hotspot.addEventListener("click", handleMouseClick);
            }
          }
        }
      } else {
        // If pulse animation is disabled, ensure it's not applied
        hotspot.classList.remove("pulse-animation");
        hotspot.classList.add("pulse-animation-removed");

        // Still handle showing popup based on settings
      if (hotspotPropData?.showByDefault) {
        setPopupVisibility((prev:any) => ({
          ...prev,
          [guideStep?.guideId]: true,
        }));
      } else {
        setPopupVisibility((prev:any) => ({
          ...prev,
          [guideStep?.guideId]: false,
        }));

        if (hotspotPropData?.showUpon === "Hovering Hotspot") {

          const handleMouseOver = () => {
            if(engagementTracked) return;
            engagementTracked = true;
            setPopupVisibility((prev:any) => ({
              ...prev,
              [guideStep?.guideId]: true,

            }));

            trackUserEngagement("hotspot-hover", userData, guide, browser,version,"hotspotBlinks1"+ guideStep.guideId,stepData,0,0);

          };

          const handleMouseOut = () => {
            setPopupVisibility((prev: any) => ({
              ...prev,
              [guideStep?.guideId]: true,
            }));
          };

          hotspot.addEventListener("mouseover", handleMouseOver);
          hotspot.addEventListener("mouseout", handleMouseOut);

        } else if (hotspotPropData?.showUpon === "Clicking Hotspot") {
          const handleMouseClick = () => {

            trackUserEngagement("hotspot-click", userData, guide, browser,version,"hotspotBlinks2"+ guideStep.guideId,stepData,0,0);
            setPopupVisibility((prev:any) => ({
              ...prev,
              [guideStep?.guideId]: true,
            }));          };

          hotspot.addEventListener("click", handleMouseClick);
          }
        }
      }


  };

    applyHotspotStyles();

      return () => {
        if (hotspot) {

          hotspot.onclick = null;
          hotspot.onmouseover = null;
          hotspot.onmouseout = null;

        }
      };

  }, [guideStep?.xpath, hotspotClicked, elementVisibility[guideStep?.guideId]]);

  useEffect(() => {
    if (guideStep?.hotspotProperties?.showByDefault) {
      onPopupVisible(true);
    }
  }, [guideStep?.hotspotProperties?.showByDefault, onPopupVisible]);
  // Handle hotspot hover functionality in the useEffect hooks instead

  const handlePopupProperties = (popUpProps: string, guide: any) => {
    if (guide) {
      // Use currentStep to get the correct step data instead of always using [0]
      const guideStep = guide?.GuideStep?.[currentStep];
      switch (popUpProps) {
        case "ImageUrl":
          return guideStep?.ImageProperties?.flatMap((imgProp: any) =>
            imgProp?.CustomImage?.map((uploadedImage: any) => uploadedImage.Url)
          ) || [];
        case "previousButtonLabel":
          return guideStep?.ButtonSection?.[0]?.CustomButtons?.[0]?.ButtonName || "Previous";
        case "continueButtonLabel":
          return guideStep?.ButtonSection?.[0]?.CustomButtons?.[1]?.ButtonName || "Continue";
        case "progress":
          return ((currentStep + 1) / guide?.GuideStep?.length) * 100;
        case "videoUrl":
          return guideStep?.VideoEmbedCode;
        case "textFieldProperties":
          return guideStep?.TextFieldProperties;
        case "Hotspot":
          return guideStep?.Hotspot;
        case "imageProperties":
          return guideStep?.ImageProperties;
        case "modalProperties":
          return guideStep?.Modal;
        case "canvasProperties":
          return guideStep?.Canvas || {};
        case "html_Snippet":
          return guideStep?.HtmlSnippet;
        case "customButtons":
          return guideStep?.CustomButton || [];
        case "previousButtonStyles":
          return guideStep?.CustomButton?.[0]?.ButtonProperties;
        case "continueButtonStyles":
          return guideStep?.CustomButton?.[0]?.ButtonProperties;
        case "Overlayvalue":
          return guideStep?.Overlay;
        default:
          break;
      }
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleContinue = () => {
    setCurrentStep(currentStep + 1);
  };
  //console.log(currentStep, "CurrentStep")
    ;
  return (
    <>
      <div
				// className={`${selectedTemplate === "Hotspot" || selectedTemplate === "Tooltip" ? "highlight-border" : ""}`}
				// style={{ position: "absolute", top: 50 }}
				>
					  {/* <p>
						Lorem, ipsum dolor sit amet consectetur adipisicing elit. Inventore, optio. Recusandae doloribus
						consequuntur nobis tenetur voluptate, minus
						<span>
							<a href="http://quixy.com">Quixy Web</a>
						</span>
						repudiandae laborum magnam optio
          <span>
          <span>
							<a href="http://quixy.com">Quixy Web New</a>
						</span>
							<button
								onClick={() => {
									alert("Button Clicked");
								}}
							>
								Click me
							</button>
						</span>
						<span>
							<input type="text" />
						</span>
						<span>
							<select
								name=""
								id=""
							>
								<option value="a">A</option>
								<option value="b">B</option>
							</select>
						</span>
						earum nihil! Temporibus nostrum eum recusandae! Vitae, unde expedita.
        </p>   */}




</div>
{popupVisibility[guide.GuideId] && elementVisibility[guide?.GuideId] && (

       <HotspotPopup
                key={guide.GuideId}
               data={guide}
               rectData={popupPosition?.top || rectData}
               rectDataLeft={popupPosition?.left || rectDataLeft}
                anchorEl={anchorEl}
          onClose={() => handleClose(guide.GuideId)}
        onPrevious={handlePrevious}
                onContinue={handleContinue}
                onRestart={onRestart}
                onDontShowAgain={handleDontShowAgain}
                title={guide?.GuideStep?.[currentStep]?.StepTitle}
                text={
                  guide?.GuideStep?.[currentStep]?.TextFieldProperties
                    ?.map((field: any) => field.Text)
                    .filter((text: any) => text) // Remove undefined or null values
                    .join(" ") || "" // Combine into a single string with spaces
                }
                imageUrl={handlePopupProperties("ImageUrl", guide)}
                videoUrl={handlePopupProperties("videoUrl", guide)}
                previousButtonLabel={handlePopupProperties("previousButtonLabel", guide)}
                continueButtonLabel={handlePopupProperties("continueButtonLabel", guide)}
                currentStep={currentStep}
                totalSteps={guides?.data?.length || guide?.GuideStep?.length}
                progress={handlePopupProperties("progress", guide)}
                textFieldProperties={handlePopupProperties("textFieldProperties", guide)}
                imageProperties={handlePopupProperties("imageProperties", guide)}
                customButton={
                  guide?.GuideStep?.[currentStep]?.ButtonSection
                    ?.map((section: any) =>
                      section.CustomButtons.map((button: any) => ({
                        ...button,
                        ContainerId: section.Id, // Attach the container ID for grouping
                      }))
                    )
                    ?.reduce((acc: any[], curr: any[]) => acc.concat(curr), []) || [] // Flatten the array
                }
                modalProperties={handlePopupProperties("modalProperties", guide)}
                canvasProperties={handlePopupProperties("canvasProperties", guide)}
                htmlSnippet={handlePopupProperties("htmlSnippet", guide)}
                previousButtonStyles={handlePopupProperties("previousButtonStyles", guide)}
                continueButtonStyles={handlePopupProperties("continueButtonStyles", guide)}
                OverlayValue={handlePopupProperties("OverlayValue", guide)}
          hotspotProperties={handlePopupProperties("hotspot", guide)}
          selectedOption={guide?.GuideStep?.[currentStep]?.Tooltip.ProgressTemplate}

              />
)}
      <style>
        {`
          @keyframes pulse {
            0% {
              transform: scale(1);
              opacity: 1;
            }
            50% {
              transform: scale(1.5);
              opacity: 0.6;
            }
            100% {
              transform: scale(1);
              opacity: 1;
            }
          }
          .pulse-animation {
            animation: pulse 1.5s infinite;
          }

          .pulse-animation-removed
          {
            animation:none;
          }
        `}
      </style>
    </>
  );
};

export default Hotspot;
