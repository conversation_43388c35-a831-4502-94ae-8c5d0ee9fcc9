import React, { useEffect, useState } from "react";


interface ImageCarouselProps {
  images: string[];
  selectedItem: any;
  activeItem: any;
  isMaximized: any;
  primaryColor: any;
}

const ImageCarousel: React.FC<ImageCarouselProps> = ({ images, selectedItem,activeItem,isMaximized,primaryColor }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  
  useEffect(() =>
  {
    setCurrentIndex(0);
},[activeItem])
  // Function to change the image when clicking a progress dot
  const goToImage = (index: number) => {
    setCurrentIndex(index);
  };

  return (
    <div style={{
      width: "-webkit-fill-available",
      height: isMaximized  ? "255px" : "245px",
    }} className="qadpt-imgsec">

      <div style={{
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  height: '100%',
  width: '100%'
}}
>
        {selectedItem?.SupportingMedia?.length > 0 && (
          <img
            src={images[currentIndex]} // Show selected image
            alt={`Image ${currentIndex}`}
            style={{
              width: "100%",
              height: "100%",
              borderRadius: "10px",
              transition: "opacity 0.5s ease-in-out", // Smooth transition effect
              objectFit:isMaximized ? "contain" :"initial",
            }}
          />
        )}
       
      </div>

      {/* 🔵 Progress Dots */}
      <div style={{ marginTop: "5px" ,display:"flex",placeContent:"center",alignItems:"center",position:"relative"}}>
        {images.map((_, index) => (
          <span
            key={index}
            onClick={() => goToImage(index)} // Set index correctly
            style={{
              height: "6px",
              width: "6px",
              margin: "3px",
              display: "inline-block",
              backgroundColor: currentIndex === index ? primaryColor : "gray",
              borderRadius: "50%",
              cursor: "pointer",
              transition: "background-color 0.3s",
            }}
          />
        ))}
      </div>
    </div>
  );
};

export default ImageCarousel;
