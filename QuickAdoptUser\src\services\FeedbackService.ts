// src/services/FeedbackService.ts

export interface FeedbackPayload {
    FeedbackId: string;
    UserId: string;
    OrganizationId: string;
    Message: string;
    FeedbackFile: string;
    Rating: string;
    CreatedDate: string;
    UserName: string;
    RequestDescription: string;
    RequestedTime: string;
    WrittenFeedback: string;
    UserFeedback: string;
}

// Use environment variable for API base URL
const USER_API_BASE = process.env.REACT_APP_USER_API || 'http://localhost:60552/api';

export async function sendFeedback(payload: FeedbackPayload) {
    const response = await fetch(`${USER_API_BASE}/EndUserGuide/SaveAiGuideFeedback`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
    });
    if (!response.ok) {
        throw new Error('Failed to send feedback');
    }
    return response.json();
}
