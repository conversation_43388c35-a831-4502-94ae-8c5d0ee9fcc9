import { useEffect, useState, useCallback, useRef } from "react";
import userApiService from "../service/APIService";
import { AxiosError, AxiosResponse } from "axios";

export interface IUseFetch {
	url: string;
	dependencies?: any[]; // Optional dependencies array for manual re-triggering
	timeout?: number; // Optional soft timeout in milliseconds (default: 30000) - logs warning but doesn't abort
	hardTimeout?: number; // Optional hard timeout in milliseconds - actually aborts the request
}

export interface IReponse {
	data: Record<string, any>;
	loading: boolean;
	error: {
		message: string;
		isError: boolean;
	};
}

// AbortController polyfill check for older browsers
const createAbortController = (): AbortController | null => {
	try {
		return new AbortController();
	} catch (error) {
		// AbortController not supported in older browsers
		console.warn('AbortController not supported, request cancellation disabled');
		return null;
	}
};

export const useFetch = ({ url, dependencies = [], timeout = 30000, hardTimeout }: IUseFetch) => {
	const [response, setResponse] = useState<IReponse>({
		data: [],
		loading: false,
		error: {
			message: "",
			isError: false,
		},
	});

	// Use ref to track current request and prevent race conditions
	const abortControllerRef = useRef<AbortController | null>(null);
	const requestIdRef = useRef<number>(0);
	const timeoutIdRef = useRef<NodeJS.Timeout | null>(null);
	const isMountedRef = useRef<boolean>(true);

	const fetchData = useCallback(async () => {
		if (!url || !isMountedRef.current) return; // Don't fetch if URL is empty or component unmounted

		// Cancel previous request if it exists
		if (abortControllerRef.current) {
			try {
				abortControllerRef.current.abort();
			} catch (error) {
				// Ignore abort errors
			}
		}

		// Clear previous timeout
		if (timeoutIdRef.current) {
			clearTimeout(timeoutIdRef.current);
			timeoutIdRef.current = null;
		}

		// Create new abort controller for this request (with fallback)
		const abortController = createAbortController();
		abortControllerRef.current = abortController;

		// Generate unique request ID to prevent race conditions
		const currentRequestId = ++requestIdRef.current;

		// Only update state if component is still mounted
		if (isMountedRef.current) {
			setResponse((prev) => ({
				...prev,
				loading: true,
				error: {
					message: "",
					isError: false,
				},
			}));
		}

		let isTimedOut = false;

		try {
			// Create API request promise with proper typing
			const apiPromise: Promise<AxiosResponse<any>> = userApiService.get(url, {
				...(abortController && { signal: abortController.signal }),
			});

			// Set up soft timeout that doesn't abort the request, just logs warning
			timeoutIdRef.current = setTimeout(() => {
				isTimedOut = true;
				// Don't abort the request - let it continue in background
				// This allows slow APIs to still return data for announcements
				console.warn(`API request taking longer than ${timeout}ms: ${url}`);
			}, timeout);

			// Set up hard timeout if specified (actually aborts the request)
			let hardTimeoutId: NodeJS.Timeout | null = null;
			if (hardTimeout) {
				hardTimeoutId = setTimeout(() => {
					if (abortController) {
						try {
							abortController.abort();
						} catch (error) {
							// Ignore abort errors
						}
					}
				}, hardTimeout);
			}

			// Wait for the API response (no race condition with soft timeout)
			const res = await apiPromise;

			// Clear both timeouts since request completed
			if (hardTimeoutId) {
				clearTimeout(hardTimeoutId);
			}

			// Clear timeout since request completed
			if (timeoutIdRef.current) {
				clearTimeout(timeoutIdRef.current);
				timeoutIdRef.current = null;
			}

			// Check if this is still the current request and component is mounted
			const isCurrentRequest = currentRequestId === requestIdRef.current;
			const isNotAborted = !abortController || !abortController.signal.aborted;

			if (isCurrentRequest && isNotAborted && isMountedRef.current) {
				setResponse((prev) => ({
					...prev,
					data: res.data || [],
					loading: false,
				}));
			}
		} catch (error: unknown) {
			// Clear timeout on error
			if (timeoutIdRef.current) {
				clearTimeout(timeoutIdRef.current);
				timeoutIdRef.current = null;
			}

			// Only update state if this is still the current request, not aborted, and component is mounted
			const isCurrentRequest = currentRequestId === requestIdRef.current;
			const isNotAborted = !abortController || !abortController.signal.aborted;

			if (isCurrentRequest && isNotAborted && isMountedRef.current) {
				// Create error object without mutating the original
				const errorMessage = error instanceof Error ? error.message : "An error occurred";

				setResponse((prev) => ({
					...prev,
					loading: false,
					error: {
						message: errorMessage,
						isError: true,
					},
				}));
			}
		}
	}, [url, timeout]);

	useEffect(() => {
		fetchData();

		// Cleanup function to cancel request on dependency change
		return () => {
			if (abortControllerRef.current) {
				try {
					abortControllerRef.current.abort();
				} catch (error) {
					// Ignore abort errors
				}
			}
			if (timeoutIdRef.current) {
				clearTimeout(timeoutIdRef.current);
				timeoutIdRef.current = null;
			}
		};
	}, [fetchData, ...dependencies]);

	// Cleanup on unmount
	useEffect(() => {
		isMountedRef.current = true;

		return () => {
			isMountedRef.current = false;

			if (abortControllerRef.current) {
				try {
					abortControllerRef.current.abort();
				} catch (error) {
					// Ignore abort errors
				}
			}

			if (timeoutIdRef.current) {
				clearTimeout(timeoutIdRef.current);
				timeoutIdRef.current = null;
			}
		};
	}, []);

	return [response, fetchData] as const;
};
