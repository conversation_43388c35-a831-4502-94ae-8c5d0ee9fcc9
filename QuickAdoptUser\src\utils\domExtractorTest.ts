/**
 * Test utilities for DOM data extraction functionality
 * This file contains helper functions to test the DOM extraction implementation
 */

export interface TestFormData {
  firstName: string;
  lastName: string;
  email: string;
  country: string;
  bio: string;
  interests: string[];
  gender: string;
  newsletter: boolean;
}

/**
 * Creates a test form dynamically for testing DOM extraction
 */
export function createTestForm(): HTMLFormElement {
  const form = document.createElement('form');
  form.id = 'dynamicTestForm';
  form.innerHTML = `
    <div>
      <label for="testFirstName">First Name:</label>
      <input type="text" id="testFirstName" name="firstName" value="Jane" />
    </div>
    
    <div>
      <label for="testLastName">Last Name:</label>
      <input type="text" id="testLastName" name="lastName" value="Smith" />
    </div>
    
    <div>
      <label for="testEmail">Email:</label>
      <input type="email" id="testEmail" name="email" value="<EMAIL>" />
    </div>
    
    <div>
      <label for="testCountry">Country:</label>
      <select id="testCountry" name="country">
        <option value="us" selected>United States</option>
        <option value="ca">Canada</option>
        <option value="uk">United Kingdom</option>
      </select>
    </div>
    
    <div>
      <label for="testBio">Bio:</label>
      <textarea id="testBio" name="bio">Test biography content</textarea>
    </div>
    
    <div>
      <label>Gender:</label>
      <input type="radio" id="testMale" name="gender" value="male" checked />
      <label for="testMale">Male</label>
      <input type="radio" id="testFemale" name="gender" value="female" />
      <label for="testFemale">Female</label>
    </div>
    
    <div>
      <input type="checkbox" id="testNewsletter" name="newsletter" checked />
      <label for="testNewsletter">Subscribe to newsletter</label>
    </div>
  `;
  
  return form;
}

/**
 * Adds the test form to the document body
 */
export function addTestFormToPage(): HTMLFormElement {
  const existingForm = document.getElementById('dynamicTestForm');
  if (existingForm) {
    existingForm.remove();
  }
  
  const form = createTestForm();
  document.body.appendChild(form);
  return form;
}

/**
 * Removes the test form from the document
 */
export function removeTestFormFromPage(): void {
  const form = document.getElementById('dynamicTestForm');
  if (form) {
    form.remove();
  }
}

/**
 * Validates extracted data against expected values
 */
export function validateExtractedData(
  extractedData: Record<string, string>,
  expectedData: Partial<TestFormData>
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Check if required fields are present
  const requiredFields = ['firstName', 'lastName', 'email'];
  for (const field of requiredFields) {
    if (!(field in extractedData)) {
      errors.push(`Missing required field: ${field}`);
    }
  }
  
  // Validate specific field values if expected data is provided
  if (expectedData.firstName && extractedData.firstName !== expectedData.firstName) {
    errors.push(`First name mismatch: expected "${expectedData.firstName}", got "${extractedData.firstName}"`);
  }
  
  if (expectedData.lastName && extractedData.lastName !== expectedData.lastName) {
    errors.push(`Last name mismatch: expected "${expectedData.lastName}", got "${extractedData.lastName}"`);
  }
  
  if (expectedData.email && extractedData.email !== expectedData.email) {
    errors.push(`Email mismatch: expected "${expectedData.email}", got "${extractedData.email}"`);
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Runs a comprehensive test of the DOM extraction functionality
 */
export async function runDomExtractionTest(): Promise<{
  success: boolean;
  extractedData: Record<string, string>;
  errors: string[];
}> {
  console.log('🧪 Starting DOM extraction test...');
  
  try {
    // Add test form to page
    const form = addTestFormToPage();
    console.log('✅ Test form added to page');
    
    // Wait a bit for form to be rendered
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Import the SignalR service (dynamic import to avoid circular dependencies)
    const { default: signalRService } = await import('../services/SignalRService');
    
    // Access the private extractDomData method for testing
    const service = signalRService as any;
    
    // Extract data
    const extractedData = await service.extractDomData();
    console.log('📊 Extracted data:', extractedData);
    
    // Validate the extracted data
    const expectedData: Partial<TestFormData> = {
      firstName: 'Jane',
      lastName: 'Smith',
      email: '<EMAIL>'
    };
    
    const validation = validateExtractedData(extractedData, expectedData);
    
    // Clean up
    removeTestFormFromPage();
    console.log('🧹 Test form removed from page');
    
    if (validation.isValid) {
      console.log('✅ DOM extraction test passed!');
      return {
        success: true,
        extractedData,
        errors: []
      };
    } else {
      console.log('❌ DOM extraction test failed:', validation.errors);
      return {
        success: false,
        extractedData,
        errors: validation.errors
      };
    }
    
  } catch (error) {
    console.error('❌ DOM extraction test error:', error);
    
    // Clean up on error
    removeTestFormFromPage();
    
    return {
      success: false,
      extractedData: {},
      errors: [error instanceof Error ? error.message : 'Unknown error']
    };
  }
}

/**
 * Test specifically for radio button extraction to ensure both selected and unselected radio buttons are included
 */
export async function testRadioButtonExtraction(): Promise<{
  success: boolean;
  extractedData: Record<string, string>;
  radioButtonsFound: number;
  errors: string[];
}> {
  console.log('🧪 Starting radio button extraction test...');

  try {
    // Add test form to page
    const form = addTestFormToPage();
    console.log('✅ Test form added to page');

    // Wait a bit for form to be rendered
    await new Promise(resolve => setTimeout(resolve, 100));

    // Import the SignalR service (dynamic import to avoid circular dependencies)
    const { default: signalRService } = await import('../services/SignalRService');

    // Access the private extractDomData method for testing
    const service = signalRService as any;

    // Extract data using the new consolidated method
    const extractedData = await service.extractDomData();
    console.log('📊 Extracted data:', extractedData);

    // Also get the consolidated elements data for detailed analysis
    const consolidatedData = await service.getConsolidatedElementsData();
    console.log('🎯 Consolidated elements data:', consolidatedData);

    // Count radio button entries in extracted data
    const radioButtonKeys = Object.keys(extractedData).filter(key =>
      key.includes('gender') ||
      extractedData[key] === 'male' ||
      extractedData[key] === 'female' ||
      extractedData[key] === ''
    );

    console.log('📻 Radio button keys found:', radioButtonKeys);

    // Check if we have both radio buttons (selected and unselected)
    const errors: string[] = [];
    let radioButtonsFound = 0;

    // Look for radio button entries
    for (const [key, value] of Object.entries(extractedData)) {
      if (key.includes('gender')) {
        radioButtonsFound++;
        console.log(`📻 Found radio button: ${key} = "${value}"`);
      }
    }

    // We should have at least 2 radio buttons (male and female)
    if (radioButtonsFound < 2) {
      errors.push(`Expected at least 2 radio buttons, but found ${radioButtonsFound}`);
    }

    // Check if we have both selected and unselected values
    const hasSelectedValue = Object.values(extractedData).some(value => value === 'male');
    const hasEmptyValue = Object.values(extractedData).some(value => value === '');

    if (!hasSelectedValue) {
      errors.push('Expected to find selected radio button value "male"');
    }

    if (!hasEmptyValue) {
      errors.push('Expected to find unselected radio button with empty value');
    }

    // Clean up
    removeTestFormFromPage();
    console.log('🧹 Test form removed from page');

    if (errors.length === 0) {
      console.log('✅ Radio button extraction test passed!');
      return {
        success: true,
        extractedData,
        radioButtonsFound,
        errors: []
      };
    } else {
      console.log('❌ Radio button extraction test failed:', errors);
      return {
        success: false,
        extractedData,
        radioButtonsFound,
        errors
      };
    }

  } catch (error) {
    console.error('❌ Radio button extraction test error:', error);

    // Clean up on error
    removeTestFormFromPage();

    return {
      success: false,
      extractedData: {},
      radioButtonsFound: 0,
      errors: [error instanceof Error ? error.message : 'Unknown error']
    };
  }
}

/**
 * Test the new consolidated elements data extraction
 */
export async function testConsolidatedElementsData(): Promise<{
  success: boolean;
  consolidatedData: any[];
  totalElements: number;
  errors: string[];
}> {
  console.log('🧪 Starting consolidated elements data test...');

  try {
    // Add test form to page
    addTestFormToPage();
    console.log('✅ Test form added to page');

    // Wait a bit for form to be rendered
    await new Promise(resolve => setTimeout(resolve, 100));

    // Import the SignalR service (dynamic import to avoid circular dependencies)
    const { default: signalRService } = await import('../services/SignalRService');

    // Get consolidated elements data
    const consolidatedData = await signalRService.getConsolidatedElementsData();

    console.log('🎯 ===== CONSOLIDATED ELEMENTS DATA TEST RESULTS =====');
    console.log(`📊 Total elements found: ${consolidatedData.length}`);

    consolidatedData.forEach((item, index) => {
      console.log(`\n--- Element ${index + 1} ---`);
      console.log('🔍 Raw Element Info:', item.rawElementInfo);
      console.log('✅ Processed Element Data:', item.processedElementData);
    });

    // Clean up
    removeTestFormFromPage();
    console.log('🧹 Test form removed from page');

    console.log('✅ Consolidated elements data test completed successfully!');
    return {
      success: true,
      consolidatedData,
      totalElements: consolidatedData.length,
      errors: []
    };

  } catch (error) {
    console.error('❌ Consolidated elements data test error:', error);

    // Clean up on error
    removeTestFormFromPage();

    return {
      success: false,
      consolidatedData: [],
      totalElements: 0,
      errors: [error instanceof Error ? error.message : 'Unknown error']
    };
  }
}

/**
 * Utility to log form elements found on the current page
 */
export function logCurrentPageFormElements(): void {
  console.log('🔍 Scanning current page for form elements...');
  
  const selectors = [
    'input[type="text"]',
    'input[type="email"]',
    'input[type="password"]',
    'textarea',
    'select',
    'input[type="checkbox"]',
    'input[type="radio"]',
    'input[type="date"]',
    '[contenteditable="true"]'
  ];
  
  const allElements: { selector: string; count: number; elements: Element[] }[] = [];
  
  selectors.forEach(selector => {
    const elements = Array.from(document.querySelectorAll(selector));
    const visibleElements = elements.filter(el => {
      const htmlEl = el as HTMLElement;
      const rect = htmlEl.getBoundingClientRect();
      const style = window.getComputedStyle(htmlEl);
      return (
        rect.width > 0 &&
        rect.height > 0 &&
        style.display !== 'none' &&
        style.visibility !== 'hidden'
      );
    });
    
    if (visibleElements.length > 0) {
      allElements.push({
        selector,
        count: visibleElements.length,
        elements: visibleElements
      });
    }
  });
  
  console.log('📋 Form elements found:');
  allElements.forEach(({ selector, count, elements }) => {
    console.log(`  ${selector}: ${count} elements`);
    elements.forEach((el, index) => {
      const htmlEl = el as HTMLElement;
      console.log(`    ${index + 1}. ID: ${htmlEl.id || 'none'}, Name: ${htmlEl.getAttribute('name') || 'none'}, Value: ${(htmlEl as any).value || htmlEl.textContent?.substring(0, 50) || 'none'}`);
    });
  });
  
  console.log(`📊 Total form elements: ${allElements.reduce((sum, item) => sum + item.count, 0)}`);
}

// Export test function to global scope for easy browser console access
if (typeof window !== 'undefined') {
  (window as any).runDomExtractionTest = runDomExtractionTest;
  (window as any).testRadioButtonExtraction = testRadioButtonExtraction;
  (window as any).testConsolidatedElementsData = testConsolidatedElementsData;
  (window as any).logCurrentPageFormElements = logCurrentPageFormElements;
  (window as any).addTestFormToPage = addTestFormToPage;
  (window as any).removeTestFormFromPage = removeTestFormFromPage;
}
