import { Box, Typography } from "@mui/material";
import React, { useState, useEffect, useMemo, version } from "react";
import { IReponse } from "../../hooks/useFetch";
import { POST } from "../../service/APIService";
import AnnouncementPopup from "../Announcement/AnnouncementPopUp";
import Banner from "../Banner/Banner";
import BannerStepUserView from "./BannerStepUserView";
import TooltipUserview from "../Tooltips/Tooltipuserview";
import TooltipGuide from "../Tooltips/Tooltips";
import HotspotPopup from "../Hotspot/HotspotPopup";
import Hotspot from "../Hotspot/Hotspot";
import { browser, trackUserEngagement, userData } from "../UserEngagement/userEngagementTracking";
import { useLocation } from "react-router-dom";
import FeedbackPopup from '../Common/FeedbackPopup';
import { sendFeedback, FeedbackPayload } from '../../services/FeedbackService';
import { newGuid } from '../../utils/guid';
import { useGuideDetails } from "../../context/GuideDetailsContext";

const TourUserView = ({ guide, setGuide, isFromAi, setIsFromAi, isOpen, setIsOpen, showFeedbackPopup, setShowFeedbackPopup, feedbackInfoState }: any) => {
    const location = useLocation();

    // Scheduled publishing logic for tours
    const getPublishWindowStatus = (data: any) => {
        const publishDate = data?.PublishDate ? new Date(data.PublishDate) : null;
        const unpublishDate = data?.UnPublishDate ? new Date(data.UnPublishDate) : null;
        const now = new Date();
        if (publishDate && now < publishDate) return false;
        if (unpublishDate && now > unpublishDate) return false;
        return true;
    };

  const [isGuideClosed, setIsGuideClosed] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(document.body);
  const [currentStep, setCurrentStep] = useState(0);
  const [isAnnouncementOpen, setAnnouncementOpen] = useState(false);
  const [scrollPercentage, setScrollPercentage] = useState(0);
  const currentUrl = window.location.href;   const [showTooltip, setShowTooltip] = useState(false);
  const [response, setResponse] = useState<IReponse>({
		data: [],
		loading: false,
		error: {
			message: "",
			isError: false,
		},
	});
  // const [response] = useFetch({
  //   // url: "/Guide/GetGuideDetails?guideId=09102024-180802907-0097902d-ecb8-4333-a70a-ba151cc641a6",
  //   url: `/EndUserGuide/GetGuideListByTargetUrl?targetUrl=${currentUrl}`,
  // });
  
  // Remove useFetch for guideDetails
  const { guideDetails } = useGuideDetails();
    //console.log(guideDetails, "GuideData");
  useEffect(() => {
    if (guideDetails && guideDetails.data && guideDetails.data.length > 0) {
      setResponse(prev => ({
        ...prev,
        data: guideDetails.data,
        loading: guideDetails.loading,
        error: guideDetails.error,
      }));
    }
  }, [guideDetails]);


  const tours = useMemo(() => {
    return (
      (guideDetails?.data as any[]).find(
        (item) => (item.GuideType === "Tour" && item.GuideStep?.length>0) // Removing Equals condition as we have page targets
      ) || {}
    );
  }, [window.location.href, response]);


  const data =isFromAi?guide: (tours ? tours : []);
  const totalSteps = data ? data.GuideStep?.length : "";
  const showAfter = data?.GuideStep?.[0]?.Advanced?.ShowAfter || "0s";
  const showDelay = parseInt(showAfter) * 1000;
  const [thresholdValue, setThresholdValue] = useState(0);
  useEffect(() => {
    const apiThreshold = data?.GuideStep?.[currentStep]?.Advanced?.OnScrollDelay || "";
    setThresholdValue(apiThreshold);
  }, [data]);


  const onScrollDelay = data?.GuideStep?.[currentStep]?.Advanced?.OnScrollDelay || "";

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleRestart = () => {
    // Reset to the first step of the tour
    setCurrentStep(0);

    // Track user engagement for restart action
    trackUserEngagement(
      "button-click",
      userData,
      data,
      browser,
      version,
      "restart",
      data?.GuideStep?.[currentStep]?.StepTitle,
      0,
      0
    );

    // Reset UI states based on the first step type
    const firstStepType = data?.GuideStep?.[0]?.StepType;
    setAnnouncementUserView(false);
    setBannerUserView(false);
    setTooltipUserView(false);
    setHotspotUserView(false);

    if (firstStepType === "Announcement") {
      setAnnouncementUserView(true);
    } else if (firstStepType === "Banner") {
      setBannerUserView(true);
    } else if (firstStepType === "Tooltip") {
      setTooltipUserView(true);
      setShowTooltip(true);
    } else if (firstStepType === "Hotspot") {
      setHotspotUserView(true);
    }
  };

  const handleContinue = () => {
      if (data?.GuideType != "Tour") {
          if (currentStep < totalSteps - 1) {
              setCurrentStep(currentStep + 1);
          }
      }
      else
      {
        if (currentStep === data?.GuideStep?.length - 1) {
          // Only show feedback popup if feedbackInfoState is true
          if (feedbackInfoState === true) {
            setShowFeedbackPopup(true);
          } else {
            setShowFeedbackPopup(false);
          }
        } else {
          setCurrentStep(currentStep + 1);
        }
      }
  };

  const handleDontShowAgain = () => {
    setAnchorEl(null);
  };

  const handleStepChange = (step: number) => {
    setCurrentStep(step);
  };

  const progress = ((currentStep + 1) / totalSteps) * 100;
  // Define the type for the guide objects
interface Guide {
  GuideId: string;
  DontShowAgain: boolean;
  GuideType: string;
}
  const [tourPopup, setTourPopup] = useState(false);
useEffect(() => {
  const handleStorageChange = () => {
    const storedGuides: Guide[] = JSON.parse(localStorage.getItem('closedGuides_/') || '[]');
    const guideClosed = storedGuides.some((guide) => guide.GuideId === data.GuideId && guide.DontShowAgain === true && guide.GuideType === "Tour");
    setIsGuideClosed(guideClosed);
    if (!isFromAi && response && data?.GuideStep?.length > 0 && guideClosed) {
      setAnchorEl(null);
      setPopupVisibility(prevState => ({
        ...prevState,
        [data?.GuideId]: false,  // Mark this guide as closed in popupVisibility
    }));
    } else if (data?.GuideStep?.length > 0) {
      setAnchorEl(document.body);
    }
  };

  handleStorageChange();
  window.addEventListener('storage', handleStorageChange);
  return () => {
    window.removeEventListener('storage', handleStorageChange);
  };
}, [response, data?.GuideId, data?.GuideStep?.length]);


// const handleClose = () => {
//   setAnchorEl(null);
//   const storedGuides = JSON.parse(localStorage.getItem('closedGuides_/') || '[]');
//   const updatedGuides = [
//     ...storedGuides,
//     {
//       GuideId: data.GuideId,
//       DontShowAgain: true
//     }
//   ];
//   localStorage.setItem('closedGuides_/', JSON.stringify(updatedGuides));
// };



  const handleClose = () => {
    if (isFromAi) {
      // Only show feedback popup if feedbackInfoState is true
      if (feedbackInfoState === true) {
        setShowFeedbackPopup(true);
      } else {
        setShowFeedbackPopup(false);
      }
      setCurrentStep(0);
      setIsOpen(true);
      setGuide(null);
      setIsFromAi(false);
    } else {
      setAnchorEl(null);
    }
    const body = document.querySelector("body") as HTMLElement;
        if (body) {

            body.style.setProperty("padding-top", "0px", "important");
            body.style.setProperty("max-height", "initial", "important");
        }
    setShowTooltip(false);
    const hotspotElement = document.getElementById(`hotspotBlink${data?.GuideId}`);
    if (hotspotElement) {
    hotspotElement.remove();
    }
    setPopupVisibility(prevState => ({
      ...prevState,
      [data.GuideId]: false,  // Set visibility of the current guide (by GuideId) to false
  }));
  const storedGuides: Guide[] = JSON.parse(localStorage.getItem("closedGuides_/") || "[]");

  var isAnnCloseSaved = storedGuides.some(
    (guide) => guide.GuideId === data.GuideId && guide.DontShowAgain === true && guide?.GuideType?.toLowerCase() === 'tour'
    );
    if (!isAnnCloseSaved)
    {
        const updatedGuides = [
            ...storedGuides,
            {
                GuideId: data.GuideId,
                DontShowAgain: true,
                GuideType: data.GuideType
            },
        ];
        if (!isFromAi) {
        localStorage.setItem("closedGuides_/", JSON.stringify(updatedGuides));
      }
    }

};

  const handleCloseAnnouncement = async () => {
    const newData = { ...response.data.GuideDetails, Visited: true, VisitedDate: new Date().toISOString() };
    try {
      const res = await POST("/Guide/Updateguide", newData);
      setAnchorEl(null);
    } catch (error) {

    }
  };
  const isBase64 = (str: string) => {
    return str.startsWith("data:image/");
  };
  const finalImageUrl =
  tours?.GuideStep?.[currentStep-1]?.ImageProperties?.map((imgProp:any) =>
    imgProp.CustomImage.map((uploadedImage:any) => uploadedImage.Url)
  ) || [];

const previousButtonLabel =
  tours?.GuideStep?.[currentStep]?.ButtonSection?.[0]?.CustomButtons?.[0]?.ButtonName || "Previous";

const continueButtonLabel =
  tours?.GuideStep?.[currentStep]?.ButtonSection?.[0]?.CustomButtons?.[1]?.ButtonName || "Continue";


  const imageUrl = data?.GuideStep?.[currentStep]?.ImageProperties?.CustomImage?.[currentStep]?.Url || "";


  const videoUrl = data?.GuideStep?.[currentStep]?.VideoEmbedCode;
  const textFieldProperties = data?.GuideStep?.[currentStep]?.TextFieldProperties;
  const imageProperties = data?.GuideStep?.[currentStep]?.ImageProperties;
  const modalProperties = data?.GuideStep?.[currentStep]?.Modal;
  const canvasProperties = data?.GuideStep?.[currentStep]?.Canvas || {};
  const html_Snippet = data?.GuideStep?.[currentStep]?.HtmlSnippet;
  const customButtons = data?.GuideStep?.[currentStep]?.CustomButton || [];
    const previousButtonStyles = data?.GuideStep?.[currentStep]?.CustomButton?.[0]?.ButtonProperties;
    const continueButtonStyles = data?.GuideStep?.[currentStep]?.CustomButton?.[0]?.ButtonProperties;
    const Overlayvalue = data?.GuideStep?.[currentStep]?.Overlay;
    const [a, setAnnouncementUserView] = useState(false);
    const [b, setBannerUserView] = useState(false);
    const [t, setTooltipUserView] = useState(false);
    const [h, setHotspotUserView] = useState(false);
    const stepType = data?.GuideStep?.[currentStep]?.StepType;
    useEffect(() => {
      trackUserEngagement("guide-view", userData, guideDetails.data[0], browser,version,"tourview",data?.GuideStep?.[0]?.StepTitle,0,0);

        setAnnouncementUserView(false);
        setBannerUserView(false);
        setTooltipUserView(false);
        setHotspotUserView(false);

        // Set the correct preview based on stepType
        if (stepType === "Announcement") {
          setAnnouncementUserView(true);

        } else if (stepType === "Banner") {
          setBannerUserView(true);


        } else if (stepType === "Tooltip") {
          setTooltipUserView(true);
          setShowTooltip(true);


        } else if (stepType === "Hotspot"){
          setHotspotUserView(true);


        }
    }, [stepType, currentStep]);

    useEffect(() => {
      trackUserEngagement("guide-view", userData, guideDetails.data[0], browser,version,"tourview",data?.GuideStep?.[0]?.StepTitle,0,0);

    },[]);
    const xpathUrl = data?.TargetUrl;

    const stepData = data?.GuideStep?.[currentStep];

    const steps = stepData && typeof stepData === "object"
    ? [stepData].map((step: any) => ({
        stepTitle: step.StepTitle || "",
        xpath: step.ElementPath,
        content: step.TextFieldProperties?.[0]?.Text || "",
        imageUrl: step.ImageProperties?.[0]?.CustomImage?.[0]?.Url || "",
        buttonData: step.ButtonSection?.[0]?.CustomButtons || [],
        targetUrl: step.StepTargetURL || xpathUrl || "", // Use individual step URL if available, fallback to guide URL
        overlay: step.Overlay,
        positionXAxisOffset: step.Position?.XAxisOffset,
        positionYAxisOffset: step.Position?.YAxisOffset,
        canvas: step.Canvas,
        modal: step.Modal,
        imageproperties: step.ImageProperties?.[0]?.CustomImage?.[0] || "",
        autoposition: step.AutoPosition,
        elementclick: step.Design?.GotoNext,
        PossibleElementPath: step?.PossibleElementPath || "",
    }))
    : [];

    const tooltipConfig = data?.GuideStep?.[currentStep]?.Tooltip || {};
    const getHotspotSteps = (step: any) => {
        return {
            xpath: step?.ElementPath,
            hotspotProperties: {
                size: step?.Hotspot?.Size || "10",
                color: step?.Hotspot?.Color || "#f24545",
                type: step?.Hotspot?.Type || "circle",
                showUpon: step?.Hotspot?.ShowUpon || "Hovering Hotspot",
                showByDefault: step?.Hotspot?.ShowByDefault || false,
                stopAnimation: step?.Hotspot?.StopAnimation || false,     
                pulseAnimation: step?.Hotspot?.PulseAnimation || false,
                position: step?.Hotspot?.HotspotPosition || { XOffset: "10", YOffset: "10" },
            },
            targetUrl: step?.StepTargetURL || data?.TargetUrl || "", // Use individual step URL if available, fallback to guide URL
            guideId: data?.GuideId,
        }
    }
    const [popupVisibility, setPopupVisibility] = useState<Record<string, boolean>>({});
  // Feedback popup state and handlers
    const handleFeedbackClose = () => setShowFeedbackPopup(false);
    const handleFeedback = async (helpful: boolean, message?: string) => {
        // Example: get user and org info from localStorage or context
        const UserId = localStorage.getItem('UserId') || '';
        const OrganizationId = localStorage.getItem('OrganizationId') || '';
        const UserName = localStorage.getItem('UserName') || '';
        const FeedbackId = newGuid();
        const now = new Date().toISOString();
        const payload: FeedbackPayload = {
            FeedbackId,
            UserId,
            OrganizationId,
            Message: helpful ? 'Thumbs Up' : 'Thumbs Down',
            FeedbackFile: '',
            Rating: helpful ? '5' : '1',
            CreatedDate: now,
            UserName,
            RequestDescription: '',
            RequestedTime: now,
            WrittenFeedback: message ||"",
            UserFeedback:helpful ? 'Positive' : 'Negative',
        };
        try {
            await sendFeedback(payload);
        } catch (e) {
            // Optionally handle error
        }
        setShowFeedbackPopup(false);
    };

    useEffect(() => {
        if (guideDetails?.data?.length > 0) {
          const initialVisibility = guideDetails.data.reduce((acc: Record<string, boolean>, guide: any) => {
            acc[guide.GuideId] = guide.GuideStep?.[currentStep]?.Hotspot?.ShowByDefault || false; // All popups visible initially
            return acc;
          }, {});
          setPopupVisibility(initialVisibility);
        }
    }, [guideDetails]);
    const getCurrentStep = (data:string) =>{
			//setCurrentStep(data);
		}

    const handlePopupVisible = (visible: boolean) => {
        // Handle popup visibility changes
        //console.log("Popup visibility changed:", visible);
    }

        // Scheduled publishing check for tours (after all hooks)
        if (data && !isFromAi && !getPublishWindowStatus(data)) {
            return null;
        }
        return (
            <div >

                {a && anchorEl && !isGuideClosed && stepType === "Announcement" &&(

                    <Box sx={{ zIndex: 1000 }}>
                        <AnnouncementPopup
                            rectData=""
                            rectDataLeft=""
                            data={data}
                            anchorEl={anchorEl}
                            onClose={handleClose}
                            onPrevious={handlePrevious}
                            onContinue={handleContinue}
                            onRestart={handleRestart}
                            onDontShowAgain={handleDontShowAgain}
                            title={data?.GuideStep?.[currentStep]?.StepTitle}
                            text={
                                data?.GuideStep?.[currentStep]?.TextFieldProperties
                                    ?.map((field: any) => field.Text)
                                    .filter((text: any) => text) // Remove undefined or null values
                                    .join(" ") || "" // Combine into a single string with spaces
                            }
                            imageUrl={finalImageUrl || ""}
                            videoUrl={videoUrl}
                            previousButtonLabel={previousButtonLabel}
                            continueButtonLabel={continueButtonLabel}
                            currentStep={currentStep+1}
                            totalSteps={totalSteps}
                            progress={progress}
                            textFieldProperties={data?.GuideStep?.[currentStep]?.TextFieldProperties || []}
                            imageProperties={data?.GuideStep?.[currentStep]?.ImageProperties || []}
                            customButton={
                                data?.GuideStep?.[currentStep]?.ButtonSection?.flatMap((section: any) =>
                                    section.CustomButtons.map((button: any) => ({
                                        ...button,
                                        ContainerId: section.Id,
                                    }))
                                ) || []
                            }
                            modalProperties={data?.GuideStep?.[currentStep]?.Modal || {}}
                            canvasProperties={data?.GuideStep?.[currentStep]?.Canvas || {}}
                            htmlSnippet={data?.GuideStep?.[currentStep]?.TextFieldProperties?.[0]?.Text || ""}
                            OverlayValue={Overlayvalue}
                            previousButtonStyles={previousButtonStyles}
                            continueButtonStyles={continueButtonStyles}
                            hotspotProperties=""
                            selectedOption={data?.GuideStep?.[currentStep]?.Tooltip.ProgressTemplate}
                            enableProgress={data?.GuideStep?.[currentStep]?.Tooltip.EnableProgress}
                        />
                    </Box>
                )}



                {b && anchorEl && !isGuideClosed &&  stepType === "Banner" &&  (
              <BannerStepUserView

                        data={data}
                        currentStep={currentStep}
                        setCurrentStep={setCurrentStep}
                        onClose={handleClose}
                        onRestart={handleRestart}
                        selectedOption={data?.GuideStep?.[currentStep]?.Tooltip.ProgressTemplate}
                        progress={progress}

                    />

                )}



                {t && showTooltip && anchorEl && !isGuideClosed && stepType === "Tooltip" &&  (
              <TooltipGuide
              key={currentStep}
                        steps={steps}
                        tooltipConfig={tooltipConfig}
                        onClose={handleClose}
                        currentStep={currentStep}
                setCurrentStep={setCurrentStep}
                        data={data}
                        getCurrentStep={getCurrentStep}
                        isFromAi={isFromAi}
                onRestart={handleRestart}
                    />
                )}


                {h && anchorEl && !isGuideClosed && stepType === "Hotspot" &&   (
                    <Hotspot
                        guideStep={getHotspotSteps(data?.GuideStep?.[currentStep])}
                        onPopupVisible={handlePopupVisible}
                        setCurrentStep={setCurrentStep}
                        key={currentStep}
                        guide={data}
                        guides={guideDetails}
                        currentStep={currentStep}
                        handleClose={handleClose}
                        handleDontShowAgain={handleDontShowAgain}
                        popupVisibility={popupVisibility}
                setPopupVisibility={setPopupVisibility}
                        guideDetails={guideDetails}
                        onContinue={handleContinue}
                        onRestart={handleRestart}
                    />
                )}

                {showFeedbackPopup && (
                <FeedbackPopup open={showFeedbackPopup} onClose={handleFeedbackClose} onFeedback={handleFeedback} />
            )}

            </div>
        );
    };

export default TourUserView;
