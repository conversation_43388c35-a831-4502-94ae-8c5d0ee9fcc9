{"name": "quickado<PERSON>er", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@microsoft/signalr": "^8.0.7", "@mui/icons-material": "^5.15.20", "@mui/material": "^5.15.20", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.100", "@types/react": "^18.3.23", "@types/react-bootstrap": "^1.0.1", "@types/react-dom": "^18.3.0", "axios": "^1.8.2", "css-loader": "^7.1.2", "dayjs": "^1.11.13", "dompurify": "^3.2.6", "html-react-parser": "^5.2.5", "perfect-scrollbar": "^1.5.6", "react": "^18.3.1", "react-bootstrap": "^2.10.10", "react-dom": "^18.3.1", "react-draggable": "^4.4.6", "react-perfect-scrollbar": "^1.5.8", "react-router-dom": "^6.23.1", "react-scripts": "^5.0.1", "sass": "^1.86.1", "sass-loader": "^16.0.5", "style-loader": "^4.0.0", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "env-cmd -f .env.dev craco start", "build": "rm -rf build && env-cmd -f .env.prod craco build", "build-ts": "tsc public/contentScript.ts public/background.ts --outDir public", "build:dev": "env-cmd -f .env.dev craco build", "build:cloud": "env-cmd -f .env.cloud craco build", "build:production": "env-cmd -f .env.prod craco build", "build:qa": "env-cmd -f .env.qa craco build", "build:uat": "env-cmd -f .env.uat craco build", "build:development": "env-cmd -f .env.development craco build", "copy-scripts": "cp public/contentScript.js build/ && cp public/background.js build/", "test": "craco test", "eject": "craco eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "description": "This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app).", "main": "index.js", "author": "", "license": "ISC", "devDependencies": {"@craco/craco": "^7.1.0", "@types/axios": "^0.14.0", "@types/chrome": "^0.0.214", "copy-webpack-plugin": "^12.0.2", "dotenv-webpack": "^8.1.0", "env-cmd": "^10.1.0", "webpack-cli": "^5.1.4"}}