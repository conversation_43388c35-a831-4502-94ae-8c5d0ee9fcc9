import React, { useState, useEffect } from 'react';

import {
  Box,
  Paper,
  Typography,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  LinearProgress,
  Chip,
  Tooltip,
  Alert,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  PlayArrow,
  Stop,
  Refresh,
  CheckCircle,
  Error as ErrorIcon,
  Schedule,
  Close,
  Visibility,
  VisibilityOff,
  Clear
} from '@mui/icons-material';
import userApiService from '../../service/APIService';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import workAgentSignalRService from '../../services/SignalRService';
import { GetAgentById } from '../Services/AgentService';

// Types
export type TaskAction = 'click' | 'fill' | 'select' | 'dropdown' | 'radio' | 'daterange' | 'dateinput' | 'fetch' | 'hover' | 'custom' | 'focusout' | 'opendropdown' | 'multiselect';
export type TaskStatus = 'pending' | 'running' | 'completed' | 'failed' | 'skipped';

// DomAction interface to match API response
export interface DomAction {
  XPath: string;
  Action: string;
  Value?: string | DateRangeValue;
  Name?: string; // Element name attribute for fallback (2nd priority)
  LabelName?: string; // For fallback element finding (3rd priority)
  Selector?: string; // CSS selector for fallback (4th priority)
  CssSelector?: string; // Additional CSS selector for fallback (5th priority)
}

// Interface for DOM actions with binding data
export interface DomActionWithBinding {
  labelName: string;
  value: string;
  xpath: string;
  type: string;
  name?: string; // Element name attribute for fallback (2nd priority)
  selector?: string; // CSS selector for fallback (4th priority)
  cssselector?: string; // Additional CSS selector for fallback (5th priority)
}

// Interface for date range value structure
export interface DateRangeValue {
  start: string;
  end: string;
}

dayjs.extend(customParseFormat);

// Helper function to check if value is DateRangeValue
const isDateRangeValue = (value: any): value is DateRangeValue => {
  return value && typeof value === 'object' && 'start' in value && 'end' in value;
};


function parseDateRange(value: any): any {
  if (typeof value === 'string') {
    const match = value.match(/^(\d{2}-\d{2}-\d{4})\s*(?:to|-)\s*(\d{2}-\d{2}-\d{4})$/i);
    if (match) {
      return {
        start: match[1],
        end: match[2]
      };
    }
  }
  return value;
}


function normalizeDate(input: string): string {
  const parsed = dayjs(input, [
    'DD-MM-YYYY', 'DD/MM/YYYY', 'MM/DD/YYYY',
    'YYYY-MM-DD', 'YYYY/MM/DD', 'DD MMM YYYY',
  ], true);
  return parsed.isValid() ? parsed.format('DD/MM/YYYY') : '';
}

// Helper function to get string value from DomAction.Value
const getStringValue = (value: string | DateRangeValue | undefined): string => {
  if (!value) return '';
  if (typeof value === 'string') return value;
  if (isDateRangeValue(value)) {
    // Convert DateRangeValue to formatted date range string
    const startDate = new Date(value.start);
    const endDate = new Date(value.end);
    const formatDate = (date: Date) => {
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      return `${day}/${month}/${year}`;
    };
    return `${formatDate(startDate)} - ${formatDate(endDate)}`;
  }
  return '';
};

// Helper function to check if DOM element already has a value
const checkElementHasValue = (xpath: string, elementType: string, elementName?: string, labelName?: string, selector?: string, cssselector?: string): boolean => {
  try {
    const element = XPathUtils.findElementWithFallback(xpath, elementName, labelName, selector, cssselector);

    if (!element) {
      return false;
    }

    const type = elementType.toLowerCase();
    let hasValue = false;
    let currentValue = '';

    switch (type) {
      case 'text':
      case 'number':
      case 'textarea':
        const inputElement = element as HTMLInputElement | HTMLTextAreaElement;
        currentValue = inputElement.value?.trim() || '';
        hasValue = currentValue.length < 0;
        break;

      case 'dropdown':
      case 'select':
      case 'custom_dropdown':
        const selectElement = element as HTMLSelectElement;
        if (selectElement.tagName.toLowerCase() === 'select') {
          currentValue = selectElement.value?.trim() || '';
          hasValue = currentValue.length < 0 && currentValue !== '' && currentValue !== 'null';
        } else {
          const displayElement = element.querySelector('.e-input-value, .e-ddl-value, input') as HTMLInputElement;
          if (displayElement) {
            currentValue = displayElement.value?.trim() || displayElement.textContent?.trim() || '';
            hasValue = currentValue.length < 0;
          }
        }
        break;

      case 'daterange':
      case 'date':
      case 'dateinput':
        const dateInput = element as HTMLInputElement;
        currentValue = dateInput.value?.trim() || '';
        hasValue = currentValue.length < 0;
        break;

      case 'radio':
        const radioInput = element as HTMLInputElement;
        if (radioInput.type === 'radio') {
          hasValue = radioInput.checked;
          currentValue = hasValue ? radioInput.value : '';
        } else {
          const checkedRadio = element.querySelector('input[type="radio"]:checked') as HTMLInputElement;
          hasValue = !!checkedRadio;
          currentValue = checkedRadio?.value || '';
        }
        break;

      case 'checkbox':
        const checkboxInput = element as HTMLInputElement;
        hasValue = checkboxInput.checked;
        currentValue = hasValue ? 'checked' : '';
        break;

      case 'multiselect':
        currentValue = (element as any).value?.trim() || element.textContent?.trim() || '';
        hasValue = currentValue.length < 0;
        break;

      default:
        // For unknown types, check if element has any text content or value
        currentValue = (element as any).value?.trim() || element.textContent?.trim() || '';
        hasValue = currentValue.length > 0;
        break;
    }

    return hasValue;

  } catch (error) {
    return false;
  }
};

// Function to deduplicate dropdown elements by labelName and prioritize dropdown types
const deduplicateDropdownElements = (domActionsWithBinding: DomActionWithBinding[]): DomActionWithBinding[] => {
  const elementMap = new Map<string, DomActionWithBinding>();

  // Group elements by labelName
  const groupedByLabel = new Map<string, DomActionWithBinding[]>();

  domActionsWithBinding.forEach((domAction) => {
    const key = domAction.labelName.toLowerCase().trim();
    if (!groupedByLabel.has(key)) {
      groupedByLabel.set(key, []);
    }
    groupedByLabel.get(key)!.push(domAction);
  });

  // For each group, select the best element based on type priority
  groupedByLabel.forEach((elements, labelKey) => {
    if (elements.length === 1) {
      // Only one element, keep it
      elementMap.set(labelKey, elements[0]);
    } else {
      // Multiple elements with same label, prioritize dropdown types
      const priorityOrder = ['dropdown', 'custom_dropdown', 'select', 'text', 'unknown'];

      let bestElement = elements[0];
      let bestPriority = priorityOrder.indexOf(bestElement.type.toLowerCase());
      if (bestPriority === -1) bestPriority = 999; // Unknown types get lowest priority

      elements.forEach((element) => {
        const elementPriority = priorityOrder.indexOf(element.type.toLowerCase());
        const actualPriority = elementPriority === -1 ? 999 : elementPriority;

        if (actualPriority < bestPriority) {
          bestElement = element;
          bestPriority = actualPriority;
        }
      });

      elementMap.set(labelKey, bestElement);
    }
  });

  // Convert back to array
  const deduplicatedElements = Array.from(elementMap.values());
  return deduplicatedElements;
};



// Function to process DOM actions with binding data and determine actions based on type
const processDomActionsWithBinding = (domActionsWithBinding: DomActionWithBinding[]): DomAction[] => {
  const processedActions: DomAction[] = [];

  // First, deduplicate dropdown elements by labelName and prioritize dropdown types
  const deduplicatedActions = deduplicateDropdownElements(domActionsWithBinding);

  deduplicatedActions.forEach((domAction: DomActionWithBinding) => {
    // Skip elements with empty xpath (unmapped fields)
    if (!domAction.xpath || domAction.xpath.trim() === '') {
      console.warn(`⚠️ Skipping element with empty xpath: ${domAction.labelName}`);
      return;
    }

    if(domAction.type !== "click" && domAction.type !== "hover"){
    const elementHasValue = checkElementHasValue(domAction.xpath, domAction.type, domAction.name, domAction.labelName, domAction.selector, domAction.cssselector);

    if (elementHasValue) {
      return;
    }
  }


    let action: string;
    let value: string | DateRangeValue | undefined = domAction.value;

    // Determine action based on type using switch case
    switch (domAction.type.toLowerCase()) {
      case 'text':
      case 'number':
        action = 'fill';
        break;
      case 'textarea':
        action = 'fill';
        break;
      case 'dropdown':
      case 'select':
      case 'custom_dropdown':
        action = 'dropdown';
        break;
      case 'radio':
        action = 'radio';
        break;
      case 'daterange':
      case 'date':
        action = 'daterange';
        // Convert date range value to proper format if needed
        if (domAction.value.includes(' - ')) {
          const [startDate, endDate] = domAction.value.split(' - ').map((d: string) => d.trim());
          value = { start: startDate, end: endDate };
        }
        break;
      case 'checkbox':
        action = 'click';
        break;
      case 'multiselect':
        action = 'multiselect';
        break;
      case 'button':
        action = 'click';
        break;
      case 'hover':
        action = 'hover';
        break;
      case 'unknown':
        // For unknown types, try to determine based on labelName or skip
        console.warn(`⚠️ Unknown type for ${domAction.labelName}, defaulting to click action`);
        action = 'click';
        break;
      case 'click':
        action = 'click';
        break;
      default:
        // Default to fill for any unhandled types
        console.warn(`⚠️ Unhandled type '${domAction.type}' for ${domAction.labelName}, defaulting to fill action`);
        action = 'fill';
        break;
    }

    // Create DomAction object
    const processedAction: DomAction = {
      XPath: domAction.xpath,
      Action: action,
      Value: value,
      Name: domAction.name,
      LabelName: domAction.labelName,
      Selector: domAction.selector,
      CssSelector: domAction.cssselector
    };

    processedActions.push(processedAction);

    console.log(`✅ Processed ${domAction.labelName}:`, {
      type: domAction.type,
      action: action,
      xpath: domAction.xpath,
      value: value
    });
  });

  return processedActions;
};

// Export function to be used by SignalR service or other components
export const createAutomationTasksFromBindingData = (bindingData: DomActionWithBinding[]): AutomationTask[] => {

  // Process the binding data to determine actions
  const processedDomActions = processDomActionsWithBinding(bindingData);

  // Convert to automation tasks
  const automationTasks = createTasksFromDomActions(processedDomActions);

  return automationTasks;
};

// Function to convert DomActions to AutomationTasks
const createTasksFromDomActions = (domActions: DomAction[]): AutomationTask[] => {
  const tasks: AutomationTask[] = [];

  domActions.forEach((domAction, index) => {
    const action = domAction.Action.toLowerCase() as TaskAction;
    const taskId = `api-task-${index + 1}`;
    const taskName = `${domAction.Action} - ${domAction.XPath.substring(0, 50)}...`;

    // Removed wait tasks between actions for faster execution

    // Create appropriate task based on action type
    switch (action) {
      case 'click':
        tasks.push(TaskQueue.createClickTask(taskId, taskName, domAction.XPath, {
          elementName: domAction.Name,
          labelName: domAction.LabelName,
          selector: domAction.Selector,
          cssselector: domAction.CssSelector
        }));
        break;
      case 'fill':
        // Add 5-second wait before every fill task
        // tasks.push(TaskQueue.createWaitTask(
        //   `wait-before-fill-${index}`,
        //   `Wait 5 seconds before filling field`,
        //   3000
        // ));
        tasks.push(TaskQueue.createFillTask(taskId, taskName, domAction.XPath, getStringValue(domAction.Value), {
          elementName: domAction.Name,
          labelName: domAction.LabelName,
          selector: domAction.Selector,
          cssselector: domAction.CssSelector
        }));
        break;
      case 'dropdown':
        // Create separate tasks with unique IDs to ensure proper sequencing
        // IMPORTANT: Both tasks use the SAME xpath (domAction.XPath) for opening and filling dropdown
        const openDropdownTaskId = `${taskId}_open`;
        const selectDropdownTaskId = `${taskId}_select`;

        // 1. Opening dropdown xpath - uses domAction.XPath
        tasks.push(TaskQueue.createOpenDropdownTask(openDropdownTaskId, `${taskName} - Open`, domAction.XPath, getStringValue(domAction.Value), {
          elementName: domAction.Name,
          labelName: domAction.LabelName,
          selector: domAction.Selector,
          cssselector: domAction.CssSelector
        }));
        // 2. Filling the selected value xpath - uses the SAME domAction.XPath with type 'dropdown'
        tasks.push(TaskQueue.createDropdownTask(selectDropdownTaskId, `${taskName} - Select`, domAction.XPath, getStringValue(domAction.Value), {
          elementName: domAction.Name,
          labelName: domAction.LabelName,
          selector: domAction.Selector,
          cssselector: domAction.CssSelector
        }));
        break;
      case 'radio':
        tasks.push(TaskQueue.createRadioTask(taskId, taskName, domAction.XPath, getStringValue(domAction.Value), {
          elementName: domAction.Name,
          labelName: domAction.LabelName,
          selector: domAction.Selector,
          cssselector: domAction.CssSelector
        }));
        break;
       case 'multiselect':
        tasks.push(TaskQueue.createMultiSelectTask(taskId, `${taskName} - Select`, domAction.XPath, getStringValue(domAction.Value), {
          elementName: domAction.Name,
          labelName: domAction.LabelName,
          selector: domAction.Selector,
          cssselector: domAction.CssSelector
        }));
        break;
      case 'hover':
        tasks.push(TaskQueue.createHoverTask(taskId, taskName, domAction.XPath, {
          elementName: domAction.Name,
          labelName: domAction.LabelName,
          selector: domAction.Selector,
          cssselector: domAction.CssSelector
        }));
        break;
      case 'opendropdown':
        tasks.push(TaskQueue.createOpenDropdownTask(taskId, taskName, domAction.XPath, getStringValue(domAction.Value), {
          elementName: domAction.Name,
          labelName: domAction.LabelName,
          selector: domAction.Selector,
          cssselector: domAction.CssSelector
        }));
        break;
case 'daterange':
  let startDate: string = '';
  let endDate: string = '';
  let value = parseDateRange(domAction.Value);

  if (isDateRangeValue(value)) {
    const startDateObj = value.start;
    const endDateObj = value.end;

    startDate = normalizeDate(startDateObj || '');
    endDate = normalizeDate(endDateObj || '');
  } else {
    const dateValue = getStringValue(domAction.Value);
    const normalizedDateValue = normalizeDate(dateValue || '');

    if (normalizedDateValue) {
      // Use the same normalized date for both start and end
      startDate = normalizedDateValue;
      endDate = normalizedDateValue;
    }
  }

  const dateTask = TaskQueue.createDateRangeTask(taskId, taskName, domAction.XPath, startDate, endDate, {
    elementName: domAction.Name,
    labelName: domAction.LabelName,
    selector: domAction.Selector,
    cssselector: domAction.CssSelector,
    metadata: {
      dateRangeType: 'binding-only',
      bindingFormat: startDate && endDate ? `${startDate} - ${endDate}` : (startDate || endDate)
    }
  });

  console.log(`📅 Created date task with value: ${dateTask.value}`);
  tasks.push(dateTask);
  break;
case 'dateinput':
        let dateInputValue: string;

        if (isDateRangeValue(domAction.Value)) {
          // Convert DateRangeValue to formatted string
          dateInputValue = getStringValue(domAction.Value);
        } else {
          dateInputValue = getStringValue(domAction.Value);
        }

        tasks.push(TaskQueue.createFillTask(taskId, taskName, domAction.XPath, dateInputValue, {
          elementName: domAction.Name,
          labelName: domAction.LabelName,
          selector: domAction.Selector,
          cssselector: domAction.CssSelector
        }));
        break;
      default:
        // For unsupported actions, create a click task as fallback
        tasks.push(TaskQueue.createClickTask(taskId, taskName, domAction.XPath, {
          elementName: domAction.Name,
          labelName: domAction.LabelName,
          selector: domAction.Selector,
          cssselector: domAction.CssSelector
        }));
        break;
    }
  });

  return tasks;
};

export interface AutomationTask {
  id: string;
  name: string;
  xpath: string;
  action: TaskAction;
  value?: string;
  delay?: number;
  retryAttempts?: number;
  status: TaskStatus;
  error?: string;
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  priority?: number;
  condition?: string;
  skipOnError?: boolean;
  metadata?: Record<string, any>;
  elementName?: string; // Element name attribute for fallback (2nd priority)
  labelName?: string; // For fallback element finding (3rd priority)
  selector?: string; // CSS selector for fallback (4th priority)
  cssselector?: string; // Additional CSS selector for fallback (5th priority)
}

export interface ProgressInfo {
  total: number;
  completed: number;
  failed: number;
  current: number;
  percentage: number;
}

// XPath Utilities with Enhanced Fallback Mechanism
// This class provides element finding with multiple fallback strategies:
// 1. Primary: XPath-based finding with various XPath fallback patterns
// 2. Secondary: Label name-based finding using multiple DOM strategies
// 3. Tertiary: CSS selector-based finding
export class XPathUtils {
  static findElementByXPath(xpath: string): HTMLElement | null {
    try {
      const result = document.evaluate(
        xpath,
        document,
        null,
        XPathResult.FIRST_ORDERED_NODE_TYPE,
        null
      );
      return result.singleNodeValue as HTMLElement;
    } catch (error) {
      console.error('XPath evaluation error:', error);
      return null;
    }
  }

  static findElementWithFallback(xpath: string, elementName?: string, labelName?: string, selector?: string, cssselector?: string): HTMLElement | null {
    console.log(`🔍 Finding element with fallback - XPath: ${xpath}, Name: ${elementName}, Label: ${labelName}, Selector: ${selector}, CssSelector: ${cssselector}`);

    // 1. Try original xpath first
    let element = this.findElementByXPath(xpath);
    if (element) {
      console.log(`✅ Found element using original XPath: ${xpath}`);
      return element;
    }

    // Try XPath variations
    if (xpath.includes("text()=")) {
      const fallbackXpath = xpath.replace(/text\(\)=/g, 'contains(text(),');
      element = this.findElementByXPath(fallbackXpath);
      if (element) {
        console.log(`✅ Found element using modified XPath: ${fallbackXpath}`);
        return element;
      }
    }

    if (xpath.includes("'") && !xpath.includes("contains")) {
      const textMatch = xpath.match(/'([^']+)'/);
      if (textMatch) {
        const text = textMatch[1];
        const containsXpath = `//*[contains(text(),'${text}')]`;
        element = this.findElementByXPath(containsXpath);
        if (element) {
          console.log(`✅ Found element using contains XPath: ${containsXpath}`);
          return element;
        }

        const trimmedText = text.trim();
        if (trimmedText !== text) {
          const trimmedXpath = `//*[contains(text(),'${trimmedText}')]`;
          element = this.findElementByXPath(trimmedXpath);
          if (element) {
            console.log(`✅ Found element using trimmed XPath: ${trimmedXpath}`);
            return element;
          }
        }

        const normalizedText = text.replace(/\s+/g, ' ').trim();
        if (normalizedText !== text) {
          const normalizedXpath = `//*[contains(text(),'${normalizedText}')]`;
          element = this.findElementByXPath(normalizedXpath);
          if (element) {
            console.log(`✅ Found element using normalized XPath: ${normalizedXpath}`);
            return element;
          }
        }

        const words = trimmedText.split(/\s+/).filter(word => word.length > 2);
        for (const word of words) {
          const wordXpath = `//*[contains(text(),'${word}')]`;
          element = this.findElementByXPath(wordXpath);
          if (element) {
            console.log(`✅ Found element using word XPath: ${wordXpath}`);
            return element;
          }
        }
      }
    }

    // 2. Fallback to name-based search
    if (elementName && elementName.trim()) {
      console.log(`🔄 Falling back to name-based search: ${elementName}`);
      element = this.findElementByName(elementName);
      if (element) {
        console.log(`✅ Found element using name: ${elementName}`);
        return element;
      }
    }

    // 3. Fallback to labelName-based search
    if (labelName && labelName.trim()) {
      console.log(`🔄 Falling back to labelName-based search: ${labelName}`);
      element = this.findElementByLabelName(labelName);
      if (element) {
        console.log(`✅ Found element using labelName: ${labelName}`);
        return element;
      }
    }

    // 4. Fallback to selector-based search
    if (selector && selector.trim()) {
      console.log(`🔄 Falling back to selector-based search: ${selector}`);
      element = this.findElementBySelector(selector);
      if (element) {
        console.log(`✅ Found element using selector: ${selector}`);
        return element;
      }
    }

    // 5. Fallback to cssselector-based search
    if (cssselector && cssselector.trim()) {
      console.log(`🔄 Falling back to cssselector-based search: ${cssselector}`);
      element = this.findElementBySelector(cssselector);
      if (element) {
        console.log(`✅ Found element using cssselector: ${cssselector}`);
        return element;
      }
    }

    console.log(`❌ Element not found with any method - XPath: ${xpath}, Name: ${elementName}, Label: ${labelName}, Selector: ${selector}, CssSelector: ${cssselector}`);
    return null;
  }

  static findElementByName(elementName: string): HTMLElement | null {
    const trimmedName = elementName.trim().toLowerCase();

    // Strategy 1: Find by exact name attribute match
    const nameElements = Array.from(document.querySelectorAll(`[name="${elementName}"]`));
    if (nameElements.length > 0) {
      return nameElements[0] as HTMLElement;
    }

    // Strategy 2: Find by case-insensitive name attribute match
    const allNameElements = Array.from(document.querySelectorAll('[name]'));
    for (const element of allNameElements) {
      const name = element.getAttribute('name')?.trim().toLowerCase();
      if (name === trimmedName) {
        return element as HTMLElement;
      }
    }

    // Strategy 3: Find by partial name attribute match
    for (const element of allNameElements) {
      const name = element.getAttribute('name')?.trim().toLowerCase();
      if (name?.includes(trimmedName) || trimmedName.includes(name || '')) {
        return element as HTMLElement;
      }
    }

    // Strategy 4: Find by id that matches the name
    const idElement = document.getElementById(elementName);
    if (idElement) {
      return idElement as HTMLElement;
    }

    // Strategy 5: Find by id case-insensitive match
    const allIdElements = Array.from(document.querySelectorAll('[id]'));
    for (const element of allIdElements) {
      const id = element.getAttribute('id')?.trim().toLowerCase();
      if (id === trimmedName) {
        return element as HTMLElement;
      }
    }

    return null;
  }

  static findElementByLabelName(labelName: string): HTMLElement | null {
    const trimmedLabel = labelName.trim().toLowerCase();

    // Strategy 1: Find by label element's 'for' attribute
    const labels = Array.from(document.querySelectorAll('label'));
    for (const label of labels) {
      if (label.textContent?.trim().toLowerCase() === trimmedLabel) {
        const forAttr = label.getAttribute('for');
        if (forAttr) {
          const targetElement = document.getElementById(forAttr);
          if (targetElement) return targetElement as HTMLElement;
        }

        // If no 'for' attribute, look for nested input
        const nestedInput = label.querySelector('input, textarea, select');
        if (nestedInput) return nestedInput as HTMLElement;
      }
    }

    // Strategy 2: Find by placeholder attribute
    const placeholderElements = Array.from(document.querySelectorAll('[placeholder]'));
    for (const element of placeholderElements) {
      const placeholder = element.getAttribute('placeholder')?.trim().toLowerCase();
      if (placeholder === trimmedLabel) {
        return element as HTMLElement;
      }
    }

    // Strategy 3: Find by aria-label attribute
    const ariaLabelElements = Array.from(document.querySelectorAll('[aria-label]'));
    for (const element of ariaLabelElements) {
      const ariaLabel = element.getAttribute('aria-label')?.trim().toLowerCase();
      if (ariaLabel === trimmedLabel) {
        return element as HTMLElement;
      }
    }

    // Strategy 4: Find by name attribute
    const nameElements = Array.from(document.querySelectorAll('[name]'));
    for (const element of nameElements) {
      const name = element.getAttribute('name')?.trim().toLowerCase();
      if (name === trimmedLabel || name?.includes(trimmedLabel)) {
        return element as HTMLElement;
      }
    }

    // Strategy 5: Find by id attribute (partial match)
    const idElements = Array.from(document.querySelectorAll('[id]'));
    for (const element of idElements) {
      const id = element.getAttribute('id')?.trim().toLowerCase();
      if (id?.includes(trimmedLabel.replace(/\s+/g, '')) || trimmedLabel.replace(/\s+/g, '').includes(id || '')) {
        return element as HTMLElement;
      }
    }

    // Strategy 6: Find by nearby text content
    const allElements = Array.from(document.querySelectorAll('input, textarea, select, [contenteditable="true"]'));
    for (const element of allElements) {
      const htmlElement = element as HTMLElement;

      // Check previous sibling text
      let sibling = htmlElement.previousElementSibling;
      while (sibling) {
        if (sibling.textContent?.trim().toLowerCase().includes(trimmedLabel)) {
          return htmlElement;
        }
        sibling = sibling.previousElementSibling;
      }

      // Check parent element text
      const parent = htmlElement.parentElement;
      if (parent && parent.textContent?.trim().toLowerCase().includes(trimmedLabel)) {
        // Make sure the parent doesn't contain other form elements (to avoid false positives)
        const otherFormElements = parent.querySelectorAll('input, textarea, select');
        if (otherFormElements.length === 1) {
          return htmlElement;
        }
      }
    }

    return null;
  }

  static findElementBySelector(selector: string): HTMLElement | null {
    try {
      const element = document.querySelector(selector);
      return element as HTMLElement;
    } catch (error) {
      console.error(`Invalid CSS selector: ${selector}`, error);
      return null;
    }
  }

  static async waitForElement(xpath: string, timeout: number = 1000, elementName?: string, labelName?: string, selector?: string, cssselector?: string): Promise<HTMLElement | null> {
    const startTime = Date.now();

    while (Date.now() - startTime < timeout) {
      const element = this.findElementWithFallback(xpath, elementName, labelName, selector, cssselector);
      if (element && this.isElementVisible(element)) {
        return element;
      }
      await new Promise(resolve => setTimeout(resolve, 50)); // Reduced from 100ms to 50ms
    }

    return null;
  }

  static isElementVisible(element: HTMLElement): boolean {
    const rect = element.getBoundingClientRect();
    return (
      rect.width > 0 &&
      rect.height > 0 &&
      rect.top >= 0 &&
      rect.left >= 0 &&
      window.getComputedStyle(element).visibility !== 'hidden' &&
      window.getComputedStyle(element).display !== 'none'
    );
  }

  static isElementInteractable(element: HTMLElement): boolean {
    return this.isElementVisible(element) && !element.hasAttribute('disabled');
  }

  // Store highlighted elements for cleanup
  private static highlightedElements: Map<HTMLElement, string> = new Map();

  static highlightElement(element: HTMLElement, duration: number = 1000): void {
    // Store original style for cleanup
    if (!this.highlightedElements.has(element)) {
      this.highlightedElements.set(element, element.style.cssText);
    }

    // Apply highlight styles
    element.style.cssText += `
      outline: 2px solid #ff6b6b !important;
      outline-offset: 2px !important;
      background-color: rgba(255, 107, 107, 0.1) !important;
      transition: all 0.3s ease !important;
    `;

    // Set timeout to remove highlight
    setTimeout(() => {
      this.removeHighlight(element);
    }, duration);
  }

  static removeHighlight(element: HTMLElement): void {
    if (this.highlightedElements.has(element)) {
      const originalStyle = this.highlightedElements.get(element) || '';
      element.style.cssText = originalStyle;
      this.highlightedElements.delete(element);
    }
  }

  static clearAllHighlights(): void {
    this.highlightedElements.forEach((originalStyle, element) => {
      try {
        element.style.cssText = originalStyle;
      } catch (error) {
        // Element might have been removed from DOM
        console.warn('Could not restore style for highlighted element:', error);
      }
    });
    this.highlightedElements.clear();
  }

  // Test function to demonstrate the fallback mechanism
  static testFallbackMechanism(xpath: string, elementName?: string, labelName?: string, selector?: string, cssselector?: string): void {
    console.log(`🧪 Testing fallback mechanism for:`);
    console.log(`   XPath: ${xpath}`);
    console.log(`   ElementName: ${elementName}`);
    console.log(`   LabelName: ${labelName}`);
    console.log(`   Selector: ${selector}`);
    console.log(`   CssSelector: ${cssselector}`);

    const element = this.findElementWithFallback(xpath, elementName, labelName, selector, cssselector);

    if (element) {
      console.log(`✅ Element found successfully!`);
      console.log(`   Tag: ${element.tagName}`);
      console.log(`   ID: ${element.id || 'none'}`);
      console.log(`   Classes: ${element.className || 'none'}`);
      console.log(`   Text: ${element.textContent?.substring(0, 50) || 'none'}`);

      // Highlight the found element
      this.highlightElement(element, 3000);
    } else {
      console.log(`❌ Element not found with any fallback method`);
    }
  }
}

// TaskQueue Class
export class TaskQueue {
  private tasks: AutomationTask[] = [];
  private currentIndex: number = 0;

  addTask(task: AutomationTask): void {
    this.tasks.push(task);
  }

  getNext(): AutomationTask | null {
    if (this.currentIndex < this.tasks.length) {
      return this.tasks[this.currentIndex++];
    }
    return null;
  }

  hasNext(): boolean {
    return this.currentIndex < this.tasks.length;
  }

  getProgress(): ProgressInfo {
    const total = this.tasks.length;
    const completed = this.tasks.filter(t => t.status === 'completed').length;
    const failed = this.tasks.filter(t => t.status === 'failed').length;
    const current = this.currentIndex;
    const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;

    return { total, completed, failed, current, percentage };
  }

  reset(): void {
    this.currentIndex = 0;
    this.tasks.forEach(task => {
      task.status = 'pending';
      task.error = undefined;
      task.startedAt = undefined;
      task.completedAt = undefined;
    });
  }

  clear(): void {
    this.tasks = [];
    this.currentIndex = 0;
  }

  getAllTasks(): AutomationTask[] {
    return this.tasks;
  }

  // Static factory methods
  static createClickTask(id: string, name: string, xpath: string, options?: Partial<AutomationTask>): AutomationTask {
    return {
      id,
      name,
      xpath,
      action: 'click',
      status: 'pending',
      createdAt: new Date(),
      delay: 0, // Removed delay for faster execution
      retryAttempts: 3,
      skipOnError: true, // Continue to next task if element not found
      ...options
    };
  }
  // Add a new task creator for focus out/blur operations
  static createFocusOutTask(id: string, name: string, xpath: string, options?: Partial<AutomationTask>): AutomationTask {
    return {
      id,
      name,
      xpath,
      action: 'focusout',
      status: 'pending',
      createdAt: new Date(),
      delay: 0, // Removed delay for faster execution
      retryAttempts: 2,
      skipOnError: true, // Continue to next task if element not found
      ...options
    };
  }

  static createFillTask(id: string, name: string, xpath: string, value: string, options?: Partial<AutomationTask>): AutomationTask {
    return {
      id,
      name,
      xpath,
      action: 'fill',
      value,
      status: 'pending',
      createdAt: new Date(),
      delay: 0, // Removed delay for faster execution
      retryAttempts: 3,
      skipOnError: true, // Continue to next task if element not found
      ...options
    };
  }

  // createWaitTask method removed - no wait tasks for faster execution

  static createDropdownTask(id: string, name: string, xpath: string, value: string, options?: Partial<AutomationTask>): AutomationTask {
    return {
      id,
      name,
      xpath,
      action: 'dropdown',
      value,
      status: 'pending',
      createdAt: new Date(),
      delay: 0, // Removed delay for faster execution
      retryAttempts: 3,
      skipOnError: true, // Continue to next task if element not found
      ...options
    };
  }


  static createMultiSelectTask(id: string, name: string, xpath: string, value: string, options?: Partial<AutomationTask>): AutomationTask {
    return {
      id,
      name,
      xpath,
      action: 'multiselect',
      value,
      status: 'pending',
      createdAt: new Date(),
      delay: 0, // Removed delay for faster execution
      retryAttempts: 3,
      skipOnError: true,
      ...options
    };
  }


  static createOpenDropdownTask(id: string, name: string, xpath: string, value: string, options?: Partial<AutomationTask>): AutomationTask {
  //   const element = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
  //   if (element) {
  //     ['mousedown', 'mouseup', 'click'].forEach(eventType => {
  //       element.dispatchEvent(new MouseEvent(eventType, { bubbles: true, cancelable: true, view: window }));
  //     });
    
  //   setTimeout(() => {
  //     const listItem = Array.from(document.querySelectorAll('.e-popup li'))
  //       .find((li: any) => li.textContent.trim() === value);

  //     if (listItem) {
  //       ['mouseover', 'mousedown', 'mouseup', 'click'].forEach(eventType => {
  //         listItem.dispatchEvent(new MouseEvent(eventType, { bubbles: true, cancelable: true, view: window }));
  //       });
  //     } else {
  //       console.warn('value option not found');
  //     }
  //   }, 300); // Adjust delay if needed
  // }
    return {
      id,
      name,
      xpath,
      action: 'opendropdown',
      value,
      status: 'pending',
      createdAt: new Date(),
      delay: 0, // Removed delay for faster execution
      retryAttempts: 3,
      skipOnError: true, // Continue to next task if element not found
      ...options
    };

  }


  static createRadioTask(id: string, name: string, xpath: string,value: string, options?: Partial<AutomationTask>): AutomationTask {
    return {
      id,
      name,
      xpath,
      action: 'radio',
      value,
      status: 'pending',
      createdAt: new Date(),
      delay: 0, // Removed delay for faster execution
      retryAttempts: 3,
      skipOnError: true, // Continue to next task if element not found
      ...options
    };
  }


  static createDateRangeTask(
    id: string,
    name: string,
    xpath: string,
    startDate: string,
    endDate: string,
    options?: Partial<AutomationTask>
  ): AutomationTask {
    // Support single dates by allowing empty startDate or endDate
    let taskValue: string;

    if (startDate && endDate) {
      // Both dates provided - full date range
      taskValue = `${startDate}|${endDate}`;
    } else if (startDate && !endDate) {
      // Only start date provided - use single date format
      taskValue = startDate;
    } else if (!startDate && endDate) {
      // Only end date provided - use single date format
      taskValue = endDate;
    } else {
      // Neither date provided - use empty string
      taskValue = '';
    }

    return {
      id,
      name,
      xpath,
      action: "daterange",
      value: taskValue,
      status: "pending",
      createdAt: new Date(),
      delay: 0, // Removed delay for faster execution
      skipOnError: true, // Continue to next task if element not found
      retryAttempts: 3,
      ...options
    };
  }

  /**
   * Convenience method to create a single date task
   * @param id - Task ID
   * @param name - Task name
   * @param xpath - Element XPath
   * @param date - Single date value (e.g., "16/06/2025")
   * @param options - Additional task options
   */
  static createSingleDateTask(
    id: string,
    name: string,
    xpath: string,
    date: string,
    options?: Partial<AutomationTask>
  ): AutomationTask {
    return {
      id,
      name,
      xpath,
      action: "daterange",
      value: date, // Single date value without any pipe separator
      status: "pending",
      createdAt: new Date(),
      delay: 0, // Removed delay for faster execution
      skipOnError: true, // Continue to next task if element not found
      retryAttempts: 3,
      ...options
    };
  }
  static createSeparateDateFieldsTask(id: string, name: string, startDateXpath: string, endDateXpath: string, startDate: string, endDate: string, options?: Partial<AutomationTask>): AutomationTask {
    return {
      id,
      name,
      xpath: startDateXpath,
      action: 'daterange',
      value: `${startDate}|${endDate}`,
      status: 'pending',
      createdAt: new Date(),
      delay: 0, // Removed delay for faster execution
      retryAttempts: 3,
      skipOnError: true, // Continue to next task if element not found
      metadata: {
        dateRangeType: 'separate',
        endDateXpath: endDateXpath
      },
      ...options
    };
  }

  static createHoverTask(id: string, name: string, xpath: string, options?: Partial<AutomationTask>): AutomationTask {
    return {
      id,
      name,
      xpath,
      action: 'hover',
      status: 'pending',
      createdAt: new Date(),
      delay: 0, // Removed delay for faster execution
      retryAttempts: 3,
      skipOnError: true, // Continue to next task if element not found
      ...options
    };
  }

  static createFetchTask(id: string, name: string, xpath: string, options?: Partial<AutomationTask>): AutomationTask {
    return {
      id,
      name,
      xpath,
      action: 'fetch',
      status: 'pending',
      createdAt: new Date(),
      delay: 0, // Removed delay for faster execution
      retryAttempts: 2,
      skipOnError: true, // Continue to next task if element not found
      ...options
    };
  }
}

// Predefined tasks for the specific XPaths (wait tasks removed for faster execution)
const createPredefinedTasks = (): AutomationTask[] => {
  return [
    TaskQueue.createClickTask(
      'leave-attendance',
      'Navigate to Leave & Attendance',
      "//*[text()=' Leave & Attendance ']"
    ),
    TaskQueue.createClickTask(
      'requisitions',
      'Navigate to Requisitions',
      "//*[text()=' Requisitions ']"
    ),
    TaskQueue.createClickTask(
      'leave-application',
      'Navigate to Leave Application',
      "//*[text()='Leave Application']"
    ),
    TaskQueue.createDropdownTask(
      'select-leave-type',
      'Select Leave Type - PL',
      '//*[@id="DropDown1"]/div/input',
      'PL'
    ),
    TaskQueue.createFillTask(
      'fill-reason',
      'Fill sick leave reason in textarea',
      '//*[@id="textArea1"]/div',
      'Went to home for Dussehra festival'
    ),
    TaskQueue.createDateRangeTask(
      'select-date-range',
      'Select Leave Dates (June 3-9, 2025)',
      '//*[@id="Leave Dates New ds1_input"]',
      '03/06/2025',
      '09/06/2025'
    )
  ];
};

// AutomationEngine Class
export class AutomationEngine {
  private isRunning: boolean = false;
  private currentTask: AutomationTask | null = null;
  private onTaskUpdate?: (task: AutomationTask) => void;
  private onComplete?: () => void;
  private onError?: (error: string) => void;

  constructor(
    onTaskUpdate?: (task: AutomationTask) => void,
    onComplete?: () => void,
    onError?: (error: string) => void
  ) {
    this.onTaskUpdate = onTaskUpdate;
    this.onComplete = onComplete;
    this.onError = onError;
  }

  async start(taskQueue: TaskQueue): Promise<void> {
    if (this.isRunning) {
      console.warn('Automation engine is already running');
      return;
    }

    this.isRunning = true;

    try {
      while (taskQueue.hasNext() && this.isRunning) {
        const task = taskQueue.getNext();
        if (!task) break;

        this.currentTask = task;

        // Execute task with retry logic
        let attempts = 0;
        const maxAttempts = task.retryAttempts || 3;
        let lastError: Error | null = null;

        while (attempts < maxAttempts && this.isRunning) {
          try {
            await this.executeTask(task);
            break; // Success, exit retry loop
          } catch (error) {
            attempts++;
            lastError = error instanceof Error ? error : new Error('Unknown error');
            console.warn(`⚠️ Task attempt ${attempts}/${maxAttempts} failed:`, lastError.message);

            if (attempts < maxAttempts) {
              // Add 1-second delay before retry
              await new Promise(resolve => setTimeout(resolve, 1000));
            }
          }
        }

        // If all attempts failed, mark task as failed
        if (attempts >= maxAttempts && lastError) {
          task.status = 'failed';
          task.error = lastError.message;
          task.completedAt = new Date();

          if (this.onTaskUpdate) {
            this.onTaskUpdate(task);
          }

          if (!task.skipOnError) {
            throw lastError;
          }
        }
      }

      // Clear all highlights when automation completes
      XPathUtils.clearAllHighlights();

      if (this.onComplete) {
        this.onComplete();
      }
    } catch (error) {
      console.error('❌ Automation failed:', error);

      // Clear all highlights when automation fails
      XPathUtils.clearAllHighlights();

      if (this.onError) {
        this.onError(error instanceof Error ? error.message : 'Unknown error');
      }
    } finally {
      this.isRunning = false;
      this.currentTask = null;
    }
  }

  stop(): void {
    this.isRunning = false;
    // Clear all highlights when automation is stopped
    XPathUtils.clearAllHighlights();
  }

  isEngineRunning(): boolean {
    return this.isRunning;
  }

  getCurrentTask(): AutomationTask | null {
    return this.currentTask;
  }

  private async performAction(task: AutomationTask): Promise<void> {
    console.log(`📋 Task details:`, {
      id: task.id,
      name: task.name,
      action: task.action,
      xpath: task.xpath,
      value: task.value
    });

    switch (task.action) {
      case 'click':
        await this.performClick(task);
        break;
      case 'fill':
        await this.performFill(task);
        break;
      case 'dropdown':
        // For dropdown action, only perform the dropdown selection (opening should be handled by separate opendropdown task)
        await this.performDropdown(task);
        break;
      case 'multiselect':
        await this.performMultiselect(task);
        break;
      case 'opendropdown':
        await this.performOpenDropdown(task);
        break;
      case 'radio':
        await this.performRadio(task);
        break;
      case 'daterange':
        await this.performDateRange(task);
        break;
      case 'dateinput':
        await this.performDateInput(task);
        break;
      case 'focusout':
        await this.performFocusOut(task);
        break;
      case 'hover':
        await this.performHover(task);
        break;
      case 'fetch':
        await this.performFetch(task);
        break;
      default:
        throw new Error(`Unsupported action: ${task.action}`);
    }
  }

  private async performClick(task: AutomationTask): Promise<void> {

    const element = await XPathUtils.waitForElement(task.xpath, 1000, task.elementName, task.labelName, task.selector, task.cssselector);
    if (!element) {
      // Additional debugging for element not found
      console.error(`❌ Element not found with XPath: ${task.xpath}, Name: ${task.elementName}, LabelName: ${task.labelName}, Selector: ${task.selector}, CssSelector: ${task.cssselector}`);

      // Try to find similar elements for debugging
      const allElements = document.querySelectorAll('*');
      const textElements = Array.from(allElements).filter(el =>
  el.textContent && el.textContent.toLowerCase().includes(task.value ?? "")
);


      console.warn(`⚠️ Click element not found: ${task.xpath} - Task will be skipped and automation will continue`);
      throw new Error(`Element not found: ${task.xpath}`);
    }

   

    if (!XPathUtils.isElementInteractable(element)) {
      throw new Error(`Element not interactable: ${task.xpath}`);
    }

    // Highlight element for visual feedback
    XPathUtils.highlightElement(element, 500);

    // Scroll element into view
    element.scrollIntoView({ behavior: 'smooth', block: 'center' });

    // Perform click with multiple event types for better compatibility
    const clickEvents = ['mousedown', 'mouseup', 'click'];
    for (const eventType of clickEvents) {
      console.log(`🖱️ Dispatching ${eventType} event`);
      element.dispatchEvent(new MouseEvent(eventType, {
        bubbles: true,
        cancelable: true,
        view: window,
        detail: 1
      }));
    }
  }

  private generateSimpleXPath(element: Element): string {
    if (element.id) {
      return `//*[@id="${element.id}"]`;
    }

    const path = [];
    let current: Element | null = element;

    while (current && current.nodeType === Node.ELEMENT_NODE) {
      let selector = current.nodeName.toLowerCase();

      if (current.className) {
        selector += `[@class="${current.className}"]`;
      }

      path.unshift(selector);
      current = current.parentElement;

      if (path.length > 5) break; // Limit depth
    }

    return '//' + path.join('/');
  }

  private async performFill(task: AutomationTask): Promise<void> {
    const element = await XPathUtils.waitForElement(task.xpath, 1000, task.elementName, task.labelName, task.selector, task.cssselector);
    if (!element) {
      console.warn(`⚠️ Fill element not found: ${task.xpath}, Name: ${task.elementName}, LabelName: ${task.labelName}, Selector: ${task.selector}, CssSelector: ${task.cssselector} - Task will be skipped and automation will continue`);
      throw new Error(`Element not found: ${task.xpath}`);
    }

    // Wait for element to be fully interactable
    await this.waitForElementReady(element);

    // Highlight element
    XPathUtils.highlightElement(element, 500);

    // Scroll into view and focus
    element.scrollIntoView({ behavior: 'smooth', block: 'center' });

    element.focus();

    if (!task.value) {
      console.warn('No value provided for fill task');
      return;
    }

    // Handle different types of fillable elements
    if (element instanceof HTMLInputElement || element instanceof HTMLTextAreaElement) {
      // Standard input/textarea elements
      try {
        // Clear existing value
        element.value = '';
        element.dispatchEvent(new Event('input', { bubbles: true }));

        // Set new value using property descriptor
        const prototype = element instanceof HTMLInputElement ? HTMLInputElement.prototype : HTMLTextAreaElement.prototype;
        const setter = Object.getOwnPropertyDescriptor(prototype, 'value')?.set;

        if (setter) {
          setter.call(element, task.value);
        } else {
          element.value = task.value;
        }

        // Trigger events
        ['input', 'change', 'blur'].forEach(eventType => {
          element.dispatchEvent(new Event(eventType, { bubbles: true }));
        });
      } catch (error) {
        console.error('Error setting input/textarea value:', error);
        // Fallback: direct assignment
        element.value = task.value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
      }
    } else if (element.contentEditable === 'true' || element.tagName.toLowerCase() === 'div') {
      // Content editable div or similar (like rich text editors)
      try {

        // Check if this div is inside a textarea container
        const parentTextArea = element.closest('textarea') ||
                              element.parentElement?.querySelector('textarea') ||
                              document.querySelector('#textArea1');

        if (parentTextArea) {
          const textAreaElement = parentTextArea as HTMLTextAreaElement;

          // Focus the textarea
          textAreaElement.focus();

          // Set value using property descriptor
          const setter = Object.getOwnPropertyDescriptor(HTMLTextAreaElement.prototype, 'value')?.set;
          if (setter) {
            setter.call(textAreaElement, task.value);
          } else {
            textAreaElement.value = task.value;
          }

          // Trigger events on textarea
          ['input', 'change', 'blur', 'focus'].forEach(eventType => {
            textAreaElement.dispatchEvent(new Event(eventType, { bubbles: true }));
          });

          return;
        }

        // Method 1: Direct content setting for content editable
        element.focus();

        // Clear existing content
        if (element.contentEditable === 'true') {
          element.innerHTML = '';
          element.textContent = '';
        } else {
          element.textContent = '';
        }

        // Set new content
        if (element.contentEditable === 'true') {
          // For content editable, use innerHTML
          element.innerHTML = task.value;

          // Also try execCommand for better compatibility
          try {
            document.execCommand('selectAll', false);
            document.execCommand('insertText', false, task.value);
          } catch (e) {
            console.log('execCommand failed, using innerHTML');
          }
        } else {
          // For regular divs, use textContent
          element.textContent = task.value;
        }

        // Trigger comprehensive events
        const events = ['input', 'change', 'blur', 'keyup', 'keydown', 'focusout', 'focusin'];
        events.forEach(eventType => {
          element.dispatchEvent(new Event(eventType, { bubbles: true }));
        });

        // Also trigger on parent elements in case of event delegation
        let parent = element.parentElement;
        while (parent && parent !== document.body) {
          ['input', 'change'].forEach(eventType => {
            parent!.dispatchEvent(new Event(eventType, { bubbles: true }));
          });
          parent = parent.parentElement;
        }

      } catch (error) {
        console.error('Error setting div content:', error);
        // Fallback: just set textContent
        element.textContent = task.value;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
      }
    } else {
      throw new Error(`Element is not fillable: ${task.xpath} (tag: ${element.tagName})`);
    }

    console.log(`✅ Filled element: ${task.xpath} with value: ${task.value}`);
  }

  // performWait method removed - no wait tasks for faster execution

private async performDropdown(task: AutomationTask): Promise<void> {
  const element = await XPathUtils.waitForElement(task.xpath, 1000, task.elementName, task.labelName, task.selector, task.cssselector);
  if (!element) {
    console.warn(`⚠️ Dropdown element not found: ${task.xpath}, Name: ${task.elementName}, LabelName: ${task.labelName}, Selector: ${task.selector}, CssSelector: ${task.cssselector}`);
    throw new Error(`Dropdown element not found: ${task.xpath}`);
  }

  XPathUtils.highlightElement(element, 500);

  if (element.tagName.toLowerCase() === 'select') {
    // Handle standard <select> dropdown
    const selectElement = element as HTMLSelectElement;
    const taskValue = (task.value || '').toLowerCase();

    const matchingOption = Array.from(selectElement.options).find(opt =>
      opt.value.toLowerCase() === taskValue || opt.text.toLowerCase() === taskValue
    );

    if (matchingOption) {
      selectElement.value = matchingOption.value;
      selectElement.dispatchEvent(new Event('change', { bubbles: true }));
      console.log(`✅ Set select dropdown: ${matchingOption.text}`);
    } else {
      console.warn(`❌ No matching option found for value: ${task.value}`);
    }
  } else {
    // Handle custom dropdown
    const inputElement = element as HTMLInputElement;
    const taskValue = (task.value || '').toLowerCase();

    // Step 1: Open the dropdown
    inputElement.focus();
    inputElement.click();

    // Step 3: Search and match item (case-insensitive)
    const allOptions = Array.from(document.querySelectorAll('li')) as HTMLElement[];

    const matchingOption = allOptions.find(li =>
      li.textContent?.trim().toLowerCase() === taskValue
    );

    if (matchingOption) {
      matchingOption.scrollIntoView({ behavior: 'smooth', block: 'center' });

      // Step 4: Click the matching option
      ['mousedown', 'mouseup', 'click'].forEach(type =>
        matchingOption.dispatchEvent(new MouseEvent(type, { bubbles: true }))
      );

      console.log(`✅ Selected option: ${matchingOption.textContent?.trim()}`);
    } else {
      console.warn(`❌ No matching dropdown option found for "${task.value}"`);
    }
  }
}


private async performMultiselect(task: AutomationTask): Promise<void> {
  function simulateClick(el: HTMLElement): void {
    ['mousedown', 'mouseup', 'click'].forEach(type => {
      el.dispatchEvent(new MouseEvent(type, {
        bubbles: true,
        cancelable: true,
        view: window
      }));
    });
  }

  const dropdown = await XPathUtils.waitForElement(task.xpath, 1000, task.elementName, task.labelName, task.selector, task.cssselector);
  if (!dropdown) {
    console.warn(`⚠️ Multiselect dropdown not found: ${task.xpath}, Name: ${task.elementName}, LabelName: ${task.labelName}, Selector: ${task.selector}, CssSelector: ${task.cssselector}`);
    return;
  }

  // Step 1: Open the dropdown
  simulateClick(dropdown);
  console.log('✅ Multiselect dropdown opened');

  const valueString = task.value ?? '';
  const targetValues = valueString.split(',').map(v => v.trim().toLowerCase());

  // Collect all <li> options in the dropdown (adjust selector if needed)
  const allOptions = Array.from(document.querySelectorAll('li')) as HTMLElement[];

  const selectedValues: string[] = [];

  for (const target of targetValues) {
    const match = allOptions.find(opt =>
      opt.textContent?.trim().toLowerCase() === target
    );

    if (match) {
      simulateClick(match);
      const actualValue = match.textContent?.trim() || target;
      selectedValues.push(actualValue);
      console.log(`✅ Selected: ${actualValue}`);
    } else {
      console.warn(`❌ Option not found for value: "${target}"`);
    }
  }

  // Step 3: Optional - close dropdown
  simulateClick(dropdown);
  console.log('✅ Multiselect dropdown closed');

  // Step 4: Persist selected values if needed
 
}






  private async performOpenDropdown(task: AutomationTask): Promise<void> {
    const element = await XPathUtils.waitForElement(task.xpath, 1000, task.elementName, task.labelName, task.selector, task.cssselector);
    if (!element) {
      console.warn(`⚠️ Dropdown trigger element not found: ${task.xpath}, Name: ${task.elementName}, LabelName: ${task.labelName}, Selector: ${task.selector}, CssSelector: ${task.cssselector} - Task will be skipped and automation will continue`);
      throw new Error(`Dropdown trigger element not found: ${task.xpath}`);
    }

    // Highlight element
    XPathUtils.highlightElement(element, 500);

    // Click to open dropdown
    ['mousedown', 'mouseup', 'click'].forEach(eventType => {
      element.dispatchEvent(new MouseEvent(eventType, {
        bubbles: true,
        cancelable: true,
        view: window
      }));
    });

    console.log(`✅ Dropdown opened successfully for: ${task.xpath}`);
  }

  private async performRadio(task: AutomationTask): Promise<void> {
    const container = await XPathUtils.waitForElement(task.xpath, 1000, task.elementName, task.labelName, task.selector, task.cssselector);
    if (!container) {
      console.warn(`⚠️ Radio element not found: ${task.xpath}, Name: ${task.elementName}, LabelName: ${task.labelName}, Selector: ${task.selector}, CssSelector: ${task.cssselector} - Task will be skipped and automation will continue`);
      throw new Error(`Radio element not found: ${task.xpath}`);
    }

    const radios = container.querySelectorAll<HTMLInputElement>('input[type="radio"]');
  let selected = false;

  radios.forEach(radio => {
    if (radio.value === task.value) {
      radio.checked = true;

      // Dispatch native events to simulate user interaction
      ['click', 'change'].forEach(eventType => {
        const event = new Event(eventType, { bubbles: true });
        radio.dispatchEvent(event);
      });

      selected = true;
    }
  });

  if (!selected) {
    //console.warn(`No radio button found with value "${valueToSelect}" inside XPath "${groupXPath}"`);
  }
  }





private async performDateRange(task: AutomationTask): Promise<void> {
  const inputEl: any = await XPathUtils.waitForElement(task.xpath, 1000, task.elementName, task.labelName, task.selector, task.cssselector);

  if (!inputEl) {
    throw new Error(`Date range element not found: ${task.xpath}, Name: ${task.elementName}, LabelName: ${task.labelName}, Selector: ${task.selector}, CssSelector: ${task.cssselector}`);
  }

  
  const set = (val: string) => {
    const setter = Object.getOwnPropertyDescriptor(HTMLInputElement.prototype, 'value')?.set;
    if (setter) {
      setter.call(inputEl, val);
    }
    ['input', 'change', 'blur'].forEach(e =>
      inputEl.dispatchEvent(new Event(e, { bubbles: true }))
    );
  };

  const isRange = (val: string): boolean => val.includes('|') || val.includes(' - ');

  // Handle task value parsing
  let finalValue: string | null = null;

  if (task.value) {
    const raw = task.value.trim();

    if (raw.includes('|')) {
      const [startDate, endDate] = raw.split('|').map(s => s.trim());
      finalValue = startDate && endDate ? `${startDate} - ${endDate}` : startDate || endDate;
    } else if (raw.includes(' - ')) {
      finalValue = raw;
    } else {
      finalValue = raw; // single date
    }
    if (isRange(raw)) {  
    set('04/06/1999 - 05/06/1999');  
    set(finalValue);
    } else {
      set(finalValue);
    }
  }
}

 

  private async performDateInput(task: AutomationTask): Promise<void> {
    // Similar to performFill but specifically for date inputs
    await this.performFill(task);
  }

  private async performFocusOut(task: AutomationTask): Promise<void> {
    const element = await XPathUtils.waitForElement(task.xpath, 1000, task.elementName, task.labelName, task.selector, task.cssselector);
    if (!element) {
      console.warn(`⚠️ FocusOut element not found: ${task.xpath}, Name: ${task.elementName}, LabelName: ${task.labelName}, Selector: ${task.selector}, CssSelector: ${task.cssselector} - Task will be skipped and automation will continue`);
      throw new Error(`Element not found: ${task.xpath}`);
    }

    element.blur();
    element.dispatchEvent(new Event('focusout', { bubbles: true }));
    console.log(`✅ Focus out: ${task.xpath}`);
  }

  private async performHover(task: AutomationTask): Promise<void> {
    const element = await XPathUtils.waitForElement(task.xpath, 1000, task.elementName, task.labelName, task.selector, task.cssselector);
    if (!element) {
      console.warn(`⚠️ Hover element not found: ${task.xpath}, Name: ${task.elementName}, LabelName: ${task.labelName}, Selector: ${task.selector}, CssSelector: ${task.cssselector} - Task will be skipped and automation will continue`);
      throw new Error(`Element not found: ${task.xpath}`);
    }

    // Highlight element for visual feedback
    XPathUtils.highlightElement(element, 500);

    // Scroll element into view
    element.scrollIntoView({ behavior: 'smooth', block: 'center' });

    // Get element's bounding rectangle for mouse position calculation
    const rect = element.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;

    // Create mouse events for hover simulation
    const mouseEvents = [
      new MouseEvent('mouseenter', {
        bubbles: true,
        cancelable: true,
        view: window,
        clientX: centerX,
        clientY: centerY,
        relatedTarget: null
      }),
      new MouseEvent('mouseover', {
        bubbles: true,
        cancelable: true,
        view: window,
        clientX: centerX,
        clientY: centerY,
        relatedTarget: null
      })
    ];

    // Dispatch hover events
    for (const event of mouseEvents) {
      console.log(`🖱️ Dispatching ${event.type} event on element`);
      element.dispatchEvent(event);
    }

    // Wait a moment to allow hover effects to be visible
    await new Promise(resolve => setTimeout(resolve, 1000));

    console.log(`✅ Hover performed on element: ${task.xpath}`);
  }

  private async performFetch(task: AutomationTask): Promise<void> {
    const element = await XPathUtils.waitForElement(task.xpath, 1000, task.labelName, task.selector);
    if (!element) {
      console.warn(`⚠️ Fetch element not found: ${task.xpath}, Name: ${task.elementName}, LabelName: ${task.labelName}, Selector: ${task.selector}, CssSelector: ${task.cssselector} - Task will be skipped and automation will continue`);
      throw new Error(`Element not found: ${task.xpath}`);
    }

    const value = element.textContent || (element as HTMLInputElement).value || '';
    console.log(`✅ Fetched value from ${task.xpath}: ${value}`);

    // Store the fetched value in task metadata for later use
    if (!task.metadata) {
      task.metadata = {};
    }
    task.metadata.fetchedValue = value;
  }

  private async waitForFormReady(): Promise<void> {
    // Wait for common form loading indicators to disappear
    const maxWait = 10000; // 10 seconds
    const startTime = Date.now();

    while (Date.now() - startTime < maxWait) {
      // Check for loading indicators
      const loadingElements = document.querySelectorAll('.loading, .spinner, [class*="load"], [class*="spin"]');
      const hasLoading = Array.from(loadingElements).some(el => XPathUtils.isElementVisible(el as HTMLElement));

      // Check if form elements are ready
      const formReady = this.isFormReady();

      if (!hasLoading && formReady) {
        console.log(`✅ Form is ready`);
        return;
      }

      await new Promise(resolve => setTimeout(resolve, 50)); // Reduced for faster execution
    }

    console.log(`⚠️ Form readiness timeout, proceeding anyway`);
  }

  private isFormReady(): boolean {
    // Check if key form elements are present and visible
    const keyElements = [
      'input[type="text"]',
      'textarea',
      'select',
      '[contenteditable="true"]',
      '.form-control',
      '#textArea1', // Specific to Leave Application form
      '[id*="DropDown"]', // Dropdown elements
      '[id*="textArea"]' // Text area elements
    ];

    for (const selector of keyElements) {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        const visibleElements = Array.from(elements).filter(el =>
          XPathUtils.isElementVisible(el as HTMLElement)
        );
        if (visibleElements.length > 0) {
          return true;
        }
      }
    }

    // Additional check for Leave Application specific elements
    const leaveFormElements = document.querySelectorAll('[id*="Leave"], [class*="leave"]');
    if (leaveFormElements.length > 0) {
      const visibleLeaveElements = Array.from(leaveFormElements).filter(el =>
        XPathUtils.isElementVisible(el as HTMLElement)
      );
      if (visibleLeaveElements.length > 2) { // Multiple leave-related elements visible
        return true;
      }
    }

    return false;
  }

  private async waitForElementReady(element: HTMLElement): Promise<void> {
    const maxWait = 1000; // Reduced to 1 second for faster execution
    const startTime = Date.now();

    while (Date.now() - startTime < maxWait) {
      if (XPathUtils.isElementInteractable(element)) {
        // Additional check for content editable elements
        if (element.contentEditable === 'true' || element.tagName.toLowerCase() === 'div') {
          // Removed additional wait for rich text editors for faster execution
        }
        return;
      }
      await new Promise(resolve => setTimeout(resolve, 50)); // Reduced from 100ms to 50ms
    }

    console.log(`⚠️ Element readiness timeout, proceeding anyway`);
  }

  private async executeTask(task: AutomationTask): Promise<void> {
    console.log(`Executing task: ${task.name}`);

    task.status = 'running';
    task.startedAt = new Date();

    if (this.onTaskUpdate) {
      this.onTaskUpdate(task);
    }

    try {
      await this.performAction(task);

      task.status = 'completed';
      task.completedAt = new Date();
      console.log(`Task completed: ${task.name}`);

    } catch (error) {
      task.status = 'failed';
      task.error = error instanceof Error ? error.message : 'Unknown error';
      task.completedAt = new Date();
      console.error(`Task failed: ${task.name}`, error);

      if (!task.skipOnError) {
        throw error;
      } else {
        // Log that we're skipping this task and continuing
        console.warn(`⚠️ Task failed but skipOnError=true, continuing: ${task.name}`);
        console.warn(`❌ Error was: ${task.error}`);
      }
    }

    if (this.onTaskUpdate) {
      this.onTaskUpdate(task);
    }

    // Add 1-second delay after each task execution
    await new Promise(resolve => setTimeout(resolve, 1000));
  }



}

// WorkerAgent Component Props
interface WorkerAgentProps {
  agentId:string;
  setAgentId:any;
  isVisible?: boolean;
  onToggleVisibility?: () => void;
  startTraining?: boolean;
  onTrainingStarted?: () => void;
  backgroundMode?: boolean; // New prop to run in background without UI
  bindingData?: Array<{labelName: string; value: any; xpath: string; type: string; key: string; name: string; selector: string; cssSelector: string;}>; // Binding data from SignalR
  setWorkerAgentVisible?:any;
  setStartTraining?:any;
  setBackgroundMode?:any;
  setBindingData?:any;
  setSubmitClicked?:any;
  submitClicked?:boolean;
}

const WorkerAgent: React.FC<WorkerAgentProps> = ({
  setWorkerAgentVisible,setStartTraining, setBackgroundMode,setBindingData,
  agentId,
  setAgentId,
  isVisible = true,
  onToggleVisibility,
  startTraining = false,
  onTrainingStarted,
  backgroundMode = false,
  bindingData = [],
  setSubmitClicked,
  submitClicked
}) => {
  const [taskQueue] = useState(() => new TaskQueue());
  const [isLoadingTasks, setIsLoadingTasks] = useState(false);
  const [showConfirmationDialog, setShowConfirmationDialog] = useState(false);

  const [automationEngine] = useState(() => new AutomationEngine(
    (task) => setTasks(prev => prev.map(t => t.id === task.id ? task : t)),
     () => {
      setIsRunning(false);
      console.log('🎉 QuickAdopt Training completed successfully!');
      console.log('📋 Setting showConfirmationDialog to true');
      console.log('🔧 backgroundMode:', backgroundMode);


   if( workAgentSignalRService.getFormSubmissionStatus() && !submitClicked){
      setShowConfirmationDialog(true);

   }
    },
    (error) => {
      setError(error);
      setIsRunning(false);
      if (backgroundMode) {
        console.error('❌ QuickAdopt Training failed:', error);
        // You can add an error toast notification here if needed
      }
    }
  ));

  const [tasks, setTasks] = useState<AutomationTask[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [progress, setProgress] = useState<ProgressInfo>(taskQueue.getProgress());
  const [error, setError] = useState<string | null>(null);

  // Update progress when tasks change
  useEffect(() => {
    setProgress(taskQueue.getProgress());
  }, [tasks, taskQueue]);

  // Debug: Track showConfirmationDialog state changes
  useEffect(() => {
    console.log('🔔 showConfirmationDialog state changed:', showConfirmationDialog);
    console.log('🔧 Current backgroundMode:', backgroundMode);
  }, [showConfirmationDialog, backgroundMode]);

  // Cleanup highlights when component unmounts
  useEffect(() => {
    return () => {
      // Clear all highlights when component unmounts
      XPathUtils.clearAllHighlights();
    };
  }, []);

  // Handle automatic training start when binding data is received
  // This starts automatically without requiring training button click
  useEffect(() => {
    console.log('🔍 WorkerAgent - Auto-start conditions check:', {
      startTraining,
      isRunning,
      backgroundMode,
      bindingDataLength: bindingData?.length || 0
    });

    if (startTraining && !isRunning) {
      if (backgroundMode) {
        console.log('🚀 Starting QuickAdopt automation in background mode (auto-triggered)...');
      } else {
        console.log('🚀 Starting QuickAdopt automation in normal mode (manual trigger)...');
      }
      handleStart();
      if (onTrainingStarted) {
        onTrainingStarted();
      }
    }

  
  }, [startTraining, isRunning, onTrainingStarted, backgroundMode, bindingData]);

  const handleStart = async () => {
    try {
      setError(null);
      setIsLoadingTasks(true);
      setIsRunning(true);

      // Clear existing tasks
      taskQueue.clear();

      // Use binding data from SignalR if available, otherwise use fallback data
      console.log('🚀 Starting training with binding data...');
      console.log('📋 Binding data received:', bindingData);

      let domActionsWithBinding: DomActionWithBinding[] = [];


      if (bindingData && bindingData.length > 0) {
        // Use the binding data from SignalR
        domActionsWithBinding = bindingData.map(item => ({
          labelName: item.labelName,
          name: item.name,
          cssSelector: item.cssSelector,
          selector: item.selector,
          value: item.value,
          xpath: item.xpath,
          type: item.type
        }));
        console.log('✅ Using binding data from SignalR:', domActionsWithBinding);
      } else {
        // Fallback to hardcoded data if no binding data available
        console.log('⚠️ No binding data available, using fallback data');

      }
      

      // Process DOM actions with binding data to determine actions based on type
      const processedDomActions = processDomActionsWithBinding(domActionsWithBinding);

      // Convert DOM actions to automation tasks
      const apiTasks = createTasksFromDomActions(processedDomActions);

      // Add tasks to queue
      apiTasks.forEach(task => taskQueue.addTask(task));

      // Update tasks state
      setTasks([...taskQueue.getAllTasks()]);

      console.log(`📋 Loaded ${apiTasks.length} tasks from API`);
      setIsLoadingTasks(false);

      // Start automation
      await automationEngine.start(taskQueue);
    } catch (error) {
      console.error('❌ Training failed:', error);
      setError(error instanceof Error ? error.message : 'Unknown error');
      setIsRunning(false);
      setIsLoadingTasks(false);
    }
  };

  const handleStop = () => {
    automationEngine.stop();
    setIsRunning(false);
  };

  const handleClearHighlights = () => {
    XPathUtils.clearAllHighlights();
    console.log('✅ All element highlights cleared');
  };

  const handleReset = () => {
    taskQueue.clear();
    setTasks([]);
    setError(null);
    setIsLoadingTasks(false);
  };

  const handleConfirmSubmit = async () => {
     setSubmitClicked(true);
    setShowConfirmationDialog(false);
           workAgentSignalRService.resetWorkflowState();

const agent = await GetAgentById(agentId);
  const clickObject = agent.TrainingFields.filter((field: any) => {
  const isClick = field.Type === "click";
  const hasButtonInPath =
    (field.Xpath && field.Xpath.toLowerCase().includes("button"))
   
  return isClick && hasButtonInPath;
});
const mappedData = clickObject.map((item: any) => ({
    name: item.Name,
    xpath: item.Xpath,
    type: item.Type,
    value: item.Value,
    selector: item.Selector,
    cssSelector: item.CSSSelector,
    labelName: item.LabelName
  }));
console.log(clickObject,"clickobject");
      setBindingData(mappedData);
    
    if (setBackgroundMode) setBackgroundMode(true);
    if (setWorkerAgentVisible) setWorkerAgentVisible(false);
    if (setStartTraining) setStartTraining(true);
    try {
      console.log('✅ WorkAgent Started');
    } catch (error) {
      console.error('❌ Failed to send DOM data binding confirmation:', error);
    }
   
  };

  const handleCancelSubmit = () => {
    setShowConfirmationDialog(false);
    console.log('❌ User cancelled form submission');
    // Optionally, you can add logic here to handle cancellation
    // For example, reset the form or show a message
  };

  const getStatusIcon = (status: TaskStatus) => {
    switch (status) {
      case 'completed':
        return <CheckCircle color="success" />;
      case 'failed':
        return <ErrorIcon color="error" />;
      case 'running':
        return <Schedule color="primary" />;
      default:
        return <Schedule color="disabled" />;
    }
  };

  const getStatusColor = (status: TaskStatus) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'failed':
        return 'error';
      case 'running':
        return 'primary';
      default:
        return 'default';
    }
  };

    if (showConfirmationDialog) {
      console.log('✅ Rendering confirmation dialog in background mode');
      return (
        <>
          {/* Confirmation Dialog for Background Mode */}
          <Dialog
            open={showConfirmationDialog}
            onClose={() => {}} // Prevent closing by clicking outside
            aria-labelledby="confirmation-dialog-title"
            maxWidth="sm"
            fullWidth
          >
            <DialogTitle id="confirmation-dialog-title">
              Confirm Submission
            </DialogTitle>
            <DialogContent>
              <Typography>
                All missing fields have been filled and DOM data binding is completed.
                Are you sure you want to confirm and submit the form?
              </Typography>
            </DialogContent>
            <DialogActions>
              <Button
                onClick={handleCancelSubmit}
                color="secondary"
                variant="outlined"
              >
                Cancel
              </Button>
              <Button
                onClick={handleConfirmSubmit}
                color="primary"
                variant="contained"
                autoFocus
              >
                Submit
              </Button>
            </DialogActions>
          </Dialog>
        </>
      );
    } else {
      console.log('❌ Not showing dialog, returning null in background mode');
      return null;
    }
  

};

export default WorkerAgent;


