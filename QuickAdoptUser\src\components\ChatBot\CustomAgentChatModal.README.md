# CustomAgentChatModal - Simple WorkAgent SignalR Implementation (No TTS)

This is a simplified chat modal component that uses Azure SignalR to communicate with the WorkAgentHub for real-time AI agent interactions, based on the existing `ChatModal.tsx` structure but with Text-to-Speech functionality removed.

## Features

- **WorkAgent Integration**: Uses Azure SignalR to communicate with WorkAgentHub
- **Singleton Connection**: Single persistent connection pattern
- **Connection Status**: Visual indicators showing WorkAgent connection state
- **Manual Disconnect**: Disconnect button for user control
- **Speech Recognition**: Voice input functionality (microphone support)
- **Auto-reconnection**: Automatic reconnection with exponential backoff
- **Error Handling**: Comprehensive error handling for connection issues
- **No TTS**: Text-to-Speech functionality has been completely removed
- **Simple Management**: No automatic timeouts or rate limiting

## Key Differences from Original ChatModal

1. **WorkAgent SignalR Integration**: Connects to `/WorkAgentHub` endpoint instead of SSE
2. **Singleton Connection Pattern**: Single persistent connection to avoid Azure rate limits
3. **ProcessPrompt Method**: Uses `ProcessPrompt` hub method instead of REST API calls
4. **Real-time Status**: Connection status indicator showing WorkAgent connection state
5. **Enhanced Error Handling**: Better error messages for WorkAgent connection issues
6. **No Text-to-Speech**: All TTS functionality has been removed including:
   - Auto-speak toggle button
   - Voice playback of responses
   - ElevenLabs audio integration
   - WebKit speech synthesis
   - Audio controls and buttons
7. **Manual Connection Control**: User can manually disconnect via button

## Usage

```tsx
import { CustomAgentChatModal } from './components/ChatBot';

function App() {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState([]);
  // ... other state variables

  return (
    <CustomAgentChatModal
      open={isOpen}
      onClose={() => setIsOpen(false)}
      guide={guide}
      isFromAi={isFromAi}
      setGuide={setGuide}
      setIsFromAi={setIsFromAi}
      setIsOpen={setIsOpen}
      isWelcomeMessageShown={isWelcomeMessageShown}
      setWelcomeMessageShown={setWelcomeMessageShown}
      messages={messages}
      setMessages={setMessages}
      isReWelcomeMessageShown={isReWelcomeMessageShown}
      setReWelcomeMessageShown={setReWelcomeMessageShown}
    />
  );
}
```

## Environment Configuration

The component automatically uses your existing `REACT_APP_ADMIN_API` environment variable to construct the WorkAgentHub URL:

```env
# Your existing admin API URL
REACT_APP_ADMIN_API=http://localhost:60550/api

# The component will automatically connect to:
# http://localhost:60550/WorkAgentHub
```

## Server-Side Requirements (Already Implemented)

Your AdminAPI already has the WorkAgentHub implemented at `/WorkAgentHub` with the following methods:

### Hub Methods Available

```csharp
public class WorkAgentHub : Hub
{
    public async Task ProcessPrompt(object prompt)
    {
        // Process the prompt and send responses
        await Clients.Caller.SendAsync("ReceiveMessage", "Processing your request...");
        // ... process with agent execution service
        await Clients.Caller.SendAsync("ReceiveMessage", result);
    }

    public async Task SubmitFieldValues(Dictionary<string, string> fieldValues)
    {
        // Handle field value submissions
    }

    public async Task ConfirmSubmission(bool confirmed)
    {
        // Handle submission confirmations
    }
}
```

### Client Events the Component Listens For

- `ConnectionEstablished`: Confirms connection to WorkAgentHub
- `ReceiveMessage`: Receives responses from the WorkAgent
- `AgentError`: Receives error messages from the WorkAgent

## WorkAgent SignalR Service

The component uses a singleton `WorkAgentSignalRService` that provides:

- **Singleton Pattern**: Single connection instance across all components
- **Rate Limit Protection**: Avoids Azure SignalR rate limits
- **Connection management**: Auto-reconnection with exponential backoff
- **Callback Management**: Multiple components can register callbacks
- **WorkAgent Integration**: Direct communication with your WorkAgentHub

## Connection States

The component displays different WorkAgent connection states:

- 🟢 **Connected**: Ready to send prompts to WorkAgent
- 🟡 **Connecting/Reconnecting**: Attempting to establish WorkAgent connection
- 🔴 **Disconnected**: No connection to WorkAgent

## Authentication

The WorkAgent SignalR service supports token-based authentication. Set the `authToken` in localStorage:

```javascript
localStorage.setItem('authToken', 'your-jwt-token');
```

## Simple Connection Management

The singleton pattern ensures:
- Only one connection to Azure SignalR per browser session
- Multiple chat components can share the same connection
- **Manual disconnect button** for user control
- Connection persists until manually disconnected or page refresh
- Simple and predictable connection behavior

## Error Handling

The component handles various error scenarios:

- WorkAgent connection failures
- Prompt processing errors
- Authentication errors
- Network interruptions

All errors are displayed as chat messages to provide user feedback.

## Key Benefits

1. **Simple**: Easy to understand and use
2. **User Control**: Manual disconnect button for user control
3. **Better Performance**: Persistent connection reduces latency
4. **Scalable**: Multiple components can use the same connection
5. **Reliable**: Auto-reconnection ensures consistent connectivity
6. **Predictable**: Connection behavior is straightforward
7. **Transparent**: Clear connection status indicators
