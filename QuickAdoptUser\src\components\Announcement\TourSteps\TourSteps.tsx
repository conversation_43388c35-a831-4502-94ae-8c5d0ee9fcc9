import { Box, DialogContent, FormControlLabel, FormGroup, LinearProgress, Popover,Dialog, Checkbox, Typography } from "@mui/material";
import React, { useState } from "react";
import { TourStepWrapper, TourWrapper, IconBtnStyle } from "./TourSteps.style";
import { ReactComponent as TourStepLogo } from "../../../assets/icons/tourstep.svg";
import { CustomIconButton } from "../../Button";
import CloseIcon from "@mui/icons-material/Close";
import CheckBox from "@mui/icons-material/CheckBox";
import DialogTitle from "@mui/material/DialogTitle";

interface ITourStep {
	currentStep: number;
	totalSteps: number;
	guideSteps: { isVisited: boolean }[];
	onStepChange: (step: number) => void;
}

const TourStep = ({ currentStep, totalSteps, onStepChange, guideSteps }: ITourStep) => {
	const [open, setOpen] = useState(true);
	const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);

	const handleClose = () => {
		setOpen(false);
	};

	const handleOpen = (e: React.MouseEvent<HTMLButtonElement>) => {
		setAnchorEl(e.currentTarget);
		setOpen(true);
	};

	const handleStepChange = (step: number) => {
		onStepChange(step);
	};


	const visitedStepsCount = guideSteps?.filter(step => step.isVisited).length;
    const progress = (visitedStepsCount / totalSteps) * 100;

	return (
		<>
			<Box sx={TourWrapper}>
				<CustomIconButton
					sx={TourStepWrapper}
					type="button"
					color="primary"
					style={IconBtnStyle}
					onClick={handleOpen}
				>
					<TourStepLogo />
				</CustomIconButton>
			</Box>
			{open ? (
				<TourDialog
					open={open}
					onClose={handleClose}
					anchorEl={anchorEl}
					currentStep={currentStep}
					totalSteps={totalSteps}
					progress={progress}
					guideSteps={guideSteps}
					onStepChange={handleStepChange}
				/>
			) : null}
		</>
	);
};

export default TourStep;

interface ITourDialog {
	open: boolean;
	onClose: (value: string) => void;
	anchorEl: HTMLButtonElement | null;
	currentStep: number;
	totalSteps: number;
	progress: number;
	guideSteps: { isVisited: boolean }[];
	onStepChange: (step: number) => void;
}

function TourDialog(props: ITourDialog) {
	const { onClose, open, anchorEl, currentStep, totalSteps, progress, onStepChange, guideSteps } = props;

	return (
		<Popover
			anchorEl={anchorEl}
			onClose={onClose}
			anchorOrigin={{
				vertical: "bottom",
				horizontal: "right",
			}}
			transformOrigin={{
				vertical: "bottom",
				horizontal: "right",
			}}
			open={open}
			sx={{
				zIndex: 1301,
				"& .MuiPaper-root": {
					borderRadius: "15px",
					
				},
				position:"absolute"
			}}

		>
			<DialogTitle sx={{ padding: "8px 24px" }}>Tour</DialogTitle>
			<CustomIconButton sx={{ position: "absolute", right: 8, top: 8 }}>
				<CloseIcon />
			</CustomIconButton>

			<DialogContent dividers sx={{ minWidth: "320px", p: 2 }}>
				<Box>
					<Typography>{currentStep + 1}/{totalSteps} tasks are done   { progress}%</Typography>
					
					<LinearProgress variant="determinate" sx={{
						height: "13px",
						borderRadius: "15px"
					}} value={progress} />
				</Box>
				<FormGroup>
					{Array.from({ length: totalSteps }).map((_, index) => (
						<FormControlLabel
							key={index}
							control={
								<Checkbox
									checked={guideSteps[index].isVisited}
									onChange={() => onStepChange(index)}
								/>
							}
							label={`Step ${index + 1}`}
						/>
					))}
				</FormGroup>
			</DialogContent>
		</Popover>
	);
}
