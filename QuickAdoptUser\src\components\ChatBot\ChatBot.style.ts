import { SxProps } from "@mui/material";

// Button styles
export const ChatButtonWrapper: SxProps = {
  position: "fixed",
  right: "20px",
  bottom: "20px",
  zIndex: 1200,
};

export const ChatButtonStyle: SxProps = {
  backgroundColor: "#5f9ea0", // Cadet blue color
  color: "white",
  "&:hover": {
    backgroundColor: "#4f8e90", // Slightly darker shade for hover
  },
  width: "56px",
  height: "56px",
  boxShadow: "0px 3px 5px -1px rgba(0,0,0,0.2), 0px 6px 10px 0px rgba(0,0,0,0.14), 0px 1px 18px 0px rgba(0,0,0,0.12)",
};

// Modal styles
export const ChatModalStyle: SxProps = {
  position: "fixed",
  right: "20px", // Add padding from the right edge
  top: "5vh", // Position 5% from the top
  height: "90vh", // Take up 90% of the viewport height
  width: "400px",
  display: "flex",
  flexDirection: "column",
  borderRadius: "8px",
  boxShadow: "0px 0px 15px rgba(0,0,0,0.2)",
  backgroundColor: "#fff",
  overflow: "hidden",
  zIndex: 1300,
};

export const ChatHeaderStyle: SxProps = {
  display: "flex",
  alignItems: "center",
  justifyContent: "space-between",
  padding: "10px 16px",
  backgroundColor: "#5f9ea0", // Cadet blue color
  color: "white",
};

export const ChatBodyStyle: SxProps = {
  flexGrow: 1,
  overflowY: "auto",
  padding: "16px",
  display: "flex",
  flexDirection: "column",
  gap: "8px",
  height: "calc(90vh - 110px)", // Adjust for header and footer
};

export const ChatFooterStyle: SxProps = {
  padding: "10px 16px",
  borderTop: "1px solid #e0e0e0",
  display: "flex",
  alignItems: "center",
  gap: "8px",
};

export const MessageContainerStyle: SxProps = {
  display: "flex",
  flexDirection: "column",
  gap: "8px",
  width: "100%",
};

export const UserMessageStyle: SxProps = {
  alignSelf: "flex-end",
  backgroundColor: "#e3f2fd",
  padding: "8px 12px",
  borderRadius: "18px 18px 0 18px",
  maxWidth: "80%",
  wordBreak: "break-word",
};

export const BotMessageStyle: SxProps = {
  alignSelf: "flex-start",
  backgroundColor: "#f5f5f5",
  padding: "8px 12px",
  borderRadius: "18px 18px 18px 0",
  maxWidth: "80%",
  wordBreak: "break-word",
};

export const InterimTranscriptStyle: SxProps = {
  padding: '8px 16px',
  backgroundColor: '#f0f8ff',
  borderTop: '1px solid #e0e0e0',
  fontStyle: 'italic',
};

export const MicButtonStyle: SxProps = {
  position: 'relative',
  marginRight: '8px',
};

export const ListeningIndicatorStyle: SxProps = {
  position: 'absolute',
  top: 0,
  left: 0,
  width: '100%',
  height: '100%',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
};

export const SpeechButtonStyle: SxProps = {
  marginLeft: '8px',
  transition: 'color 0.3s ease',
  '&:hover': {
    color: '#5f9ea0', // Cadet blue color on hover
  },
};
