



import React, { useEffect, useMemo, useState } from 'react';
import ChecklistCircle from "./ChecklistCheckIcon";
import ImageCarousel from "./ImageCarousel";
import VideoPlayer from "./VideoPlayer";
import CloseIcon from "@mui/icons-material/Close";
import { chkdefault, maximize } from '../../assets/icons/icons';
import './Checklist.scss';
import { GetGuideDetailsByGuideId } from '../Services/GuideService';

// Function to modify the color of an SVG icon
const modifySVGColor = (base64SVG: any, color: any) => {
	if (!base64SVG) {
		return "";
	}

	try {
		// Check if the string is a valid base64 SVG
		if (!base64SVG.includes("data:image/svg+xml;base64,")) {
			return base64SVG; // Return the original if it's not an SVG
		}

		const decodedSVG = atob(base64SVG.split(",")[1]);

		// Check if this is primarily a stroke-based or fill-based icon
		const hasStroke = decodedSVG.includes('stroke="');
		const hasColoredFill = /fill="(?!none)[^"]+"/g.test(decodedSVG);

		let modifiedSVG = decodedSVG;

		if (hasStroke && !hasColoredFill) {
			// This is a stroke-based icon (like chkicn2-6) - only change stroke color
			modifiedSVG = modifiedSVG.replace(/stroke="[^"]+"/g, `stroke="${color}"`);
		} else if (hasColoredFill) {
			// This is a fill-based icon (like chkicn1) - only change fill color
			modifiedSVG = modifiedSVG.replace(/fill="(?!none)[^"]+"/g, `fill="${color}"`);
		} else {
			// No existing fill or stroke, add fill to make it visible
			modifiedSVG = modifiedSVG.replace(/<path(?![^>]*fill=)/g, `<path fill="${color}"`);
			modifiedSVG = modifiedSVG.replace(/<svg(?![^>]*fill=)/g, `<svg fill="${color}"`);
		}

		const modifiedBase64 = `data:image/svg+xml;base64,${btoa(modifiedSVG)}`;
		return modifiedBase64;
	} catch (error) {
		console.error("Error modifying SVG color:", error);
		return base64SVG; // Return the original if there's an error
	}
};

let CheckpointId: string;
// import { closepluginicon, maximize } from '../../assets/icons/icons';
interface CheckListPopupProps {
    isOpen: any;
    onClose: () => void;
    onRemainingCountUpdate: (formattedCount: number) => void;
    data: any;
    guideDetails: any;
    isRightPanelVisible: any;
  setIsRightPanelVisible: any;
  Canvas: any;
  TitleSubTitle: any;
  Checkpoints: any
  CheckpointsList: any;
  onStepCheckpoint: (foramttedstepcheckpoint: string) => void;
  completedStatus: any;
  setCompletedStatus: any;
  Launcher: any;

}
interface Guide {
  GuideId: string;
  DontShowAgain: boolean;
  GuideType: string;
  CompletedCheckPoint?: boolean; // Make it optional since not all guides will have it
}

interface ClosedGuides {
  checklist?: Guide[]; // A separate checklist array inside closedGuides
}

let selectedId: any;
let checkpointslistData: any;
  const ChecklistPopup: React.FC<CheckListPopupProps> = ({ isOpen, onClose, onRemainingCountUpdate, data,onStepCheckpoint, guideDetails,isRightPanelVisible,setIsRightPanelVisible,Canvas,TitleSubTitle,Checkpoints,CheckpointsList,completedStatus,setCompletedStatus,Launcher }) => {

    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(document.body);
    const [isMaximized, setIsMaximized] = useState(false);
  
  
     checkpointslistData = CheckpointsList?.map(
      (checkpoint: any, index: number) => ({
        ...checkpoint,
        
      })
    ) || [];

    const [checklistItems, setChecklistItems] = useState(checkpointslistData);
    const [activeItem, setActiveItem] = useState(checkpointslistData[0]?.Id || "");
  
    useEffect(() => {
      if (Object.keys(completedStatus).length === 0) {  
        const initialCompletedStatus: { [key: string]: boolean } = {};
        
        checkpointslistData.forEach((item: any, index: number) => {
          initialCompletedStatus[item.id] = index === 0; 
        });
    
        setCompletedStatus(initialCompletedStatus);
      }
    }, []);
    useEffect(() => {
      if (checkpointslistData.length > 0 ) {
        setActiveItem(checkpointslistData[0].Id);
      }
    }, [isOpen]);  
  
    const selectedItem = checkpointslistData?.find((item: any) => item.Id === activeItem);
    const totalItems = checkpointslistData.length || 1;
    const progress = Object.values(completedStatus).filter(status => status).length || 0;    
    const remainingProgress = totalItems - Object.values(completedStatus).filter(status => status).length;

    useEffect(() => {
      const updatedStatus: { [key: string]: boolean } = {};
    
      // Retrieve `closedGuides` from localStorage
      const storedGuides: (Guide | ClosedGuides)[] = JSON.parse(localStorage.getItem("closedGuides_/") || "[]");
    
      // Find the `ClosedGuides` object containing `Checklist`
      const checklistObject = storedGuides.find((guide) => (guide as ClosedGuides).checklist !== undefined) as ClosedGuides | undefined;
      
      // Extract the checklist array if it exists
      const checklistGuides: Guide[] = checklistObject?.checklist || [];
    
      // Update status for each checkpoint
      CheckpointsList?.forEach((checkpoint: any) => {
        const isGuideClosed = checklistGuides.some(
          (guide) => guide.GuideId === checkpoint.Id && guide.CompletedCheckPoint === true
        );
    
        updatedStatus[checkpoint.Id] = isGuideClosed;
      });
    
      // Update state
      setCompletedStatus(updatedStatus);
    
      // Save updated data back to localStorage
      localStorage.setItem("closedGuides_/", JSON.stringify(storedGuides));
    }, [isOpen]);

    const isRTL = 
  document.documentElement.getAttribute('dir') === 'rtl' ||
  document.body.getAttribute('dir') === 'rtl'; 

    
    useEffect(() => {
      // Make sure we're calculating the remaining items correctly
      // by considering all checkpoints in the list
      onRemainingCountUpdate(remainingProgress);
    }, [completedStatus, onRemainingCountUpdate, remainingProgress]);

    // useEffect(() =>
    //   {
    //     const handleStore = () => {
          
    
    //       const storedGuides: Guide[] = JSON.parse(localStorage.getItem("completedChekpoints_/") || "[]");
    //       setAnchorEl(null);
    //       var isCheckpointsCompleted = storedGuides.some(
    //         (guide) => guide.GuideId === selectedId && guide.completedCheckpoint === true && guide?.GuideType.toLowerCase() === 'Checklist'
    //         );        
    //         if (!isCheckpointsCompleted)
    //         {
    //             const updatedGuides = [
    //                 ...storedGuides,
    //                 {
    //                     GuideId: selectedId,
    //                     completedCheckpoint: true,
    //                     GuideType: 'Checklist'
    //                 },
    //             ];
    //             localStorage.setItem("completedChekpoints_/", JSON.stringify(updatedGuides));        
    //         }  
         
    //     };
    //     handleStore();
    //   },[completedStatus])

    // Removed unused function
    useEffect(() => {
      const updateChecklistStatus = () => {
        const updatedStatus: { [key: string]: boolean } = {};
        
        // Get all closed guides from localStorage
        const storedGuides = JSON.parse(localStorage.getItem("closedGuides_/") || "[]");
        // Find checklist array
        let checklistObject = storedGuides.find((guide: any) => "checklist" in guide);
        if (!checklistObject) {
          checklistObject = { checklist: [] };
          storedGuides.push(checklistObject);
        }
        // Find guides that have been closed (DontShowAgain is true)
        const closedGuides = storedGuides.filter((guide: any) => guide.DontShowAgain === true);
        // Update status for each checkpoint
        CheckpointsList?.forEach((checkpoint: any) => {
          // If the checkpoint is in checklist with CompletedCheckPoint: true, mark as completed
          const checklistEntry = checklistObject.checklist.find((item: any) => item.GuideId === checkpoint.Id && item.CompletedCheckPoint === true);
          if (checklistEntry) {
            updatedStatus[checkpoint.Id] = true;
          } else {
            // If the guide is closed, add it to checklist with CompletedCheckPoint: true
            const isGuideClosed = closedGuides.some((guide: any) => guide.GuideId === checkpoint.Id);
            if (isGuideClosed) {
              checklistObject.checklist = checklistObject.checklist.filter((item: any) => item.GuideId !== checkpoint.Id);
              checklistObject.checklist.push({
                GuideId: checkpoint.Id,
                CompletedCheckPoint: true,
                DontShowAgain: false,
                GuideType: "Checklist"
              });
              updatedStatus[checkpoint.Id] = true;
              localStorage.setItem("closedGuides_/", JSON.stringify(storedGuides));
            } else {
              updatedStatus[checkpoint.Id] = false;
            }
          }
        });
    
        // Update the completedStatus state
        setCompletedStatus(updatedStatus);
      };
    
      // Initial check
      updateChecklistStatus();
    
      // Listen for storage changes
      window.addEventListener('storage', updateChecklistStatus);
      
      return () => {
        window.removeEventListener('storage', updateChecklistStatus);
      };
    }, [CheckpointsList, isOpen, setCompletedStatus]); // Add isOpen dependency if you want to update when popup opens
    const primaryColor = Canvas?.PrimaryColor || "#5F9EA0";
    const [isPublished, setIsPublished] = useState(true);

  useEffect(() => {
    const fetchGuideDetails = async () => {
      if (!selectedItem?.Id) return; // Ensure there's a valid ID
  
      try {
        const res = await GetGuideDetailsByGuideId(selectedItem.Id);
        if (res?.GuideDetails?.GuideStatus === "InActive" || res?.GuideDetails?.GuideStatus === "Draft")
        {
          setIsPublished(false);
        }
        else
        {
          setIsPublished(true);

          }
      } catch (error) {
      }
    };
    
  
    fetchGuideDetails();
  }, [selectedItem, activeItem]); 
  useEffect(() => {
    document.documentElement.style.setProperty("--chkcolor", primaryColor);
  }, [primaryColor]);
  const handleMarkAsCompleted = (Id: string) => {
    if (Checkpoints?.UnlockCheckpointsInOrder) {
      const checkpointIndex = checkpointslistData.findIndex((item: any) => item.Id === Id);
  
      if (checkpointIndex > 0) {
        const previousCheckpoint = checkpointslistData[checkpointIndex - 1];
  
        if (!completedStatus[previousCheckpoint.Id]) {
          alert("You must Complete Items In Order");
          return;
        }
      }
    }

    // Update localStorage checklist array inside closedGuides
    const storedGuides = JSON.parse(localStorage.getItem("closedGuides_/") || "[]");
    let checklistObject = storedGuides.find((guide: any) => "checklist" in guide);
    if (!checklistObject) {
      checklistObject = { checklist: [] };
      storedGuides.push(checklistObject);
    }
    // Remove any duplicate for this Id
    checklistObject.checklist = checklistObject.checklist.filter((item: any) => item.GuideId !== Id);
    checklistObject.checklist.push({
      GuideId: Id,
      DontShowAgain: false,
      CompletedCheckPoint: true,
      GuideType: "Checklist"
    });
    localStorage.setItem("closedGuides_/", JSON.stringify(storedGuides));

    // Update completedStatus state using Id as key
    setCompletedStatus((prevStatus: any) => ({
      ...prevStatus,
      [Id]: true,
    }));
  };
  
    const handleSelect = (Id: any) => {
      setActiveItem(Id);
      selectedId = Id;
        setIsRightPanelVisible(true);
    };
 
    const handleClose = () => {
      if (isRightPanelVisible) {
          setIsRightPanelVisible(false); 
      } else {
          onClose();  
      }
  };
    const handleMinimize = () =>
    {
      setIsMaximized(false);
      }

      const handleMaximize = () => {
        setIsMaximized(true); 
      };
      
    if (!isOpen) return null;
  
  
    if (!isOpen) return null;
    const leftPanelWidth = activeItem ? '40%' : "100%";
    const rightPanelWidth = '100%';
    
    const handleNavigate = (checkpointId: string) => {
      if (Checkpoints?.UnlockCheckpointsInOrder) {
          const checkpointIndex = checkpointslistData.findIndex((item: any) => item.Id === checkpointId);
  
          if (checkpointIndex > 0) {
              const previousCheckpoint = checkpointslistData[checkpointIndex - 1];
  
              if (!completedStatus[previousCheckpoint.Id]) {
                alert("You must Complete Items In Order");
                return;
              }
          }
      }
  
     

      const URL = CheckpointsList.find((item: any) => item.Id === checkpointId)?.RedirectURL;
  
      if (URL) {
          window.open(URL, '_blank');
          onStepCheckpoint(checkpointId);
      }
  
      // Retrieve stored guides
      const storedGuides = JSON.parse(localStorage.getItem("closedGuides_/") || "[]");
  
      // Find the checklist object
      const existingChecklist = storedGuides.find((guide: any) => "checklist" in guide);
      // Add the checkpoint to the checklist's checkpointIds array
      let updatedGuides;
      if (existingChecklist) {
          updatedGuides = storedGuides.map((guide: any) =>
              "checklist" in guide
                  ? {
                      ...guide,
                      checkpointIds: [...(guide.checkpointIds || []), checkpointId]
                    }
                  : guide
          );
      } else {
          updatedGuides = [
              ...storedGuides,
              {
                  checklist: [],
                  checkpointIds: [checkpointId]  // Add first checkpoint ID
              }
          ];
      }
  
      localStorage.setItem("closedGuides_/", JSON.stringify(updatedGuides));
    };

    
      return (
        <>
          {isOpen && (
            <div style={{
              position: 'fixed',
              inset: 0,
              display: 'flex',
              alignItems: 'center',
              // justifyContent: 'center',
              zIndex: 50
            }}>
              <div style={{
                position: 'absolute',
                inset: 0,
                backgroundColor: 'rgba(0, 0, 0, 0.3)'
              }} onClick={handleClose}></div>
  
   <div style={{
                boxShadow: "rgba(0, 0, 0, 0.1) 0px 20px 25px -5px, rgba(0, 0, 0, 0.04) 0px 10px 10px -5px",
                zIndex: 9,
                marginTop: 'auto',
                marginBottom: `${parseInt(Launcher?.LauncherPosition?.YAxisOffset || "10") + 30}px`,
                marginLeft: Launcher?.LauncherPosition?.Left===true ? `${parseInt(Launcher?.LauncherPosition?.XAxisOffset || "10") + 30}px` : 'auto',
                marginRight: Launcher?.LauncherPosition?.Left===true ? 'auto' : `${parseInt(Launcher?.LauncherPosition?.XAxisOffset || "10") + 30}px`,
              }} className='qadpt-chkpopup'>

                <div style={{
                  backgroundColor: Canvas?.BackgroundColor,
                  border: `${Canvas?.BorderWidth}px solid ${Canvas?.BorderColor}`,
                  borderRadius: `${Canvas?.CornerRadius}px`,
                  width: isRightPanelVisible 
                ? `${Canvas?.Width || 930}px` 
                  : '350px',
                height: `${Canvas?.Height || 500}px`,
               
  
                }}>
  
                  <div style={{
                    display: 'flex',
                    height: '100%',
                    width: "100%",
                    overflow: "auto hidden"

                  }} className='qadpt-chkcontent'>
                    {/* Left side - Checklist items */}
                    <div style={{
                       width: isRightPanelVisible ? "40%" : "100%" ,
                       borderRight: isRightPanelVisible ?'1px solid #e5e7eb':"none" ,
  
                      textAlign: isRTL?"right": "left"
  
                    }} className='qadpt-chkrgt'>
                    
                      <div style={{
                        display: "flex",
                        flexDirection: "column",
                        gap: "16px",
                        borderBottom: "1px solid #E8E8E8",
                        padding: "24px 24px 16px 24px"
                      }}
                      >
                      
                        <div style={{
                          display: "flex",
                          flexDirection: "column",
                          gap: "6px"
                        }}>
                          <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", gap :isRightPanelVisible?"0px":"5px"}}>
                            <div style={{
                              fontSize: '20px',
                              fontWeight: TitleSubTitle?.TitleBold ? 'bold' : 'normal',
                              fontStyle: TitleSubTitle?.TitleItalic ? 'italic' : 'normal',
                              color: TitleSubTitle?.TitleColor || '#333',
                              display: "block",
                              textOverflow: "ellipsis",
                              whiteSpace: "nowrap",
                              wordBreak: "break-word",
                              overflow: "hidden",
                            }}>
                              {TitleSubTitle?.Title}
                            </div>
                            <div>
                              {!isRightPanelVisible && (
                                <span onClick={handleClose} style={{
                                  background: '#e8e8e8',
                                  borderRadius: '50%',
                                  padding: '7px',
                                  display: 'flex',
                                  cursor: "pointer"
                                }}
                                >								<CloseIcon sx={{ height: "16px", width: "16px" }} /></span>
                              )}
                            </div>
                          </div>
                          <div style={{
                            fontSize: '14px',
                            fontWeight: TitleSubTitle?.SubTitleBold ? 'bold' : 'normal',
                            fontStyle: TitleSubTitle?.SubTitleItalic ? 'italic' : 'normal',
                            color: TitleSubTitle?.SubTitleColor || '#8D8D8D',
                            display: "-webkit-box",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: "vertical",
                            wordBreak: "break-word",
                          }}>
                            {TitleSubTitle?.SubTitle}
                          </div>
                        </div>
  
                        <div>
                          <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            marginBottom: '8px'
                          }}>
                            <span style={{ fontSize: '14px', color: '#6b7280' }}>{progress}/{totalItems}</span>
                          </div>
                          <div style={{
                            height: '8px',
                            backgroundColor: '#e5e7eb',
                            borderRadius: '9999px',
                            overflow: 'hidden'
                          }}>
                            <div
                              style={{
                                height: '100%',
                                backgroundColor: Canvas?.PrimaryColor,
                                borderRadius: '9999px',
                                width: `${(progress / totalItems) * 100}%`
                              }}
                            ></div>
                          </div>
                        </div>
                      </div>
  
                      <div style={{
  maxHeight: `calc(${Canvas?.Height || 500}px - 190px)`,
  overflow: "auto"
}}
className='qadpt-chklist'>
                        {checkpointslistData?.map((item: any) => (
                        <div
                        className=""
                        style={{
                          position: "relative",
                        }}
                      >
                        {activeItem === item.Id && (
                          <span
                            style={{
                              background: Canvas?.PrimaryColor || "var(--chkcolor)", // Replace with actual color
                              content: "''",
                              position: "absolute",
                              display: "block",
                              width: "6px",
                              height: "100%",
                              borderTopRightRadius: "10px",
                              borderBottomRightRadius: "10px",
                            }}
                          ></span>
                        )}
                        <div
                          key={item.id}
                          style={{
                            display: "flex",
                            flexDirection: "column",
                            padding: "10px 16px 10px 10px",
                            cursor: "pointer",
                            borderBottom: "1px solid #E8E8E8",
                          }}
                          onClick={() => handleSelect(item.Id)}
                        >
                          {/* Title Section */}
                          <div style={{ paddingLeft: "10px", display: "flex", gap: "6px", flexDirection: "column" }}>
                            <div
                              style={{
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "space-between",
                                width: "100%",
                              }}
                            >
                              <div style={{ display: "flex", alignItems: "center", gap: "10px", flexDirection: "row" }}>
                                {item.Icon && typeof item.Icon === "string" ? (
                                  <img
                                    src={modifySVGColor(item.Icon, Checkpoints?.CheckpointIconsColor || "#333333")}
                                    alt="icon"
                                    style={{ width: "20px", height: "20px" }}
                                  />
                                ) : (
                                  <span style={{ width: "20px", height: "20px" }}></span>
                                )}

                                <span style={{ color: Checkpoints?.CheckpointTitleColor }}>{item.Title}</span>
                              </div>
                              <div>
                                <ChecklistCircle
                                  key={item.Id}
                                  completed={completedStatus[item.Id]}
                                  onClick={() => {}}
                                  size="sm"
                                  primaryColor={Canvas?.PrimaryColor}
                                />
                              </div>
                            </div>
                            <div>
                              <p
                                style={{
                                  color: Checkpoints?.CheckpointDescriptionColor,
                                  fontSize: "14px",
                                  lineHeight: "1.5",
                                  margin: "0px",
                                  wordBreak: "break-word",
                                  overflow: "hidden",
                                  textOverflow: "ellipsis",
                                  WebkitLineClamp: 2,
                                  WebkitBoxOrient: "vertical",
                                  display: "-webkit-box",
                                }}
                                className="qadpt-chkpopdesc"
                              >
                                {item.Description}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                        ))}
  
  
  
                      </div>
  
                    </div>
  
                    {/* Right side - Selected item details - only show when an item is selected */}
                    {/* {activeItem && ( */}
                    {isRightPanelVisible && (
                      <div style={{
                        width:"60%",
                        padding: '20px 20px 0 20px',
                      }} className='qadpt-chklft'>
                        <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            placeContent: 'end',
                            width: '100%',
                            gap: '6px'

                          }}
                          >
                        
                        <span dangerouslySetInnerHTML={{ __html: maximize }}  style={{
        background: '#e8e8e8',
        borderRadius: '50%',
        padding: '6px',
                          display: 'flex',
        cursor:"pointer"
                        }}

                              onClick={(e) => {
												
                                handleMaximize(); // Trigger dropdown open
                              }}
                            />

                            <span onClick={handleClose} style={{
                              background: '#e8e8e8',
                              borderRadius: '50%',
                              padding: '7px',
                              display: 'flex',
                              cursor: "pointer",
                              alignItems: "center",
                              placeContent: "center",
                            
                            }}
                              
                            >								<CloseIcon sx={{ height: "16px", width: "16px" }} /></span>

                          </div>
                       <div style={{ display: 'flex', alignItems: 'center', flexDirection: 'column', gap: '10px',  height: "calc(100% - 84px)" }}
              >
                         
                          													<div style={{
    									overflow: "hidden auto",display: "flex",
										alignItems: "center",
										flexDirection: "column",width:"-webkit-fill-available"}} >
                  
                          {selectedItem?.SupportingMedia?.length > 0 && (
                            <>
                          
                              {selectedItem.SupportingMedia.some(
                                (file: any) => file?.Base64?.startsWith("data:image")
                              ) && (
                                  <ImageCarousel
                                    selectedItem={selectedItem}
                                    activeItem={activeItem}
                                    images={selectedItem.SupportingMedia
                                      .filter((file: any) => file?.Base64?.startsWith("data:image"))
                                      .map((file: any) => file.Base64)
                                    }
                                  primaryColor={Canvas?.PrimaryColor}
                                  isMaximized={isMaximized}
                                />
                               
                                )}
  
  

                              {selectedItem.SupportingMedia.some(
                                (file: any) => file?.Base64?.startsWith("data:video")
                              ) &&
                                selectedItem.SupportingMedia
                                  .filter((file: any) => file?.Base64?.startsWith("data:video"))
                                  .map((file: any, index: number) => (
                                    <VideoPlayer key={index} videoFile={file.Base64}  isMaximized={isMaximized} />
                                  ))}
                            </>
                          )}
                           {(selectedItem?.SupportingMedia?.length === 0 || !selectedItem?.SupportingMedia)&& (
                      <div style={{    width:"auto",
                         height:"245px",
                      }}>
                          <span dangerouslySetInnerHTML={{ __html: chkdefault }} />
                          <div style={{color:"#8D8D8D"}}>Check tasks, stay organized, and finish strong!</div>
                      
                       </div>
      
      
                     
                      )}
                      
                          <div style={{ width: "100%", marginTop: "10px" }} className='qadpt-chkdesc'>
                            {selectedItem && (
                              <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                                <div style={{
                                  textAlign:isRTL?'right': 'left',
                                  display: 'flex',
                                  flexDirection: 'column',
                                  gap: '12px',
                                  // width: "530px" commented due to the style is nor present in preview na dcreation 
                                }}
                                >
                                  <div style={{
                                    fontSize: '16px',
                                    fontWeight: 600,
                                    color: '#333',
                                    overflow: "hidden",
                                    textOverflow: "ellipsis",
                                    whiteSpace: "nowrap",
                              wordBreak:"break-word"
                                  }}>{selectedItem.MediaTitle}</div>
  
  <div style={{
  color: "#8D8D8D",
  lineHeight: "1.5",
  fontSize: "14px",
  height: "5.7rem",
  marginBottom: "20px",
  display: "-webkit-box",
  overflow: "hidden",
  textOverflow: "ellipsis",
  WebkitLineClamp: 3,
  WebkitBoxOrient: "vertical",
  wordBreak: "break-word",
}}
>
                                    {selectedItem.MediaDescription}
                                  </div>
                                </div>
  
                               
                              </div>
                 
                            )}
                          </div>
                          </div>
                          </div>
                        <div style={{
                                  display: 'flex',
                                  gap: '12px',
                                  alignItems: "center",
                                  placeContent: "end",
                                    paddingBottom:"20px"
                                }} className='qadpt-btnsec'>

<button
  style={{
    backgroundColor: Canvas?.PrimaryColor ,
    borderRadius: "10px",
    padding: "9px 16px",
    color: "#fff",
    border: "none",
    cursor: isPublished ? "pointer" : "not-allowed"
  }}
  onClick={isPublished ? (e: any) => handleNavigate(selectedItem?.Id) : undefined}
  disabled={!isPublished} // Disable when isPublished is false
>
  {isPublished ? "Take Tour" : "Interaction Not Available"}
</button>

                              
{(selectedItem?.SupportingMedia?.length > 0 && !completedStatus[selectedItem?.Id]) && ( 
  <button
    style={{
      borderRadius: "10px",
      padding: "9px 16px",
      color: "var(--chkcolor)",
      border: "none",
      background: "#D3D9DA",
      cursor: "pointer"
    }}
    onClick={() => handleMarkAsCompleted(selectedItem?.Id)}
  >
    Mark as Completed
  </button>
)}

                                </div>
                      </div>
                    )}
                    {/* )} */}
                  </div>
                </div>
              </div>
            </div>
          )}




          {isMaximized && (
            <div 	style={{
              position: "fixed",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              zIndex: 998,
            }}>
            <div style={{
              position: 'fixed',
              inset: 0,
              display: 'flex',
              alignItems: 'center',
              // justifyContent: 'center',
              zIndex: 50
            }}>
           
  
              <div style={{
                boxShadow: "rgba(0, 0, 0, 0.1) 0px 20px 25px -5px, rgba(0, 0, 0, 0.04) 0px 10px 10px -5px",
                zIndex: 9,
                  marginTop: "8%",
                  marginBottom: "5%",
                  display: "flex",
									alignItems: "center",
									placeContent: "center",
									width: "100%",
              }} className='qadpt-chkpopup'>
                <div style={{
                  backgroundColor: Canvas?.BackgroundColor,
                  border: `${Canvas?.borderWidth}px solid ${Canvas?.BorderColor}`,
                  borderRadius: `${Canvas?.CornerRadius}px`,
										width: "calc(-250px + 100vw)",
										height: "calc(100vh - 140px)",
										overflow: "hidden auto",
  
                }}>
  
                  <div style={{
                    display: 'flex',
                    height: '100%',
                    width: "100%"
                  }} className='qadpt-chkcontent'>
                    {/* Left side - Checklist items */}
                
  
                    {/* Right side - Selected item details - only show when an item is selected */}
                    {/* {activeItem && ( */}
             
                    <div style={{
                      width: "100%",
                      padding: '20px 20px 0 20px',
                      }} className='qadpt-chklft'>
                      <div style={{ display: 'flex', alignItems: 'center', flexDirection: 'column', gap: '10px' }}
                      >
                         <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          placeContent: 'end',
                          width: '100%',
                          gap: '6px'
                        }}
                        >
                         
                             
                        <span style={{
        background: '#e8e8e8',
        borderRadius: '50%',
        padding: '6px',
                          display: 'flex',
        cursor:"pointer"
                        }}

                              onClick={(e) => {
												
                                handleMinimize(); // Trigger dropdown open
                              }}
                                                           >								<CloseIcon sx={{ height: "16px", width: "16px" }} /></span>

                        </div>
                        {(selectedItem?.SupportingMedia?.length === 0 || !selectedItem?.SupportingMedia) && (
                      <div style={{    width:"auto",
                      height:"255px",
                   }}>
                          <span dangerouslySetInnerHTML={{ __html: chkdefault }} />
                          <div style={{color:"#8D8D8D"}}>Check tasks, stay organized, and finish strong!</div>
                      
                       </div>
      
      
                     
                      )}
                        {selectedItem?.SupportingMedia?.length > 0 && (
                          <>
                            {selectedItem.SupportingMedia.some(
                              (file: any) => file?.Base64?.startsWith("data:image")
                            ) && (
                                <ImageCarousel
                                  selectedItem={selectedItem}
                                  activeItem={activeItem}
                                  images={selectedItem?.SupportingMedia
                                    .filter((file: any) => file?.Base64?.startsWith("data:image"))
                                    .map((file: any) => file.Base64)
                                  }
                                primaryColor={Canvas?.PrimaryColor}
                                isMaximized={isMaximized}
                                />
                              )}
  
  
                            {selectedItem.SupportingMedia.some(
                              (file: any) => file?.Base64?.startsWith("data:video")
                            ) &&
                              selectedItem.SupportingMedia
                                .filter((file: any) => file?.base64?.startsWith("data:video"))
                                .map((file: any, index: number) => (
                                  <VideoPlayer key={index} videoFile={file.base64}  isMaximized={isMaximized} />
                                ))}
                          </>
                        )}

<div style={{ width: "100%", marginTop: "10px" }} className='qadpt-chkdesc'>
                          {selectedItem && (
                            <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                              <div style={{
                                textAlign: 'left',
                                display: 'flex',
                                flexDirection: 'column',
                                gap: '12px',
                                width:"100%"
                              }}
                              >
                                <div style={{
                                fontSize: '16px',
                                fontWeight: 600,
                                color: '#333',
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                whiteSpace: "nowrap",
                          wordBreak:"break-word"
                                }}>{selectedItem.MediaTitle}</div>
  
                                <div className='qadpt-desc'
                                  style={{
                                    color: "#8D8D8D",
                                    lineHeight: "1.5",
                                    fontSize: "14px",
                                    height: "4.3rem",
                                    marginBottom: "10px",
                                    display: "-webkit-box",
                                    overflow: "hidden",
                                    textOverflow: "ellipsis",
                                    WebkitLineClamp: 3,
                                    WebkitBoxOrient: "vertical",
                                    wordBreak: "break-word",
                                  }}
                                >
                                  {selectedItem.MediaDescription}
                                </div>
                              </div>
  
                          <div style={{
                                display: 'flex',
                                gap: '12px',
                                alignItems: "center",
                                placeContent: "end",
                                 paddingBottom:"20px"
                              }} className='qadpt-btnsec'>
                                <button
                                  style={{ 
                                    backgroundColor: Canvas?.PrimaryColor, 
                                    borderRadius: "10px", 
                                    padding: "9px 16px", 
                                    color: "#fff", 
                                    border: "none", 
                                    cursor: isPublished ? "pointer" : "not-allowed" 
                                  }}
                                  onClick={isPublished ? (e: any) => handleNavigate(selectedItem?.Id) : undefined}
                                  disabled={!isPublished}
                                >
                                  {isPublished ? "Take Tour" : "Interaction Not Available"}
                                </button>
                                {(selectedItem?.SupportingMedia?.length > 0 && !completedStatus[selectedItem?.Id]) && (
                                  <button
                                    style={{
                                      borderRadius: "10px", padding: "9px 16px", color: "var(--chkcolor)", border: "none", background: "#D3D9DA", cursor: "pointer"
                                    }}
                                    onClick={() => handleMarkAsCompleted(selectedItem?.Id)}
                                  >
                                    Mark as Completed
                                  </button>
                                )}
                              </div>
                    </div>
                
                          )}
                  </div>
                </div>
              </div>
              </div>
              </div>
              </div>
              </div>
              </div>
          )}

       
        </>
      );
    };
export default ChecklistPopup;
export {CheckpointId}