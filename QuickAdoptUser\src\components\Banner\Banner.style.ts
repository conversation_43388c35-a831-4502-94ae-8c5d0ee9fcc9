import { SxProps, Theme } from "@mui/material/styles";
export const BannerWrapper: SxProps<Theme> = {
	position: "absolute",
	top: 0,
	right: 0,
	left: 0,
	height: "auto",
	backgroundColor: "#F2E9DA",
	display: "block",
	justifyContent: "center",
	alignItems: "center",
	//padding: "5px 10px",
	fontFamily: "inherit",
};
export const TextWrapper: SxProps<Theme> = {
	display: "flex",
	justifyContent: "center",
	color: "#0B151F",
	fontWeight: (theme) => theme.typography.fontWeightSemiBold,
	fontFamily: "inherit",
};

export const IconButtonSX: SxProps<Theme> = {
	color: "#0B151F",
	right:"7px"
};

export const InnerWrapper: SxProps<Theme> = {
	display: "flex",
	gap: "10px",
	alignItems: "center",
	justifyContent:"space-between"
};
export const ButtonSX: SxProps<Theme> = {
	backgroundColor: "#E97A35",
	color: "white",
	fontWeight: (theme) => theme.typography.fontWeightSemiBold,
	padding: "4px 12px",
};
export const HighLightedSx: SxProps<Theme> = {
	backgroundColor: "#F5BE50",
	fontWeight: "inherit",
};
