import React, { useState } from "react";

const MenuBar = () => {
    const [expandedMenus, setExpandedMenus] = useState<{ [key: string]: boolean }>({});

    const toggleMenu = (menuId: string) => {
        setExpandedMenus((prev) => ({
            ...prev,
            [menuId]: !prev[menuId],
        }));
    };

    const menuItems = [
        {
            id: "menu1",
            title: "Menu 1",
            options: [
                { id: "sub1", title: "Sub-option 1-1" },
                { id: "sub2", title: "Sub-option 1-2" },
                { id: "sub3", title: "Sub-option 1-3" },
            ],
        },
        {
            id: "menu2",
            title: "Menu 2",
            options: [
                { id: "sub1", title: "Sub-option 2-1" },
                {
                    id: "sub2",
                    title: "Sub-option 2-2",
                    subOptions: [
                        { id: "sub2-1", title: "Nested 2-2-1" },
                        { id: "sub2-2", title: "Nested 2-2-2" },
                    ],
                },
            ],
        },
        {
            id: "menu3",
            title: "Menu 3",
            options: [
                { id: "sub1", title: "Sub-option 3-1" },
                { id: "sub2", title: "Sub-option 3-2" },
            ],
        },
    ];

    return (
        <div style={{ width: "250px", padding: "10px", background: "#f0f0f0", borderRight: "1px solid #ccc" }}>
            <h3>Menu</h3>
            <ul style={{ listStyle: "none", padding: 0 }}>
                {menuItems.map((menu) => (
                    <li key={menu.id}>
                        <button
                            onClick={() => toggleMenu(menu.id)}
                            style={{
                                width: "100%",
                                textAlign: "left",
                                background: "none",
                                border: "none",
                                cursor: "pointer",
                                padding: "10px 0",
                                fontWeight: "bold",
                            }}
                        >
                            {menu.title}
                        </button>
                        {expandedMenus[menu.id] && (
                            <ul style={{ listStyle: "none", paddingLeft: "20px" }}>
                                {menu.options.map((option) => (
                                    <li key={option.id}>
                                        <span>{option.title}</span>
                                        {option.subOptions && (
                                            <ul style={{ listStyle: "none", paddingLeft: "20px" }}>
                                                {option.subOptions.map((subOption) => (
                                                    <li key={subOption.id}>{subOption.title}</li>
                                                ))}
                                            </ul>
                                        )}
                                    </li>
                                ))}
                            </ul>
                        )}
                    </li>
                ))}
            </ul>
        </div>
    );
};

export default MenuBar;
