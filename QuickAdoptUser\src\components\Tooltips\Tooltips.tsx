import React, { useEffect, useState, useRef, useCallback } from "react";
import {
	Button,
	Tooltip,
	Box,
	LinearProgress,
	Typography,
	tooltipClasses,
	TooltipProps,
	MobileStepper,
	Breadcrumbs,
} from "@mui/material";
import { CustomIconButton } from "../../components/Button";
import CloseIcon from "@mui/icons-material/Close";
import { styled } from "@mui/material/styles";
import { browser, trackUserEngagement, userData, version } from '../UserEngagement/userEngagementTracking';
import PerfectScrollbar from 'react-perfect-scrollbar';
import 'react-perfect-scrollbar/dist/css/styles.css';

interface ButtonAction {
	Action: string;
	ActionValue: string;
	TargetUrl: string;
}
interface ButtonProperties {
	Padding: number;
	Width: number;
	Font: number;
	FontSize: number;
	ButtonTextColor: string;
	ButtonBackgroundColor: string;
	ButtonBorderColor: string;
}
interface Canvas {
	BackgroundColor: string;
	BorderColor: string;
	BorderSize: string;
	Padding: string;
	Position: string;
	Radius: string;
	Width: string;
	Zindex: string;
}
interface Modal {
	DismissOption: boolean;
	IncludeRequisiteButtons: boolean;
	InteractionWithPopup: boolean;
	ModalPlacedOn: string;
}

interface ButtonData {
	ButtonStyle: string;
	ButtonName: string;
	Alignment: string;
	BackgroundColor: string;
	ButtonAction: ButtonAction;
	Padding: {
		Top: number;
		Right: number;
		Bottom: number;
		Left: number;
	};
	ButtonProperties: ButtonProperties;
	Id: string;
}
interface CustomImage {
	AltText: string;
	BackgroundColor: string;
	Fill: string;
	Fit: string;
	SectionHeight: string;
	Url: string;
}
interface Design {
	NextStep: string;
	ButtonName: string;
	ElementPath: string;
	Id: string;
}

interface Step {
	xpath: string;
	content: string | JSX.Element;
	targetUrl: string;
	imageUrl: string;
	buttonData: ButtonData[];
	overlay: boolean;
	positionXAxisOffset: string;
	positionYAxisOffset: string;
	canvas: Canvas;
	modal: Modal;
	imageproperties: any;
	autoposition: any;
	elementclick: Design;
	PossibleElementPath: string;
	stepTitle: string;

}
interface TooltipGuideProps {
	steps: Step[];
	onClose: () => void;
	tooltipConfig: any;
	currentStep: any;
	setCurrentStep: any;
	key: any;
	data: any;
	getCurrentStep: (data: string) => void;
	isFromAi: boolean;
	onRestart?: () => void; // Added onRestart prop
}
const CustomWidthTooltip = styled(
	({ className, data, canvasStyle, buttonData, hasContent, hasOnlyText, hasOnlyButton, ...props }: TooltipProps & {
		canvasStyle?: any;
		data?: any;
		buttonData?: any[];
		hasContent?: boolean;
		hasOnlyText?: boolean;
		hasOnlyButton?: boolean;
	}) => (
		<Tooltip
			{...props}
			classes={{
				popper: `${className} ${data?.GuideType === "Tour" ? "qadpt-turendusr qadpt-tltendusr" : "qadpt-tltendusr"}`
			}}
			id="Tooltip-unique"
		/>
	)
)(({ canvasStyle, buttonData, hasContent, hasOnlyText, hasOnlyButton }: {
	canvasStyle: any;
	buttonData?: any[];
	hasContent?: boolean;
	hasOnlyText?: boolean;
	hasOnlyButton?: boolean;
}) => {
	// Determine if we have a single button with no other content
	const hasSingleButton = buttonData?.length === 1;
	const singleButtonWithNoContent = hasOnlyButton || (hasSingleButton && !hasContent);

	// Get button background color for the arrow if there's a single button
	const buttonBackgroundColor = hasSingleButton
		? buttonData?.[0]?.ButtonProperties?.ButtonBackgroundColor
		: undefined;

	// Determine the background color for the tooltip
	const tooltipBackgroundColor = singleButtonWithNoContent && buttonBackgroundColor
		? buttonBackgroundColor
		: canvasStyle?.BackgroundColor || "#ffffff";

	// Determine the background color for the arrow
	const arrowBackgroundColor = singleButtonWithNoContent && buttonBackgroundColor
		? buttonBackgroundColor
		: canvasStyle?.BackgroundColor || "#ffffff";

	return {
		[`& .${tooltipClasses.tooltip}`]: {
			backgroundColor: `${tooltipBackgroundColor} !important`,
			padding: canvasStyle?.Padding || "0px",
			// padding: "0 !important",
			borderRadius: `${canvasStyle?.Radius ? canvasStyle.Radius : canvasStyle?.BorderRadius ? `${canvasStyle.BorderRadius}px` : "8px"} !important`,
			boxShadow: `${"0px 4px 12px rgba(0, 0, 0, 0.2)"} !important`,
			border: canvasStyle?.BorderSize && canvasStyle?.BorderSize !== "0px" ? `${canvasStyle?.BorderSize} solid ${canvasStyle?.BorderColor || "transparent"}` : "none",
			// Adjust width based on content type
			maxWidth: `${canvasStyle?.Width} !important` || "300px",
			left: `${canvasStyle?.XAxisOffset || "auto"} !important`,
			bottom: `${canvasStyle?.YAxisOffset || "auto"} !important`,
			//minWidth: `${canvasStyle?.Width} !important`||"300px" ,
			// Remove extra padding when there's only a button
			...(singleButtonWithNoContent && {
				padding: "0 !important",
			}),
			// Adjust padding for text-only content
			...(hasOnlyText && {
				padding: "8px 12px !important",
				maxWidth: `${canvasStyle?.Width} !important` || "300px"
			})
		},
		[`& .${tooltipClasses.arrow}`]: {
			color: `${arrowBackgroundColor} !important`,
			fontSize: "12px",
			"&:before": {
				outlineWidth: canvasStyle?.BorderSize && canvasStyle?.BorderSize !== "0px" ? `${canvasStyle?.BorderSize}` : "0px", // This controls the width of the border of the arrow
				outlineColor: canvasStyle?.BorderColor || "transparent", // This controls the color of the border of the arrow
				outlineStyle: "solid", // Ensure the border is applied properly
				Width: "90% !important",
				height: "100%"
			},
		},
		[`&.MuiTooltip-popper`]: {
			zIndex: canvasStyle?.Zindex || 11000, // Tooltip z-index
		},
	};
});
const TooltipGuide: React.FC<TooltipGuideProps> = ({
	steps,
	onClose,
	tooltipConfig,
	currentStep,
	setCurrentStep,
	key,
	data,
	getCurrentStep,
	isFromAi,
	onRestart
}) => {
let rect: any;
const [currentStepIndex, setCurrentStepIndex] = useState(0);
const latestStepIndexRef = useRef(currentStepIndex);
useEffect(() => {
	latestStepIndexRef.current = currentStepIndex;
}, [currentStepIndex]);

	const primaryColor = "#5F9EA0";
	const totalSteps = data?.GuideStep.length;
	const [targetElement, setTargetElement] = useState<HTMLElement | null>(null);
	const [tooltipPosition, setTooltipPosition] = useState({ top: 0, left: 0 });
	const [tooltipPlacement, setTooltipPlacement] = useState<"top" | "left" | "right" | "bottom">("top");
	const [isElementVisible, setIsElementVisible] = useState(false);
	const [isWithinPublishWindow, setIsWithinPublishWindow] = useState(true);
	const currentStepData = steps[currentStepIndex];
	const observerRef = useRef<MutationObserver | null>(null);
	const [initialTime] = useState(Date.now());
	const prevPositionRef = useRef({ top: 0, left: 0 })
	const prevPlacementRef = useRef<"top" | "left" | "right" | "bottom">("top")

	// Scheduling logic: Only show tooltip if within publish window
	useEffect(() => {
		if (!data) {
			setIsWithinPublishWindow(true);
			return;
		}
		const currentDate = new Date();
		const publishDate = data?.PublishDate ? new Date(data.PublishDate) : null;
		const unpublishDate = data?.UnPublishDate ? new Date(data.UnPublishDate) : null;
		if (
			publishDate &&
			currentDate < publishDate
		) {
			setIsWithinPublishWindow(false);
			return;
		}
		if (
			unpublishDate &&
			currentDate > unpublishDate
		) {
			setIsWithinPublishWindow(false);
			return;
		}
		setIsWithinPublishWindow(true);
	}, [data]);

	// State to track if scrolling is needed
	const [needsScrolling, setNeedsScrolling] = useState(false);
	const contentRef = useRef<HTMLDivElement>(null);
	const scrollbarRef = useRef<any>(null);
	const calculateBestPosition = useCallback((element: HTMLElement): "top" | "left" | "right" | "bottom" => {
		const rect = element.getBoundingClientRect();
		const viewportWidth = window.innerWidth;
		const viewportHeight = window.innerHeight;

		const spaceTop = rect.top;
		const spaceBottom = viewportHeight - rect.bottom;
		const spaceLeft = rect.left;
		const spaceRight = viewportWidth - rect.right;

		const maxSpace = Math.max(spaceTop, spaceBottom, spaceLeft, spaceRight);

		if (maxSpace === spaceTop) return "top";
		if (maxSpace === spaceBottom) return "bottom";
		if (maxSpace === spaceLeft) return "left";
		return "right";
	}, []);


	const findScrollableContainer = (element: HTMLElement): HTMLElement | null => {
		let parent = element.parentElement;
		while (parent && parent !== document.body) {
			const style = window.getComputedStyle(parent);
			const classList = parent.classList;

			// Check for standard scrollable containers
			if (style.overflow === 'auto' || style.overflow === 'scroll' ||
				style.overflowY === 'auto' || style.overflowY === 'scroll') {
				return parent;
			}

			// Check for PerfectScrollbar containers (your sidebar)
			if (classList.contains('navbar-scroll-container') ||
				classList.contains('ps') ||
				classList.contains('fuseperfectscrollbar')) {
				return parent;
			}

			// Check for fuse sidebar containers
			if (classList.contains('fuse-sidebar') ||
				classList.contains('navbar-fuse-sidebar')) {
				// Look for the actual scrollable child
				const scrollableChild = parent.querySelector('.navbar-scroll-container, .ps, [fuseperfectscrollbar]');
				if (scrollableChild instanceof HTMLElement) {
					return scrollableChild;
				}
				return parent;
			}

			parent = parent.parentElement;
		}
		return null;
	};


	// Enhanced function to open sidemenus and accordions with DOM change detection
	// const openContainersForElement = useCallback(async (element: HTMLElement): Promise<void> => {
	// 	console.log("🔍 Checking if element is inside collapsible containers...");

	// 	let parent = element.parentElement;
	// 	const containersToOpen: HTMLElement[] = [];

	// 	// Traverse up the DOM tree to find all collapsible containers
	// 	while (parent && parent !== document.body) {
	// 		// Check for various types of collapsible containers

	// 		// 1. EJS Accordion containers
	// 		if (parent.classList.contains('e-acrdn-item')) {
	// 			if (!parent.classList.contains('e-expand-state')) {
	// 				const header = parent.querySelector('.e-acrdn-header');
	// 				if (header instanceof HTMLElement) {
	// 					containersToOpen.push(header);
	// 					console.log("📁 Found collapsed accordion to expand");
	// 				}
	// 			}
	// 		}

	// 		// 2. Bootstrap/Material UI Accordion containers
	// 		else if (parent.classList.contains('accordion-item') || parent.classList.contains('MuiAccordion-root')) {
	// 			const isExpanded = parent.classList.contains('show') ||
	// 							 parent.classList.contains('Mui-expanded') ||
	// 							 parent.getAttribute('aria-expanded') === 'true';
	// 			if (!isExpanded) {
	// 				const header = parent.querySelector('.accordion-button, .MuiAccordionSummary-root, [role="button"]');
	// 				if (header instanceof HTMLElement) {
	// 					containersToOpen.push(header);
	// 					console.log("📁 Found collapsed accordion/expansion panel to expand");
	// 				}
	// 			}
	// 		}

	// 		// 3. Sidemenu containers (various implementations)
	// 		else if (parent.classList.contains('sidebar') ||
	// 				parent.classList.contains('sidenav') ||
	// 				parent.classList.contains('side-menu') ||
	// 				parent.classList.contains('nav-menu') ||
	// 				parent.classList.contains('fuse-sidebar')) {

	// 			// Check if sidemenu is collapsed
	// 			const isCollapsed = parent.classList.contains('collapsed') ||
	// 							   parent.classList.contains('closed') ||
	// 							   parent.classList.contains('minimized') ||
	// 							   parent.style.display === 'none' ||
	// 							   parent.style.visibility === 'hidden' ||
	// 							   parent.offsetWidth < 50; // Assume collapsed if very narrow

	// 			if (isCollapsed) {
	// 				// Look for menu toggle button
	// 				const toggleButton = document.querySelector('.menu-toggle, .sidebar-toggle, .nav-toggle, [data-toggle="sidebar"]') ||
	// 									parent.querySelector('.toggle-btn, .menu-btn');
	// 				if (toggleButton instanceof HTMLElement) {
	// 					containersToOpen.push(toggleButton);
	// 					console.log("📱 Found collapsed sidemenu to open");
	// 				}
	// 			}
	// 		}

	// 		// 4. Collapsible menu items
	// 		else if (parent.classList.contains('nav-item') || parent.classList.contains('menu-item')) {
	// 			const hasSubmenu = parent.querySelector('.submenu, .dropdown-menu, .nav-submenu');
	// 			if (hasSubmenu) {
	// 				const isExpanded = parent.classList.contains('open') ||
	// 								  parent.classList.contains('expanded') ||
	// 								  parent.classList.contains('show') ||
	// 								  parent.getAttribute('aria-expanded') === 'true';
	// 				if (!isExpanded) {
	// 					const trigger = parent.querySelector('a, button, .nav-link') as HTMLElement;
	// 					if (trigger) {
	// 						containersToOpen.push(trigger);
	// 						console.log("📂 Found collapsed menu item to expand");
	// 					}
	// 				}
	// 			}
	// 		}

	// 		// 5. Generic collapsible containers
	// 		else if (parent.hasAttribute('data-toggle') ||
	// 				parent.classList.contains('collapsible') ||
	// 				parent.classList.contains('expandable')) {
	// 			const isExpanded = parent.classList.contains('expanded') ||
	// 							  parent.classList.contains('open') ||
	// 							  parent.getAttribute('aria-expanded') === 'true';
	// 			if (!isExpanded) {
	// 				const trigger = parent.querySelector('[data-toggle], .toggle, .expand-btn') as HTMLElement;
	// 				if (trigger) {
	// 					containersToOpen.push(trigger);
	// 					console.log("📋 Found generic collapsible container to expand");
	// 				}
	// 			}
	// 		}

	// 		parent = parent.parentElement;
	// 	}

	// 	// Open all found containers in sequence
	// 	if (containersToOpen.length > 0) {
	// 		console.log(`🚀 Opening ${containersToOpen.length} container(s) for target element...`);

	// 		for (const container of containersToOpen) {
	// 			await openSingleContainer(container);
	// 		}

	// 		console.log("✅ All containers opened successfully");
	// 	} else {
	// 		console.log("ℹ️ No collapsible containers found - element is already accessible");
	// 	}
	// }, []);

	// Helper function to open a single container and wait for DOM changes
	const openSingleContainer = async (trigger: HTMLElement): Promise<void> => {
		return new Promise((resolve) => {
			console.log("🔓 Opening container:", trigger.className || trigger.tagName);

			// Set up DOM change observer to detect when opening is complete
			const observer = new MutationObserver((mutations) => {
				// Check if any significant DOM changes occurred
				const hasSignificantChanges = mutations.some(mutation =>
					(mutation.type === 'childList' && mutation.addedNodes.length > 0) ||
					(mutation.type === 'attributes' && (
						mutation.attributeName === 'class' ||
						mutation.attributeName === 'style' ||
						mutation.attributeName === 'aria-expanded'
					))
				);

				if (hasSignificantChanges) {
					console.log("📊 DOM changes detected - container opening completed");
					observer.disconnect();
					// Additional wait to ensure animations complete
					setTimeout(resolve, 200);
				}
			});

			// Start observing DOM changes
			observer.observe(document.body, {
				childList: true,
				subtree: true,
				attributes: true,
				attributeFilter: ['class', 'style', 'aria-expanded']
			});

			// Trigger the container opening
			try {
				trigger.click();
				console.log("🖱️ Container trigger clicked");
			} catch (error) {
				console.warn("⚠️ Failed to click container trigger:", error);
				observer.disconnect();
				resolve();
				return;
			}

			// Fallback timeout in case DOM changes aren't detected
			setTimeout(() => {
				console.log("⏰ Fallback timeout reached - assuming container opened");
				observer.disconnect();
				resolve();
			}, 1000); // 1 second fallback
		});
	};


	// Enhanced scrolling function with multiple fallback methods for cross-environment compatibility
	const smoothScrollTo = (element: HTMLElement, targetTop: number, duration: number = 300) => {
		// Ensure targetTop is within valid bounds
		const maxScroll = element.scrollHeight - element.clientHeight;
		const clampedTargetTop = Math.max(0, Math.min(targetTop, maxScroll));

		// Method 1: Try CSS scroll-behavior first (most reliable)
		try {
			const originalBehavior = element.style.scrollBehavior;
			element.style.scrollBehavior = 'smooth';
			element.scrollTop = clampedTargetTop;

			// Reset after animation completes
			setTimeout(() => {
				element.style.scrollBehavior = originalBehavior || 'auto';
			}, duration + 100);
			return;
		} catch (error) {
			console.log("CSS scroll-behavior failed, trying alternative methods");
		}

		// Method 2: Try requestAnimationFrame animation
		try {
			const startTop = element.scrollTop;
			const distance = clampedTargetTop - startTop;

			// Skip animation if distance is very small
			if (Math.abs(distance) < 5) {
				element.scrollTop = clampedTargetTop;
				return;
			}

			const startTime = performance.now();

			const animateScroll = (currentTime: number) => {
				const elapsed = currentTime - startTime;
				const progress = Math.min(elapsed / duration, 1);

				// Easing function (ease-out)
				const easeOut = 1 - Math.pow(1 - progress, 3);

				element.scrollTop = startTop + (distance * easeOut);

				if (progress < 1) {
					requestAnimationFrame(animateScroll);
				}
			};

			requestAnimationFrame(animateScroll);
		} catch (error) {
			console.log("RequestAnimationFrame failed, using direct assignment");
			// Method 3: Direct assignment as final fallback
			element.scrollTop = clampedTargetTop;
		}
	};

	// Enhanced reusable element polling function with exponential backoff and better timing
	const pollForElement = useCallback((
		xpath: string,
		possibleElementPath: string,
		onElementFound: (element: HTMLElement) => void,
		maxAttempts: number = 30,
		initialIntervalMs: number = 16, // Start with one frame
		logPrefix: string = "Element",
		onElementNotFound?: () => void
	) => {
		let elementCheckTimeout: NodeJS.Timeout | null = null;
		let attempts = 0;
		let currentInterval = initialIntervalMs;
		let isCleanedUp = false;

		const checkElement = () => {
			if (isCleanedUp) return;

			attempts++;

			// Use requestAnimationFrame for better timing synchronization
			requestAnimationFrame(() => {
				if (isCleanedUp) return;

				const element = getElementByXPath(xpath, possibleElementPath);

				// Enhanced element validation with DOM stability check
				if (element && validateElementPosition(element) && isDOMStable(element)) {
					// Element found and valid, clear timeout and execute callback
					if (elementCheckTimeout) {
						clearTimeout(elementCheckTimeout);
						elementCheckTimeout = null;
					}
					console.log(`${logPrefix} found after ${attempts} attempts (${attempts * initialIntervalMs}ms total)`);

					// Ensure DOM is fully stable before proceeding
					requestAnimationFrame(() => {
						if (!isCleanedUp) {
							onElementFound(element);
						}
					});

				} else if (attempts >= maxAttempts) {
					// Max attempts reached, clear timeout
					if (elementCheckTimeout) {
						clearTimeout(elementCheckTimeout);
						elementCheckTimeout = null;
					}
					console.log(`${logPrefix} not found after ${maxAttempts} attempts, giving up`);
					// Call the optional callback for when element is not found
					if (onElementNotFound && !isCleanedUp) {
						onElementNotFound();
					}
				} else {
					// Continue polling with exponential backoff (capped at 200ms)
					currentInterval = Math.min(currentInterval * 1.2, 200);
					if (!isCleanedUp) {
						elementCheckTimeout = setTimeout(checkElement, currentInterval);
					}
				}
			});
		};

		// Start the polling with initial delay to allow current execution to complete
		elementCheckTimeout = setTimeout(checkElement, initialIntervalMs);

		// Return cleanup function
		return () => {
			isCleanedUp = true;
			if (elementCheckTimeout) {
				clearTimeout(elementCheckTimeout);
				elementCheckTimeout = null;
			}
		};
	}, []);

	// Enhanced DOM stability check to ensure element is ready for interaction
	const isDOMStable = (element: HTMLElement): boolean => {
		try {
			// Check if element is properly attached to DOM
			if (!document.contains(element)) {
				return false;
			}

			// Check if element has valid computed styles (more lenient)
			const computedStyle = window.getComputedStyle(element);
			if (!computedStyle) {
				return false;
			}

			// Allow hidden elements but check if they have dimensions
			const rect = element.getBoundingClientRect();
			if (rect.width === 0 && rect.height === 0) {
				// Element has no dimensions, check if it's intentionally hidden
				if (computedStyle.display === 'none') {
					return false;
				}
			}

			// More lenient check for animations - don't block on transitions/animations
			// Just ensure element has some dimensions
			return rect.width > 0 || rect.height > 0;
		} catch (error) {
			console.warn("DOM stability check failed, assuming stable:", error);
			// Be more lenient - if check fails, assume element is stable
			return true;
		}
	};

	// Enhanced cross-environment scrolling function
	const universalScrollTo = (element: HTMLElement | Window, options: { top?: number; left?: number; behavior?: 'smooth' | 'auto' }) => {
		const isWindow = element === window;
		const targetElement = isWindow ? document.documentElement : element as HTMLElement;

		// Method 1: Try native scrollTo if available and not blocked
		if (!isWindow && 'scrollTo' in element && typeof (element as any).scrollTo === 'function') {
			try {
				(element as any).scrollTo(options);
				return true;
			} catch (error) {
				console.log("Native scrollTo blocked or failed:", error);
			}
		}

		// Method 2: Try window.scrollTo for window scrolling
		if (isWindow) {
			try {
				window.scrollTo(options);
				return true;
			} catch (error) {
				console.log("Window scrollTo blocked or failed:", error);
			}
		}

		// Method 3: Use CSS scroll-behavior + direct assignment
		try {
			const originalBehavior = targetElement.style.scrollBehavior;
			if (options.behavior === 'smooth') {
				targetElement.style.scrollBehavior = 'smooth';
			}

			if (options.top !== undefined) {
				targetElement.scrollTop = options.top;
			}
			if (options.left !== undefined) {
				targetElement.scrollLeft = options.left;
			}

			// Reset behavior after animation
			if (options.behavior === 'smooth') {
				setTimeout(() => {
					targetElement.style.scrollBehavior = originalBehavior || 'auto';
				}, 1000);
			}
			return true;
		} catch (error) {
			console.log("CSS scroll-behavior failed:", error);
		}

		// Method 4: Direct property assignment (final fallback)
		try {
			if (options.top !== undefined) {
				targetElement.scrollTop = options.top;
			}
			if (options.left !== undefined) {
				targetElement.scrollLeft = options.left;
			}
			return true;
		} catch (error) {
			console.log("Direct property assignment failed:", error);
			return false;
		}
	};

	// Scroll operation queue to prevent conflicts
	const scrollOperationQueue = useRef<Promise<void>>(Promise.resolve());

	const scrollToTargetElement = useCallback(async (targetElement: HTMLElement, placement: "top" | "left" | "right" | "bottom", stepData?: Step) => {
		if (!targetElement) {
			console.log("ScrollToTargetElement: No target element provided");
			return;
		}

		// Queue this scroll operation to prevent conflicts
		scrollOperationQueue.current = scrollOperationQueue.current.then(async () => {
			console.log("ScrollToTargetElement: Scrolling to element", targetElement, "with placement", placement);

			// Wait for any pending DOM updates
			await new Promise(resolve => requestAnimationFrame(resolve));

			// First, open any collapsible containers (sidemenus, accordions, etc.)
			//await openContainersForElement(targetElement);

			// Additional wait to ensure DOM stability after accordion expansion
			await new Promise(resolve => setTimeout(resolve, 100));

			// Find if element is inside a scrollable container
			const scrollableContainer = findScrollableContainer(targetElement);
			console.log("ScrollToTargetElement: Found scrollable container:", scrollableContainer);

			if (scrollableContainer) {
				// Handle scrolling within a container using scrollIntoView with fallback
				const containerRect = scrollableContainer.getBoundingClientRect();
				const elementRect = targetElement.getBoundingClientRect();

				// Check if element is visible within container
				const isElementVisibleInContainer =
					elementRect.top >= containerRect.top &&
					elementRect.bottom <= containerRect.bottom;

				if (!isElementVisibleInContainer) {
					console.log("ScrollToTargetElement: Element not visible in container, scrolling...");

					// Enhanced multi-method approach for container scrolling
					let scrollSuccess = false;

					// Method 1: Try scrollIntoView with multiple attempts
					try {
						// Determine scroll block based on placement
						let scrollBlock: ScrollLogicalPosition = 'center';
						switch (placement) {
							case "top":
								scrollBlock = 'end'; // Element at bottom of container to show tooltip above
								break;
							case "bottom":
								scrollBlock = 'start'; // Element at top of container to show tooltip below
								break;
							case "left":
							case "right":
								scrollBlock = 'center'; // Element centered for side tooltips
								break;
						}

					// Try different scrollIntoView configurations
					const scrollOptions = [
						{ behavior: 'smooth' as const, block: scrollBlock, inline: 'nearest' as const },
						{ behavior: 'auto' as const, block: scrollBlock, inline: 'nearest' as const },
						{ block: scrollBlock, inline: 'nearest' as const }, // Without behavior
						{ behavior: 'smooth' as const, block: 'center' as const }, // Simplified
						{ block: 'center' as const } // Most basic
					];

					for (const options of scrollOptions) {
						try {
							// Store initial position to verify scroll actually happened
							const initialScrollTop = scrollableContainer.scrollTop;

							targetElement.scrollIntoView(options);

							// Wait a moment for scroll to start
							await new Promise(resolve => setTimeout(resolve, 50));

							// Check if scroll position changed or element is now visible
							const newScrollTop = scrollableContainer.scrollTop;
							const scrollChanged = Math.abs(newScrollTop - initialScrollTop) > 5;
							const elementNowVisible = isElementInViewport(targetElement);

							if (scrollChanged || elementNowVisible) {
								scrollSuccess = true;
								console.log("ScrollToTargetElement: scrollIntoView succeeded with options:", options, {
									scrollChanged,
									elementNowVisible,
									scrollDelta: newScrollTop - initialScrollTop
								});
								break;
							} else {
								console.log("ScrollToTargetElement: scrollIntoView had no effect with options:", options);
							}
						} catch (e) {
							console.log("ScrollToTargetElement: scrollIntoView failed with options:", options, e);
						}
					}
				} catch (error) {
					console.log("ScrollToTargetElement: All scrollIntoView attempts failed:", error);
				}

					// Method 2: If scrollIntoView failed, try universal scrollTo
					if (!scrollSuccess) {
						try {
							const elementTopInContainer = elementRect.top - containerRect.top + scrollableContainer.scrollTop;
							const tooltipHeight = 200;
							const padding = 20;
							let targetScrollTop = scrollableContainer.scrollTop;

						switch (placement) {
							case "top":
								const requiredTopSpace = tooltipHeight + padding;
								if (elementRect.top - containerRect.top < requiredTopSpace) {
									targetScrollTop = Math.max(0, elementTopInContainer - requiredTopSpace);
								}
								break;
							case "bottom":
								const requiredBottomSpace = tooltipHeight + padding;
								if (elementRect.bottom + requiredBottomSpace > containerRect.bottom) {
									targetScrollTop = elementTopInContainer + elementRect.height + requiredBottomSpace - containerRect.height;
								}
								break;
							case "left":
							case "right":
								const containerCenter = containerRect.height / 2;
								const elementCenter = elementTopInContainer + (elementRect.height / 2);
								targetScrollTop = elementCenter - containerCenter;
								break;
						}

						// Try universalScrollTo first
						scrollSuccess = universalScrollTo(scrollableContainer, {
							top: targetScrollTop,
							behavior: 'smooth'
						});

						if (!scrollSuccess) {
							// Final fallback: custom smooth scroll
							smoothScrollTo(scrollableContainer, targetScrollTop);
							scrollSuccess = true;
						}

							console.log("ScrollToTargetElement: Used fallback scrolling method");
						} catch (error) {
							console.log("ScrollToTargetElement: All scrolling methods failed:", error);
						}
					}
					// Wait for scroll animation to complete and verify success
					await new Promise(resolve => setTimeout(resolve, 300));

					// Verify scroll was successful by checking element visibility again
					const postScrollRect = targetElement.getBoundingClientRect();
					const isNowVisible = isElementInViewport(targetElement);
					console.log("ScrollToTargetElement: Post-scroll verification:", {
						isNowVisible,
						elementPosition: {
							top: Math.round(postScrollRect.top),
							bottom: Math.round(postScrollRect.bottom),
							left: Math.round(postScrollRect.left),
							right: Math.round(postScrollRect.right)
						}
					});
				} else {
					console.log("ScrollToTargetElement: Element already visible in container");
				}

				// Also ensure the container itself is visible on the page
				const containerRect2 = scrollableContainer.getBoundingClientRect();
				if (containerRect2.top < 0 || containerRect2.bottom > window.innerHeight) {
					console.log("ScrollToTargetElement: Scrolling page to show container");
					scrollableContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
					await new Promise(resolve => setTimeout(resolve, 300));
				}

			} else {
				// Handle page-level scrolling with enhanced compatibility
				console.log("ScrollToTargetElement: Using page-level scrolling");

				let scrollSuccess = false;

				// Method 1: Try multiple scrollIntoView configurations
				try {
					// Determine scroll block based on placement
					let scrollBlock: ScrollLogicalPosition = 'center';
					switch (placement) {
						case "top":
							scrollBlock = 'end'; // Element at bottom of viewport to show tooltip above
							break;
						case "bottom":
							scrollBlock = 'start'; // Element at top of viewport to show tooltip below
							break;
						case "left":
						case "right":
							scrollBlock = 'center'; // Element centered for side tooltips
							break;
					}

					// Try different scrollIntoView configurations for page scrolling
					const pageScrollOptions = [
						{ behavior: 'smooth' as const, block: scrollBlock, inline: 'center' as const },
						{ behavior: 'auto' as const, block: scrollBlock, inline: 'center' as const },
						{ block: scrollBlock, inline: 'center' as const }, // Without behavior
						{ behavior: 'smooth' as const, block: 'center' as const }, // Simplified
						{ block: 'center' as const }, // Most basic
						{ behavior: 'smooth' as const }, // Very basic
						{} // Completely basic
					];

					for (const options of pageScrollOptions) {
						try {
							// Store initial scroll position to verify scroll actually happened
							const initialScrollY = window.scrollY;

						targetElement.scrollIntoView(options);

						// Wait a moment for scroll to start
						await new Promise(resolve => setTimeout(resolve, 50));

						// Check if scroll position changed or element is now visible
						const newScrollY = window.scrollY;
						const scrollChanged = Math.abs(newScrollY - initialScrollY) > 5;
						const elementNowVisible = isElementInViewport(targetElement);

							if (scrollChanged || elementNowVisible) {
								scrollSuccess = true;
								console.log("ScrollToTargetElement: Page scrollIntoView succeeded with options:", options, {
									scrollChanged,
									elementNowVisible,
									scrollDelta: newScrollY - initialScrollY
								});
								break;
							} else {
								console.log("ScrollToTargetElement: Page scrollIntoView had no effect with options:", options);
							}
						} catch (e) {
							console.log("ScrollToTargetElement: Page scrollIntoView failed with options:", options, e);
						}
					}
				} catch (error) {
					console.log("ScrollToTargetElement: All page scrollIntoView attempts failed:", error);
				}

				// Method 2: If scrollIntoView failed, try universal window scrolling
				if (!scrollSuccess) {
					try {
						const rect = targetElement.getBoundingClientRect();
						const viewportHeight = window.innerHeight;
						const currentScrollY = window.scrollY;
						const elementTop = rect.top + currentScrollY;
						const tooltipHeight = 200;
						const padding = 20;

						let targetScrollY = currentScrollY;

						switch (placement) {
							case "top":
								if (rect.top < tooltipHeight + padding) {
									targetScrollY = Math.max(0, elementTop - tooltipHeight - padding);
								}
								break;
							case "bottom":
								if (rect.bottom + tooltipHeight + padding > viewportHeight) {
									targetScrollY = elementTop + rect.height + tooltipHeight + padding - viewportHeight;
								}
								break;
							case "left":
							case "right":
								targetScrollY = elementTop + (rect.height / 2) - (viewportHeight / 2);
								break;
						}

						targetScrollY = Math.max(0, Math.min(targetScrollY, document.documentElement.scrollHeight - viewportHeight));

						// Try universalScrollTo for window
						scrollSuccess = universalScrollTo(window, {
							top: targetScrollY,
							behavior: 'smooth'
						});

						console.log("ScrollToTargetElement: Used universal window scrolling, success:", scrollSuccess);
					} catch (error) {
						console.log("ScrollToTargetElement: All page scrolling methods failed:", error);
					}
				}

				// Wait for scroll animation to complete
				if (scrollSuccess) {
					await new Promise(resolve => setTimeout(resolve, 300));

					// Final verification and fallback if still not visible
					const postPageScrollRect = targetElement.getBoundingClientRect();
					const isNowVisible = isElementInViewport(targetElement);

					if (!isNowVisible) {
						console.log("ScrollToTargetElement: Element still not visible after page scroll, trying final fallback");
						try {
							// Last resort: simple scrollIntoView without options
							targetElement.scrollIntoView();
							await new Promise(resolve => setTimeout(resolve, 200));
						} catch (error) {
							console.log("ScrollToTargetElement: Final fallback also failed:", error);
						}
					}

					console.log("ScrollToTargetElement: Final page scroll verification:", {
						isNowVisible,
						elementPosition: {
							top: Math.round(postPageScrollRect.top),
							bottom: Math.round(postPageScrollRect.bottom),
							left: Math.round(postPageScrollRect.left),
							right: Math.round(postPageScrollRect.right)
						}
					});
				}
			}

			// Final verification that element is visible (more lenient check)
			const finalRect = targetElement.getBoundingClientRect();
			const viewportHeight = window.innerHeight;
			const viewportWidth = window.innerWidth;

			// More lenient visibility check - element just needs to be partially visible
			const isVisible = finalRect.bottom > 0 &&
				finalRect.top < viewportHeight &&
				finalRect.right > 0 &&
				finalRect.left < viewportWidth;

			// Additional check for reasonable visibility (at least 50% of element visible)
			const visibleHeight = Math.min(finalRect.bottom, viewportHeight) - Math.max(finalRect.top, 0);
			const visibleWidth = Math.min(finalRect.right, viewportWidth) - Math.max(finalRect.left, 0);
			const elementArea = finalRect.width * finalRect.height;
			const visibleArea = Math.max(0, visibleHeight) * Math.max(0, visibleWidth);
			const visibilityRatio = elementArea > 0 ? visibleArea / elementArea : 0;

			console.log("ScrollToTargetElement: Final element visibility check:", {
				isVisible,
				visibilityRatio: Math.round(visibilityRatio * 100) + '%',
				elementRect: {
					top: Math.round(finalRect.top),
					bottom: Math.round(finalRect.bottom),
					left: Math.round(finalRect.left),
					right: Math.round(finalRect.right)
				},
				viewport: { width: viewportWidth, height: viewportHeight }
			});

			// If target element is not sufficiently visible and PossibleElementPath is available, try fallback
			if ((!isVisible || visibilityRatio < 0.3) && stepData?.PossibleElementPath) {
				console.log("🔄 Target element not sufficiently visible, trying PossibleElementPath fallback:", stepData.PossibleElementPath);

				try {
					const possibleElement = getElementByXPath(stepData.PossibleElementPath, "");
					if (possibleElement) {
						console.log("✅ Found PossibleElementPath element, scrolling to it:", possibleElement);

					// Recursively call scrollToTargetElement for the possible element
					// but without stepData to avoid infinite recursion
					await scrollToTargetElement(possibleElement, placement);

					// Update the tooltip to point to the PossibleElementPath element
					console.log("🎯 Updating tooltip target to PossibleElementPath element");
					const elementRect = possibleElement.getBoundingClientRect();
					const xOffset = parseFloat(stepData?.positionXAxisOffset || "0");
					const yOffset = parseFloat(stepData?.positionYAxisOffset || "0");

					setTargetElement(possibleElement);
					setIsElementVisible(true);
					setTooltipPosition({
						top: elementRect.top + window.scrollY + yOffset,
						left: elementRect.left + window.scrollX + xOffset,
					});

					// Update placement if needed
					if (stepData?.autoposition) {
						const bestPlacement = calculateBestPosition(possibleElement);
						setTooltipPlacement(bestPlacement);
					}

						console.log("✅ PossibleElementPath fallback completed - tooltip now pointing to possible element");
					} else {
						console.log("❌ PossibleElementPath element not found:", stepData.PossibleElementPath);
					}
				} catch (error) {
					console.log("❌ Error during PossibleElementPath fallback:", error);
				}
			}
		});

		return scrollOperationQueue.current;
	}, [setTargetElement, setIsElementVisible, setTooltipPosition, setTooltipPlacement, calculateBestPosition]);

	const processContentWithHyperlinks = (content: string) => {
		// Return empty string for null, undefined, or empty content
		if (!content || content.trim() === '') return '';

		// Check if content has any meaningful text after stripping HTML tags
		const tempDiv = document.createElement('div');
		tempDiv.innerHTML = content;
		const textContent = tempDiv.textContent || tempDiv.innerText;
		if (!textContent || textContent.trim() === '') return '';

		// Clean up empty HTML elements
		let cleanedContent = content;

		// Remove empty paragraph tags
		cleanedContent = cleanedContent.replace(/<p>\s*(&nbsp;)*\s*<\/p>/gi, '');

		// Remove empty div tags
		cleanedContent = cleanedContent.replace(/<div>\s*(&nbsp;)*\s*<\/div>/gi, '');

		// Remove empty span tags
		cleanedContent = cleanedContent.replace(/<span>\s*(&nbsp;)*\s*<\/span>/gi, '');

		// Remove <br> tags at the beginning or end
		cleanedContent = cleanedContent.replace(/^(\s*<br\s*\/?>\s*)+|(\s*<br\s*\/?>\s*)+$/gi, '');

		// Remove consecutive <br> tags
		cleanedContent = cleanedContent.replace(/(<br\s*\/?>\s*){2,}/gi, '<br>');

		// If after cleaning there's no content left, return empty string
		if (cleanedContent.trim() === '') return '';

		// Regular expression to find hyperlinks in the content
		// This matches both href links and plain URLs
		const linkRegex = /<a\s+(?:[^>]*?\s+)?href=["']([^"']*)["'][^>]*>(.*?)<\/a>|(https?:\/\/[^\s<>"]+)/g;

		// Replace hyperlinks with modified versions that open in new tab
		cleanedContent = cleanedContent.replace(linkRegex, (match, href, linkText, plainUrl) => {
			if (plainUrl) {
				// Handle plain URLs (not wrapped in <a> tags)
				return `<a href="${plainUrl}" target="_blank" rel="noopener noreferrer">${plainUrl}</a>`;
			} else if (href) {
				// Handle existing <a> tags - replace with target="_blank"
				return `<a href="${href}" target="_blank" rel="noopener noreferrer">${linkText}</a>`;
			}
			return match; // Return unchanged if not matched
		});

		// If the content is just a single paragraph, extract its content
		// This helps remove unnecessary <p> tags when there's only text
		if (hasOnlyTextContent) {
			const singleParagraphMatch = cleanedContent.match(/^\s*<p>(.*?)<\/p>\s*$/);
			if (singleParagraphMatch) {
				cleanedContent = singleParagraphMatch[1];
			}
		}

		return cleanedContent;
	};

	const isElementInViewport = (element: HTMLElement) => {
		const rect = element.getBoundingClientRect();
		const viewportHeight = window.innerHeight || document.documentElement.clientHeight;
		const viewportWidth = window.innerWidth || document.documentElement.clientWidth;

		// More lenient check - element just needs to be partially visible
		const isPartiallyVisible = rect.bottom > 0 &&
			rect.top < viewportHeight &&
			rect.right > 0 &&
			rect.left < viewportWidth;

		// Check if at least 30% of the element is visible (more lenient than before)
		const visibleHeight = Math.min(rect.bottom, viewportHeight) - Math.max(rect.top, 0);
		const visibleWidth = Math.min(rect.right, viewportWidth) - Math.max(rect.left, 0);
		const elementArea = rect.width * rect.height;
		const visibleArea = Math.max(0, visibleHeight) * Math.max(0, visibleWidth);
		const visibilityRatio = elementArea > 0 ? visibleArea / elementArea : 0;

		// Consider element visible if it's partially visible and at least 30% is shown
		return isPartiallyVisible && visibilityRatio >= 0.3;
	};

	const validateElementPosition = (element: HTMLElement) => {
		const rect = element.getBoundingClientRect();
		return (
			rect.width > 0 &&
			rect.height > 0 &&
			rect.top >= 0 &&
			rect.left >= 0 &&
			!Number.isNaN(rect.top) &&
			!Number.isNaN(rect.left)
		);
	};
	const getElementByXPath = (xpath: string, PossibleElementPath: string): HTMLElement | null => {
		// Check if xpath is empty or undefined
		if (!xpath || xpath.trim() === '') {
			return null;
		}

		const query = `${xpath}[not(ancestor::div[@id='quickAdopt_banner']) and not(@id='quickAdopt_banner')]`;
		try {
			const result = document.evaluate(query, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
			const node = result.singleNodeValue;

			if (node instanceof HTMLElement) {
				return node;
			} else if (node?.parentElement) {
				return node.parentElement;
			} else if (node === null && PossibleElementPath && PossibleElementPath.trim() !== '') {
				// Only evaluate PossibleElementPath if it's not empty
				try {
					const result = document.evaluate(PossibleElementPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
					const node = result.singleNodeValue;
					if (node instanceof HTMLElement) {
						return node;
					}
				} catch (error) {
					//console.error("Error evaluating XPath:", error);
				}
				return null;
			} else {
				return null;
			}
		} catch (error) {
			//console.error("Error evaluating primary XPath:", error);
			return null;
		}
	};

	// Extract the current step number from the currentStep
	// If it's a string like "step X", extract X. If it's a number, use it directly. Otherwise, default to 0
	const currentStepNumber = typeof currentStep === 'string' && currentStep.includes('step ')
		? parseInt(currentStep.replace('step ', ''), 10)
		: typeof currentStep === 'number'
			? currentStep
			: 0;

	// Calculate progress color based on current step index instead of currentStepNumber for consistency
	const currentStepProgressColor = data?.GuideStep?.[currentStepIndex]?.Modal?.ProgressColor;
	const progressColor = currentStepProgressColor === "var(--primarycolor)" ? primaryColor : currentStepProgressColor;

	// Use currentStepIndex for progress calculation to ensure consistency with navigation
	const stepIndexForProgress = data?.GuideType === "Tour" ? currentStep : currentStepIndex;
	const progress = ((stepIndexForProgress + 1) / totalSteps) * 100;
	const interactWithPage = tooltipConfig?.InteractWithPage;
	useEffect(() => {
		if (currentStepData && currentStepIndex >= 0 && interactWithPage === false) {
			document.body.style.pointerEvents = data?.GuideType === "Tour" ? "auto" : "none";
		} else {
			document.body.style.pointerEvents = "auto";
		}
		return () => {
			document.body.style.pointerEvents = "auto";
		};
	}, [currentStepData, currentStepIndex, interactWithPage, data?.GuideType]);

	// Enhanced updateTargetAndPosition with better timing and debouncing
	const updateTargetAndPosition = useCallback(() => {
		// Additional check: ensure currentStepData matches currentStepIndex to prevent stale data usage
		const expectedStepData = steps[latestStepIndexRef.current];
		if (currentStepData !== expectedStepData) {
			console.log("Skipping update - step data mismatch detected, waiting for sync");
			return;
		}

		// Check if currentStepData exists and has valid xpath
		if (!currentStepData || !currentStepData.xpath) {
			setTargetElement(null);
			setIsElementVisible(false);
			return;
		}

		// Use requestAnimationFrame to ensure DOM is ready
		requestAnimationFrame(() => {
			const element = getElementByXPath(
				currentStepData.xpath || "",
				currentStepData.PossibleElementPath || ""
			);

			if (!element) {
				setTargetElement(null);
				setIsElementVisible(false);
				return;
			}

			const isValid = validateElementPosition(element);
			const isVisible = isElementInViewport(element);

			if (!isValid || !isVisible) {
				setTargetElement(null);
				setIsElementVisible(false);
				return;
			}

			const rect = element.getBoundingClientRect();
			const xOffset = parseFloat(currentStepData?.positionXAxisOffset || "0");
			const yOffset = parseFloat(currentStepData?.positionYAxisOffset || "0");

			setTargetElement(element);
			setIsElementVisible(true);

			// Calculate placement
			let newPlacement: "top" | "left" | "right" | "bottom"
			if (currentStepData?.autoposition) {
				newPlacement = calculateBestPosition(element)
			} else {
				const validPlacements = ["top", "left", "right", "bottom"] as const
				const placement = currentStepData?.canvas?.Position || "bottom"
				newPlacement = validPlacements.includes(placement as any)
					? (placement as "top" | "left" | "right" | "bottom")
					: "bottom"
			}


			if (prevPlacementRef.current !== newPlacement) {
				prevPlacementRef.current = newPlacement
				setTooltipPlacement(newPlacement)
			}


			const newPosition = {
				top: Math.round(rect.top + window.scrollY + yOffset),
				left: Math.round(rect.left + window.scrollX + xOffset),
			}


			const positionChanged =
				Math.abs(prevPositionRef.current.top - newPosition.top) > 1 ||
				Math.abs(prevPositionRef.current.left - newPosition.left) > 1

			if (positionChanged) {
				prevPositionRef.current = newPosition
				setTooltipPosition(newPosition)
			}


			// Auto-scroll to ensure tooltip visibility when initially positioning
			// Only scroll if element is not fully visible or tooltip might be cut off
			const isElementFullyVisible = isElementInViewport(element);
			if (!isElementFullyVisible) {
				let currentPlacement: "top" | "left" | "right" | "bottom" = "bottom";
				if (currentStepData?.autoposition) {
					currentPlacement = calculateBestPosition(element);
				} else {
					const validPlacements = ["top", "left", "right", "bottom"] as const;
					const placement = currentStepData?.canvas?.Position || "bottom";
					currentPlacement = validPlacements.includes(placement as any)
						? (placement as "top" | "left" | "right" | "bottom")
						: "bottom";
				}

				// Use improved polling with better timing
				pollForElement(
					currentStepData.xpath || "",
					currentStepData.PossibleElementPath || "",
					(foundElement) => {
						console.log("Element found successfully for auto-scroll:", foundElement);
						scrollToTargetElement(foundElement, currentPlacement, currentStepData);
					},
					30, // maxAttempts - increased for better reliability
					16, // initialIntervalMs - start with one frame
					"Current step element", // logPrefix
					() => {
						// Callback for when element is not found
						console.log("Element not found - unable to scroll to target");
					}
				);
			}
		});
	}, [currentStepData, currentStepIndex, steps, calculateBestPosition, setTargetElement, setIsElementVisible, setTooltipPlacement, setTooltipPosition, pollForElement, scrollToTargetElement]);

	// Enhanced auto-scroll navigation system for tooltip next functionality
	const handleNext = useCallback(async () => {
		if (data?.GuideType !== "Tour") {
			// Calculate the next index first
			const nextIndex = currentStepIndex + 1;

			if (nextIndex < steps.length) {
				// Get the next step data for scrolling
				const nextStepData = steps[nextIndex];

				console.log("🔍 Starting navigation to next step:", {
					currentIndex: currentStepIndex,
					nextIndex: nextIndex,
					xpath: nextStepData?.xpath,
					stepTitle: nextStepData?.stepTitle || `Step ${nextIndex + 1}`
				});

				// Enhanced element discovery and auto-scroll execution BEFORE updating step index
				if (nextStepData?.xpath) {
					console.log("🔍 Starting enhanced auto-scroll for next element:", {
						xpath: nextStepData.xpath,
						stepIndex: nextIndex,
						stepTitle: nextStepData.stepTitle || `Step ${nextIndex + 1}`
					});

					// Create a promise to handle the polling and scrolling
					const scrollPromise = new Promise<void>((resolve) => {
						// Use the enhanced polling mechanism with exponential backoff
						pollForElement(
							nextStepData.xpath || "",
							nextStepData.PossibleElementPath || "",
							async (foundElement) => {
								console.log("✅ Next element discovered successfully:", {
									element: foundElement,
									tagName: foundElement.tagName,
									className: foundElement.className,
									id: foundElement.id
								});

								try {
									// Calculate optimal tooltip placement based on viewport space
									let nextPlacement: "top" | "left" | "right" | "bottom" = "bottom";
									if (nextStepData?.autoposition) {
										nextPlacement = calculateBestPosition(foundElement);
										console.log("📍 Auto-calculated optimal placement:", nextPlacement);
									} else {
										const validPlacements = ["top", "left", "right", "bottom"] as const;
										const placement = nextStepData?.canvas?.Position || "bottom";
										nextPlacement = validPlacements.includes(placement as any)
											? (placement as "top" | "left" | "right" | "bottom")
											: "bottom";
										console.log("📍 Using configured placement:", nextPlacement);
									}

									// Execute auto-scroll with proper timing synchronization
									console.log("🎯 Executing auto-scroll to target element...");
									await scrollToTargetElement(foundElement, nextPlacement, nextStepData);

									console.log("✅ Auto-scroll completed successfully for next step");

									// Verify element is now visible in viewport (more lenient check)
									const finalRect = foundElement.getBoundingClientRect();
									const viewportHeight = window.innerHeight;
									const viewportWidth = window.innerWidth;

									// Check if element is at least partially visible
									const isPartiallyVisible = finalRect.bottom > 0 &&
										finalRect.top < viewportHeight &&
										finalRect.right > 0 &&
										finalRect.left < viewportWidth;

									// Calculate visibility ratio
									const visibleHeight = Math.min(finalRect.bottom, viewportHeight) - Math.max(finalRect.top, 0);
									const visibleWidth = Math.min(finalRect.right, viewportWidth) - Math.max(finalRect.left, 0);
									const elementArea = finalRect.width * finalRect.height;
									const visibleArea = Math.max(0, visibleHeight) * Math.max(0, visibleWidth);
									const visibilityRatio = elementArea > 0 ? visibleArea / elementArea : 0;

									if (isPartiallyVisible && visibilityRatio >= 0.3) {
										console.log("✅ Target element is now sufficiently visible in viewport", {
											visibilityRatio: Math.round(visibilityRatio * 100) + '%'
										});
									} else if (isPartiallyVisible) {
										console.log("⚠️ Target element is partially visible but may be cut off", {
											visibilityRatio: Math.round(visibilityRatio * 100) + '%'
										});
									} else {
										console.warn("⚠️ Target element may not be visible after scroll");
									}

									// Resolve the promise to indicate scrolling is complete
									resolve();

								} catch (error) {
									console.error("❌ Error during auto-scroll execution:", error);
									// Fallback: try simple scrollIntoView
									try {
										foundElement.scrollIntoView({
											behavior: 'smooth',
											block: 'center',
											inline: 'nearest'
										});
										console.log("✅ Fallback scroll completed");
										// Wait for fallback scroll to complete
										setTimeout(() => resolve(), 500);
									} catch (fallbackError) {
										console.error("❌ Fallback scroll also failed:", fallbackError);
										// Even if scrolling fails, continue with step transition
										resolve();
									}
								}
							},
							30, // maxAttempts - increased for better reliability
							16, // initialIntervalMs - start with one frame (16ms)
							"Next step element", // logPrefix
							() => {
								// Enhanced error handling when element is not found
								console.error("❌ Next element not found after polling attempts:", {
									xpath: nextStepData.xpath,
									possiblePath: nextStepData.PossibleElementPath,
									stepIndex: nextIndex,
									maxAttempts: 30
								});

								// Fallback behavior: continue to next step without scrolling
								console.log("🔄 Continuing to next step without auto-scroll");
								resolve(); // Resolve even if element not found
							}
						);
					});

					try {
						// Wait for scrolling to complete before updating step index
						await scrollPromise;
						console.log("✅ Scroll operation completed, updating step index");
					} catch (error) {
						console.error("❌ Scroll promise failed:", error);
						// Continue with step transition even if scrolling fails
					}
				} else {
					console.log("ℹ️ No xpath provided for next step, skipping auto-scroll");
				}

				// Update the step index AFTER scrolling is complete
				console.log("🔄 Updating step index from", currentStepIndex, "to", nextIndex);
				setCurrentStepIndex(nextIndex);

				// Store current step in session for multi-page tooltips
				const nextStepTitle = steps[nextIndex]?.stepTitle || `Step ${nextIndex + 1}`;
				sessionStorage.setItem('activeTooltipSession', data?.GuideId || 'active');
				sessionStorage.setItem('currentTooltipStep', nextStepTitle);
				console.log('💾 Stored tooltip session state:', {
					step: nextStepTitle,
					guideId: data?.GuideId
				});

				// Small delay to allow DOM to update after step change
				await new Promise(resolve => setTimeout(resolve, 50));

			} else {
				console.log("🏁 Reached end of tooltip steps, closing");
				onClose(); // Close tooltip if no more steps
				return;
			}
		}

		// Update the currentStep string to reflect the new step number
		setCurrentStep(currentStep + 1);
		if (currentStep === data?.GuideStep?.length - 1) {
			console.log("🏁 Completed all tooltip steps");
			onClose();
		}
	}, [
		data?.GuideType,
		data?.GuideStep?.length,
		data?.GuideId,
		currentStepIndex,
		steps,
		setCurrentStepIndex,
		pollForElement,
		calculateBestPosition,
		scrollToTargetElement,
		onClose,
		setCurrentStep,
		currentStep
	]);

	// Helper function to handle next step navigation for both Tours and non-Tours
	const handleNextStep = useCallback(async () => {
		if (data?.GuideType === "Tour") {
			// For Tours, directly increment the step like in TourUserView.handleContinue
			if (currentStep < (data?.GuideStep?.length - 1)) {
				setCurrentStep(currentStep + 1);
			} else {
				// If it's the last step, close the tour
				onClose();
			}
		} else {
			// For non-Tours, use the existing handleNext logic
			await handleNext();
		}
	}, [data?.GuideType, data?.GuideStep?.length, currentStep, setCurrentStep, onClose, handleNext]);

	const handlePrevious = () => {
		//console.log("HandlePrevious: Current step index:", currentStepIndex);
		//console.log("HandlePrevious: Current scroll position:", window.scrollY);

		// Calculate the previous index first
		const newPrevIndex = Math.max(currentStepIndex - 1, 0);
		//console.log("HandlePrevious: New previous index:", newPrevIndex);

		// Only proceed if we're actually moving to a different step
		if (newPrevIndex !== currentStepIndex) {
			// Get the previous step data for scrolling
			const prevStepData = steps[newPrevIndex];
			//console.log("HandlePrevious: Previous step data:", prevStepData);

			// Update the step index
			setCurrentStepIndex(newPrevIndex);

			// ALWAYS scroll to top of page when going to previous step
			//console.log("HandlePrevious: Attempting to scroll to top of page");
			//console.log("HandlePrevious: Document height:", document.documentElement.scrollHeight);
			//console.log("HandlePrevious: Current window scroll:", { x: window.scrollX, y: window.scrollY });

			// Enhanced scroll to top with multiple fallback methods
			let topScrollSuccess = false;

			// Method 1: Try multiple scrollIntoView approaches for page top
			const topScrollMethods = [
				() => document.documentElement.scrollIntoView({ behavior: 'smooth', block: 'start' }),
				() => document.body.scrollIntoView({ behavior: 'smooth', block: 'start' }),
				() => document.documentElement.scrollIntoView({ block: 'start' }),
				() => document.body.scrollIntoView({ block: 'start' }),
				() => document.documentElement.scrollIntoView(),
				() => document.body.scrollIntoView()
			];

			for (const method of topScrollMethods) {
				try {
					method();
					topScrollSuccess = true;
					console.log("HandlePrevious: Page top scroll succeeded with scrollIntoView");
					break;
				} catch (error) {
					console.log("HandlePrevious: scrollIntoView method failed:", error);
				}
			}

			// Method 2: Try universalScrollTo for window
			if (!topScrollSuccess) {
				try {
					topScrollSuccess = universalScrollTo(window, {
						top: 0,
						left: 0,
						behavior: 'smooth'
					});
					console.log("HandlePrevious: Universal scroll to top success:", topScrollSuccess);
				} catch (error) {
					console.log("HandlePrevious: Universal scroll failed:", error);
				}
			}

			// Also try immediate scroll as backup
			setTimeout(() => {
				if (window.scrollY > 50) {
					//console.log("HandlePrevious: Smooth scroll didn't work, forcing immediate scroll");
					document.documentElement.scrollTop = 0;
					document.body.scrollTop = 0;
					// Use direct assignment without scrollTo
					window.scroll(0, 0);
				}
				//console.log("HandlePrevious: After scroll attempt, position:", window.scrollY);
			}, 100);

			// Handle scrolling to the previous element after page scroll
			if (prevStepData?.xpath) {
				//console.log("HandlePrevious: Found xpath for previous step:", prevStepData.xpath);

				// Use setTimeout to allow page scroll to complete, then scroll to element
				setTimeout(() => {
					//console.log("HandlePrevious: Final scroll position before element search:", window.scrollY);
					// Use latest step index to prevent stale data
					const latestIndex = latestStepIndexRef.current;
					const latestStepData = steps[latestIndex];
					const prevElement = getElementByXPath(
						latestStepData.xpath || "",
						latestStepData.PossibleElementPath || ""
					);
					//console.log("HandlePrevious: Found previous element:", prevElement);

					if (prevElement) {
						// Check if element is in a scrollable container
						const scrollableContainer = findScrollableContainer(prevElement);
						//console.log("HandlePrevious: Found scrollable container for previous element:", scrollableContainer);

						if (scrollableContainer) {
							// If element is in a scrollable container, scroll container to top first
							//console.log("HandlePrevious: Scrolling container to top to show tooltip1");
							//console.log("HandlePrevious: Container current scroll:", scrollableContainer.scrollTop);

							// Enhanced container scrolling with multiple methods
							let containerScrollSuccess = false;

							// Method 1: Try scrollIntoView on first child
							try {
								const firstChild = scrollableContainer.firstElementChild;
								if (firstChild) {
									const childScrollMethods = [
										() => firstChild.scrollIntoView({ behavior: 'smooth', block: 'start' }),
										() => firstChild.scrollIntoView({ block: 'start' }),
										() => firstChild.scrollIntoView()
									];

									for (const method of childScrollMethods) {
										try {
											method();
											containerScrollSuccess = true;
											console.log("HandlePrevious: Container scroll via firstChild succeeded");
											break;
										} catch (e) {
											console.log("HandlePrevious: firstChild scroll method failed:", e);
										}
									}
								}
							} catch (error) {
								console.log("HandlePrevious: firstChild scrolling failed:", error);
							}

							// Method 2: Try universalScrollTo on container
							if (!containerScrollSuccess) {
								try {
									containerScrollSuccess = universalScrollTo(scrollableContainer, {
										top: 0,
										behavior: 'smooth'
									});
									console.log("HandlePrevious: Universal container scroll success:", containerScrollSuccess);
								} catch (error) {
									console.log("HandlePrevious: Universal container scroll failed:", error);
								}
							}

							// Method 3: Final fallback to custom smooth scroll
							if (!containerScrollSuccess) {
								try {
									smoothScrollTo(scrollableContainer, 0);
									containerScrollSuccess = true;
									console.log("HandlePrevious: Custom smooth scroll used as final fallback");
								} catch (error) {
									console.log("HandlePrevious: All container scrolling methods failed:", error);
								}
							}

							// Wait for container scroll to complete, then position tooltip
							setTimeout(() => {
								//console.log("HandlePrevious: Container scroll completed, positioning tooltip");
								//console.log("HandlePrevious: Container final scroll:", scrollableContainer.scrollTop);

								// Force tooltip update by triggering position recalculation
								setTimeout(() => {
									//console.log("HandlePrevious: Triggering tooltip position update");
									// Force update the target and position to refresh tooltip
									updateTargetAndPosition();
									//console.log("HandlePrevious: Tooltip position update completed");
								}, 100);
							}, 300); // Wait for container scroll animation
						} else {
							// Element not in container, use normal scrolling
							let prevPlacement: "top" | "left" | "right" | "bottom" = "bottom";
							if (latestStepData?.autoposition) {
								prevPlacement = calculateBestPosition(prevElement);
							} else {
								const validPlacements = ["top", "left", "right", "bottom"] as const;
								const placement = latestStepData?.canvas?.Position || "bottom";
								prevPlacement = validPlacements.includes(placement as any)
									? (placement as "top" | "left" | "right" | "bottom")
									: "bottom";
							}
							//console.log("HandlePrevious: Calculated placement:", prevPlacement);

							// Scroll to the previous element (page-level scrolling)
							scrollToTargetElement(prevElement, prevPlacement, latestStepData);
						}
					} else {
						//console.log("HandlePrevious: No element found for xpath:", prevStepData.xpath);
					}
				}, 500); // Increased delay to ensure page scroll completes
			} else {
				console.log("HandlePrevious: No xpath found for previous step");
			}
		} else {
			console.log("HandlePrevious: Already at first step, not moving");
		}

		// Update the currentStep string to reflect the new step number
		setCurrentStep(currentStep - 1);
	};
	useEffect(() => {
		// Debug logging for AI tooltip button click functionality
		console.log("🔍 Tooltip useEffect - Element click setup:", {
			currentStepIndex,
			elementclick: currentStepData?.elementclick,
			NextStep: currentStepData?.elementclick?.NextStep,
			Id: currentStepData?.elementclick?.Id,
			xpath: currentStepData?.xpath,
			hasButtons: currentStepData?.buttonData?.length > 0
		});

		// Determine if element click should be enabled
		const shouldEnableElementClick =
			// Explicit element click configuration
			currentStepData?.elementclick?.NextStep === "element" ||
			// Explicit button click configuration
			currentStepData?.elementclick?.NextStep === "button" ||
			// Default behavior: if no buttons are configured and no explicit NextStep is set,
			// enable element click navigation (this handles the case described in the issue)
			(!currentStepData?.buttonData?.length &&
			 (!currentStepData?.elementclick?.NextStep ||
			  currentStepData?.elementclick?.NextStep === "" ||
			  currentStepData?.elementclick?.NextStep === "element"));

		if (shouldEnableElementClick) {
			const element = getElementByXPath(currentStepData?.xpath, currentStepData?.PossibleElementPath);
			if (element) {
				console.log("✅ Element found for click handler:", element, {
					reason: currentStepData?.elementclick?.NextStep === "element" ? "explicit element" :
							currentStepData?.elementclick?.NextStep === "button" ? "explicit button" :
							"default behavior (no buttons configured)"
				});

				const handleClick = () => {
					console.log("🖱️ Element clicked - advancing to next step");
					handleNextStep().catch(error => {
						console.error("Error in handleNextStep:", error);
					});
				};

				// Add a small delay to ensure DOM is ready
				const timeoutId = setTimeout(() => {
					element.addEventListener("click", handleClick);
				}, 100);

				return () => {
					clearTimeout(timeoutId);
					element.removeEventListener("click", handleClick);
				};
			} else {
				console.log("❌ Element not found for xpath:", currentStepData?.xpath);
			}
		} else {
			console.log("ℹ️ No element click setup - NextStep:", currentStepData?.elementclick?.NextStep, "hasButtons:", currentStepData?.buttonData?.length > 0);
		}
	}, [currentStepData, handleNextStep, currentStepIndex]);

	useEffect(() => {
		const handleDOMChanges = () => {
			requestAnimationFrame(updateTargetAndPosition);
		};
		observerRef.current = new MutationObserver(handleDOMChanges);
		const targetNode = document.body;
		observerRef.current.observe(targetNode, {
			childList: true,
			subtree: true,
			attributes: true,
			characterData: true,
		});
		updateTargetAndPosition();
		return () => {
			observerRef.current?.disconnect();
		};
	}, [currentStepData, updateTargetAndPosition]);

	useEffect(() => {
		const handleViewportChanges = () => {
			requestAnimationFrame(updateTargetAndPosition);
		};

		window.addEventListener("scroll", handleViewportChanges);
		window.addEventListener("resize", handleViewportChanges);

		return () => {
			window.removeEventListener("scroll", handleViewportChanges);
			window.removeEventListener("resize", handleViewportChanges);
		};
	}, [updateTargetAndPosition]);

	useEffect(() => {
		updateTargetAndPosition();
	}, [currentStepData, updateTargetAndPosition]); // Ensure all dependencies are included

	// Check if content needs scrolling with improved detection
	useEffect(() => {
		const checkScrollNeeded = () => {
			if (contentRef.current) {
				// Force a reflow to get accurate measurements
				contentRef.current.style.height = 'auto';
				const contentHeight = contentRef.current.scrollHeight;
				const containerHeight = 320; // max-height value
				const shouldScroll = contentHeight > containerHeight;


				setNeedsScrolling(shouldScroll);

				// Force update scrollbar
				if (scrollbarRef.current) {
					// Try multiple methods to update the scrollbar
					if (scrollbarRef.current.updateScroll) {
						scrollbarRef.current.updateScroll();
					}
					// Force re-initialization if needed
					setTimeout(() => {
						if (scrollbarRef.current && scrollbarRef.current.updateScroll) {
							scrollbarRef.current.updateScroll();
						}
					}, 10);
				}
			}
		};


		checkScrollNeeded();


		const timeouts = [
			setTimeout(checkScrollNeeded, 50),
			setTimeout(checkScrollNeeded, 100),
			setTimeout(checkScrollNeeded, 200),
			setTimeout(checkScrollNeeded, 500)
		];


		let resizeObserver: ResizeObserver | null = null;
		let mutationObserver: MutationObserver | null = null;

		if (contentRef.current && window.ResizeObserver) {
			resizeObserver = new ResizeObserver(() => {
				setTimeout(checkScrollNeeded, 10);
			});
			resizeObserver.observe(contentRef.current);
		}


		if (contentRef.current && window.MutationObserver) {
			mutationObserver = new MutationObserver(() => {
				setTimeout(checkScrollNeeded, 10);
			});
			mutationObserver.observe(contentRef.current, {
				childList: true,
				subtree: true,
				attributes: true,
				attributeFilter: ['style', 'class']
			});
		}

		return () => {
			timeouts.forEach(clearTimeout);
			if (resizeObserver) {
				resizeObserver.disconnect();
			}
			if (mutationObserver) {
				mutationObserver.disconnect();
			}
		};
	}, [currentStepData, currentStepIndex, steps]);
	useEffect(() => {
		const rails = document.querySelectorAll('.ps__rail-x');
		rails.forEach(el => {
			if (el instanceof HTMLElement) {
				el.style.display = 'none';
			}
		});
	}, [needsScrolling, currentStepData]);
	const canvasStyle = currentStepData?.canvas || {}; // Assuming canvas is an array, take the first item
	const enableProgress = tooltipConfig.EnableProgress || false;
	function getProgressTemplate(tooltipConfig: any) {
		if (tooltipConfig?.ProgressTemplate === "1") {
			return "dots";
		} else if (tooltipConfig?.ProgressTemplate === "2") {
			return "linear";
		}
		else if (tooltipConfig?.ProgressTemplate === "3") {
			return "BreadCrumbs";
		} else if (tooltipConfig?.ProgressTemplate === "4") {
			return "breadcrumbs";
		}

		return tooltipConfig.ProgressTemplate || "dots";
	}
	const progressTemplate = getProgressTemplate(tooltipConfig);

	const enabelCross = currentStepData?.modal?.DismissOption;
	// Utility function to check if HTML content contains meaningful text
	const hasHtmlMeaningfulContent = (htmlContent: string): boolean => {
		if (!htmlContent || htmlContent.trim() === '') {
			return false;
		}

		// Clean up common empty HTML patterns before checking
		let cleanedContent = htmlContent;

		// Remove empty paragraph tags
		cleanedContent = cleanedContent.replace(/<p>\s*(&nbsp;)*\s*<\/p>/gi, '');

		// Remove empty div tags
		cleanedContent = cleanedContent.replace(/<div>\s*(&nbsp;)*\s*<\/div>/gi, '');

		// Remove empty span tags
		cleanedContent = cleanedContent.replace(/<span>\s*(&nbsp;)*\s*<\/span>/gi, '');

		// Remove <br> tags
		cleanedContent = cleanedContent.replace(/<br\s*\/?>/gi, '');

		// Remove &nbsp; entities
		cleanedContent = cleanedContent.replace(/&nbsp;/gi, ' ');

		// If after cleaning there's no content left, return false
		if (cleanedContent.trim() === '') {
			return false;
		}

		// Create a temporary div to parse the cleaned HTML content
		const tempDiv = document.createElement('div');
		tempDiv.innerHTML = cleanedContent;

		// Get the text content (strips all HTML tags)
		const textContent = tempDiv.textContent || tempDiv.innerText;

		// Check if there's any non-whitespace text content
		if (textContent === null || textContent.trim() === '') {
			return false;
		}

		// Additional check for common empty HTML patterns
		// This handles cases like "<div><br></div>" or "<p>&nbsp;</p>" that might appear non-empty
		const lowerContent = cleanedContent.toLowerCase();
		const emptyPatterns = [
			'<div><br></div>',
			'<p><br></p>',
			'<div></div>',
			'<p></p>',
			'<span></span>',
			'<p>&nbsp;</p>',
			'<div>&nbsp;</div>',
			'<p> </p>',
			'<div> </div>'
		];

		if (emptyPatterns.some(pattern => lowerContent.includes(pattern)) && textContent.trim().length <= 1) {
			return false;
		}

		return true;
	};

	const renderContent = () => {
		const hasImage =
			currentStepData?.imageUrl?.startsWith("data:image/") || currentStepData?.imageUrl?.startsWith("http");

		// Enhanced text validation
		const hasText = Array.isArray(currentStepData?.content)
			? currentStepData.content.some(
				(item: any) => item?.Text && typeof item.Text === "string" && item.Text.trim() !== ""
			)
			: (typeof currentStepData?.content === "string" && hasHtmlMeaningfulContent(currentStepData.content)) ||
			React.isValidElement(currentStepData?.content);

		// Base text style
		const textStyle = {
			fontSize: "14px",
			lineHeight: "1.5",
			wordBreak: "break-word",
			whiteSpace: "pre-wrap",
			color: "black",
			fontWeight: "normal",
		};
		return (
			<Box>
				{hasImage && (
					<Box
						component="img"
						src={currentStepData?.imageproperties?.Url}
						alt={currentStepData?.imageproperties?.AltText || "Step Image"}
						sx={{
							backgroundColor: currentStepData?.imageproperties?.BackgroundColor || "transparent",
							objectFit: currentStepData?.imageproperties?.Fit || "cover",
							maxHeight: currentStepData?.imageproperties?.SectionHeight || "auto",
							width: "100%",
						}}
					/>
				)}
				{hasText && (
					<Box
						sx={{
							//padding: hasOnlyTextContent ? "0" : "0px 5px",
							margin: "0 !important",
							...textStyle,
							"& p": {
								margin: "0",
							},
							"& p:empty": {
								display: "none",
							},
							"& div:empty": {
								display: "none",
							},
							"& span:empty": {
								display: "none",
							},
							...(hasOnlyTextContent && {
								width: "auto !important",
								maxWidth: "none !important"
							})
						}}
						dangerouslySetInnerHTML={{
							__html: typeof currentStepData?.content === "string" ? processContentWithHyperlinks(currentStepData.content) : ""
						}}
						onClick={(e) => {
							// Handle link clicks within the content
							const target = e.target as HTMLElement;
							if (target.tagName === 'A') {
								e.preventDefault();
								const href = target.getAttribute('href');
								if (href) {
									trackUserEngagement("hyperLink-click", userData, data, browser, version,
										target.innerText, steps[currentStepIndex]?.stepTitle, 0, 0);
									window.open(href, "_blank", "noopener,noreferrer");
								}
							}
						}}
					/>
				)}
			</Box>
		);
	};
	const renderProgress = () => {
		if (!enableProgress) return null;

		const activeStepValue = data?.GuideType === "Tour" ? currentStep : currentStepIndex;

		if (progressTemplate === "dots") {
			return (
				<MobileStepper
					variant="dots"
					steps={totalSteps}
					position="static"
					activeStep={activeStepValue}
					sx={{
						backgroundColor: "transparent", "& .MuiMobileStepper-dotActive": {
							backgroundColor: progressColor, // Active dot
						},
						placeContent: "center",
						padding: "2px  !important",
						"& .MuiMobileStepper-dot": {
							width: "6px !important",
							height: "6px !important",
						}
					}}
					backButton={<Button style={{ display: "none" }} />}
					nextButton={<Button style={{ display: "none" }} />}
				/>
			);
		}


		if (progressTemplate === "BreadCrumbs") {
			return (
				<Box sx={{
					display: "flex",
					alignItems: "center",
					placeContent: "center",
					gap: "5px", padding: "2px"
				}}>
					{/* Custom Step Indicators */}

					{Array.from({ length: totalSteps }).map((_, index) => (
						<div
							key={index}
							style={{
								width: '14px',
								height: '4px',
								backgroundColor: index === activeStepValue ? progressColor : '#e0e0e0', // Active color and inactive color
								borderRadius: '100px',
							}}
						/>
					))}

				</Box>
			);
		}

		if (progressTemplate === "breadcrumbs") {
			return (
				<Box sx={{ paddingTop: "2px" }}>
					<Typography
						variant="body2"
						sx={{ padding: "2px", color: progressColor, fontSize: "12px" }}
					>
						Step {activeStepValue + 1} of {totalSteps}
					</Typography>
				</Box>
			);
		}

		if (progressTemplate === "linear") {
			return (
				<Box sx={{ padding: hasOnlyButton ? "8px" : "0", }}>
					<Typography variant="body2">
						<LinearProgress
							variant="determinate"
							value={progress}
							sx={{
								height: "6px",
								borderRadius: "20px",
								margin: hasOnlyButton ? "0" : "6px 10px",
								'& .MuiLinearProgress-bar': {
									backgroundColor: progressColor, // progress bar color
								},
							}}
						/>
					</Typography>
				</Box>
			);
		}

		return null;
	};

	const renderButtons = () => {
		return currentStepData?.buttonData?.length > 0
			? currentStepData?.buttonData.map((button, index) => {
				const buttonStyle = {
					backgroundColor: button.ButtonProperties.ButtonBackgroundColor,
					border: button.ButtonProperties.ButtonBorderColor,
					color: button.ButtonProperties.ButtonTextColor,
					padding: "4px 8px !important",
					lineHeight: "normal",
					width: "auto",
					fontSize: "12px",
					fontFamily: "Poppins",
					borderRadius: "8px",
					textTransform: "none",
					minWidth: "fit-content",
					boxShadow: "none"
				};
				const handleClick = (buttonName: string) => {
					let timeDiff = Date.now() - initialTime;
					timeDiff = timeDiff / 1000;
					if (button.ButtonAction.Action.toLocaleLowerCase() === "close") {
						onClose();
					} else if (button.ButtonAction.Action.toLocaleLowerCase() === "restart") {
						getCurrentStep("restart");
						setCurrentStepIndex(0);
						sessionStorage.removeItem('activeTooltipSession');
						sessionStorage.removeItem('currentTooltipStep');
						if (steps[0]?.targetUrl && steps[0].targetUrl.trim() !== window.location.href.trim()) {
							window.location.href = steps[0].targetUrl;
						} else {
							if (onRestart) {
								onRestart();
							} else {
								setCurrentStep(0);
							}
							if (steps[0]?.xpath) {
								const firstStepElement = getElementByXPath(steps[0].xpath, steps[0]?.PossibleElementPath || "");
								if (firstStepElement) {
									firstStepElement.scrollIntoView({ behavior: 'smooth' });
								}
							}
						}
						trackUserEngagement("button-click", userData, data, browser, version, buttonName, steps[currentStepIndex]?.stepTitle, timeDiff, 0);
					} else if (
						button.ButtonAction.Action.toLocaleLowerCase() === "next" &&
						currentStepData?.elementclick?.NextStep === "button" &&
						button.Id === currentStepData?.elementclick?.Id &&
						button.ButtonName && // Check button name exists
						currentStepData?.elementclick?.ButtonName // Check button name in gotonext object
					) {
						// Only perform element click if NextStep is 'button', ID matches, and button name exists
						if (currentStepData?.xpath) {
							const element = getElementByXPath(
								currentStepData.xpath || "",
								currentStepData.PossibleElementPath || ""
							);
							if (element) {
								element.click();
								handleNextStep().catch(error => {
									console.error("Error in handleNextStep:", error);
								});
							}
						} else {
							handleNextStep().catch(error => {
								console.error("Error in handleNextStep:", error);
							});
						}
						trackUserEngagement("button-click", userData, data, browser, version, buttonName, steps[currentStepIndex]?.stepTitle, timeDiff, 0);
					} else if (
						button.ButtonAction.Action.toLocaleLowerCase() === "next" &&
						currentStepData?.elementclick?.NextStep === "element" &&
						(!currentStepData?.elementclick?.ButtonName && !currentStepData?.elementclick?.Id)
					) {
						// If NextStep is 'element' and no button name or id in gotonext object, just navigate to next step
						const nextStepTitle = steps[currentStepIndex + 1]?.stepTitle || "";
						getCurrentStep(nextStepTitle);
						handleNextStep().catch(error => {
							console.error("Error in handleNextStep:", error);
						});
						trackUserEngagement("button-click", userData, data, browser, version, buttonName, steps[currentStepIndex]?.stepTitle, timeDiff, 0);
					} else if (
						button.ButtonAction.Action.toLocaleLowerCase() === "next" &&
						(!currentStepData?.elementclick?.NextStep ||
							(currentStepData?.elementclick?.NextStep !== "button" && currentStepData?.elementclick?.NextStep !== "element"))
					) {
						// If NextStep is empty or not 'button' or 'element', just go to next step, do NOT perform element click
						const nextStepTitle = steps[currentStepIndex + 1]?.stepTitle || "";
						getCurrentStep(nextStepTitle);
						handleNextStep().catch(error => {
							console.error("Error in handleNextStep:", error);
						});
						trackUserEngagement("button-click", userData, data, browser, version, buttonName, steps[currentStepIndex]?.stepTitle, timeDiff, 0);
					} else if (button.ButtonAction.Action.toLocaleLowerCase() === "previous") {
						const prevStepTitle = steps[currentStepIndex - 1]?.stepTitle || "";
						getCurrentStep(prevStepTitle);
						handlePrevious();
						trackUserEngagement("button-click", userData, data, browser, version, buttonName, steps[currentStepIndex]?.stepTitle, timeDiff, 0);
					} else if ((button.ButtonAction.Action === "open-url" || button.ButtonAction.Action === "open" || button.ButtonAction.Action === "openurl") && button.ButtonAction.ActionValue === "new-tab") {
						trackUserEngagement("button-click", userData, data, browser, version, buttonName, steps[currentStepIndex]?.stepTitle, timeDiff, 0);
						window.open(button.ButtonAction.TargetUrl, "_blank");
					} else if ((button.ButtonAction.Action === "open-url" || button.ButtonAction.Action === "open" || button.ButtonAction.Action === "openurl") && button.ButtonAction.ActionValue === "same-tab") {
						trackUserEngagement("button-click", userData, data, browser, version, buttonName, steps[currentStepIndex]?.stepTitle, timeDiff, 0);
						window.location.href = button.ButtonAction.TargetUrl;
					} else {
						trackUserEngagement("button-click", userData, data, browser, version, buttonName, steps[currentStepIndex]?.stepTitle, timeDiff, 0);
						onClose();
					}
				};
				return (
					<Button
						key={index}
						variant="contained"
						sx={{
							...buttonStyle,
							'&:hover': {
								backgroundColor: button.ButtonProperties.ButtonBackgroundColor + ' !important',
								color: button.ButtonProperties.ButtonTextColor + ' !important',
								border: button.ButtonProperties.ButtonBorderColor + ' !important',
								boxShadow: 'none',
							},
							'&:active': {
								backgroundColor: button.ButtonProperties.ButtonBackgroundColor + ' !important',
								color: button.ButtonProperties.ButtonTextColor + ' !important',
								border: button.ButtonProperties.ButtonBorderColor + ' !important',
								boxShadow: 'none',
							},
						}}
						onClick={() => handleClick(button?.ButtonName)}
					>
						{button.ButtonName}
					</Button>
				)
			})
			: null;
	};

	// Check if there's a valid image
	const hasValidImage =
		currentStepData?.imageUrl?.startsWith("data:image/") ||
		currentStepData?.imageUrl?.startsWith("http");

	// Prevent rendering if not within publish window
	if (!isWithinPublishWindow) {
		return null;
	}

	// Check if there's meaningful text content
	const hasValidTextContent =
		(typeof currentStepData?.content === "string" && hasHtmlMeaningfulContent(currentStepData?.content)) ||
		React.isValidElement(currentStepData?.content) ||
		(Array.isArray(currentStepData?.content) && currentStepData.content.some(
			(item: any) => item?.Text && typeof item.Text === "string" && hasHtmlMeaningfulContent(item.Text)
		));

	// Check if there are buttons
	const hasButtons = currentStepData?.buttonData?.length > 0;

	// Check if there's only text content (no images or buttons)
	const hasOnlyTextContent = hasValidTextContent && !hasValidImage && !hasButtons;

	// Check if there's only a button (no text or images)
	const hasOnlyButton = hasButtons && !hasValidTextContent && !hasValidImage && currentStepData?.buttonData?.length === 1;

	// Check if there's any meaningful content to display
	const hasValidContent = hasValidTextContent || hasValidImage;

	// Function to determine padding based on content and buttons
	const getPadding = () => {
		// Check if we have exactly one button and it's a previous button
		const hasPreviousButton = currentStepData?.buttonData?.length === 1 &&
			currentStepData?.buttonData?.[0]?.ButtonAction?.Action?.toLocaleLowerCase() === "previous";

		// Special case for previous button
		if (hasPreviousButton) {
			return "0px";
		}

		// Original logic
		if (!hasValidContent) {
			return currentStepData?.buttonData?.length === 1 ? "0px" : "4px";
		} else {
			return "0px";
		}
	};

	const TooltipContent = (
		<>
			<style>{`
    .ps__rail-x {
      display: none !important;
    }
  `}</style>
			<div style={{ placeContent: "end", display: "flex" }}>
				{enabelCross && (
					<CustomIconButton
						sx={{
							position: "absolute",
							boxShadow: "rgba(0, 0, 0, 0.06) 0px 4px 8px",
							background: "#fff !important",
							border: "1px solid #ccc",
							zIndex: "999",
							borderRadius: "50px",
							padding: "1px !important",
							float: "right",
							top: "-10px",
							right: "-10px",
							margin: canvasStyle?.BorderSize && canvasStyle?.BorderSize !== "0px" ? `-${parseInt(canvasStyle?.BorderSize) - 3}px` : "0px",
						}}
						onClick={onClose}
					>
						<CloseIcon sx={{ zoom: 1, color: "#000", width: "12px", height: "12px" }} />
					</CustomIconButton>
				)}
			</div>
			<PerfectScrollbar
				key={`scrollbar-${needsScrolling}`}
				ref={scrollbarRef}
				style={{ maxHeight: "270px" }}
				options={{
					suppressScrollY: !needsScrolling,
					suppressScrollX: true,
					wheelPropagation: false,
					swipeEasing: true,
					minScrollbarLength: 20,
					scrollingThreshold: 1000,
					scrollYMarginOffset: 0
				}}
			>

				<div ref={contentRef}
					style={{
						// maxHeight: "270px",
						overflow: "hidden",
						borderRadius: "4px",
						padding: getPadding(),
						position: "relative",
						zIndex: "999",
					}}>

					{/* Only render the content Box if there's meaningful image or text content to display */}
					{hasValidContent && (
						<Box sx={{
							width: hasOnlyTextContent ? "auto !important" : "100%"
						}}>
							<Box
								display="flex"
								flexDirection="column"
								alignItems={hasOnlyTextContent ? "flex-start" : "center"}
							>
								{renderContent()}
							</Box>
						</Box>
					)}

					{currentStepData?.buttonData?.length > 0 && (
						<Box
							display="flex"
							sx={{
								placeContent: "center",
								// padding: "4px 8px",
								gap: "4px",
								backgroundColor: currentStepData?.buttonData?.[0]?.BackgroundColor,
								width: "100%",
								alignItems: "center",
								// If there's only a button and no content, adjust padding and width
								// ...(currentStepData?.buttonData?.length === 1 && !hasValidContent && {
								// 	padding: "0",
								// 	width: "fit-content"
								// })
							}}
						>
							{renderButtons()}
						</Box>
					)}
					{enableProgress && totalSteps > 1
						&& <Box sx={{ padding: "2px 0 0 0 !important" }}>{renderProgress()}</Box>}
				</div>
			</PerfectScrollbar>
		</>
	);

	//const overlay = currentStepData?.overlay || "";
	const overlayStyle = {
		position: "fixed" as const,
		top: 0,
		left: 0,
		width: "100vw",
		height: "100vh",
		backgroundColor: "transparent",
		pointerEvents: "none",
		zIndex: 9999,
	};

	const getOverlaySections = () => {
		if (!targetElement) return null;

		const rect = targetElement.getBoundingClientRect();
		const viewportHeight = window.innerHeight;
		const viewportWidth = window.innerWidth;

		const sections = {
			top: {
				position: "fixed" as const,
				top: 0,
				left: 0,
				width: "100%",
				height: `${rect.top}px`,
				backgroundColor: "rgba(0, 0, 0, 0.5)",
				pointerEvents: "auto",
			},
			bottom: {
				position: "fixed" as const,
				top: `${rect.bottom}px`,
				left: 0,
				width: "100%",
				height: `${viewportHeight - rect.bottom}px`,
				backgroundColor: "rgba(0, 0, 0, 0.5)",
				pointerEvents: "auto",
			},
			left: {
				position: "fixed" as const,
				top: `${rect.top}px`,
				left: 0,
				width: `${rect.left}px`,
				height: `${rect.height}px`,
				backgroundColor: "rgba(0, 0, 0, 0.5)",
				pointerEvents: "auto",
			},
			right: {
				position: "fixed" as const,
				top: `${rect.top}px`,
				left: `${rect.right}px`,
				width: `${viewportWidth - rect.right}px`,
				height: `${rect.height}px`,
				backgroundColor: "rgba(0, 0, 0, 0.5)",
				pointerEvents: "auto",
			},
		};

		return sections;
	};
	const highlightBoxStyle = targetElement
		? {
			position: "fixed" as const,
			top: `${targetElement.getBoundingClientRect().top}px`,
			left: `${targetElement.getBoundingClientRect().left}px`,
			width: `${targetElement.getBoundingClientRect().width}px`,
			height: `${targetElement.getBoundingClientRect().height}px`,
			// border: '2px solid #fff',
			borderRadius: "4px",
			// pointerEvents: 'auto',
			zIndex: 10000,
		}
		: {};
	// const getTransformStyle = (placement: "top" | "left" | "right" | "bottom") => {
	// 	switch (placement) {
	// 		case "top":
	// 			return "translate(-50%, -100%)";
	// 		case "left":
	// 			return "translate(-100%, -50%)";
	// 		case "right":
	// 			return "translate(0%, -50%)";
	// 		case "bottom":
	// 			return "translate(-50%, 0%)";
	// 		default:
	// 			return "translate(-50%, -100%)"; // Default to top
	// 	}
	// };

	return (
		<>
			{currentStepData?.overlay && targetElement && isElementVisible && (
				<Box sx={overlayStyle}>
					{Object.entries(getOverlaySections() || {}).map(([key, style]) => (
						<Box
							key={key}
							sx={style}
						/>
					))}

					<Box sx={highlightBoxStyle} />
				</Box>
			)}
			<CustomWidthTooltip
				open={targetElement !== null && isElementVisible}
				data={data}
				title={TooltipContent}
				placement={tooltipPlacement}
				arrow
				PopperProps={{
					anchorEl: targetElement,
					modifiers: [
						{
							name: "preventOverflow",
							options: {
								boundary: window,
								altAxis: true,
								padding: 10,
							},
						},
						{
							name: "computeStyles",
							options: {
								// Disable adaptive positioning to prevent micro-adjustments
								adaptive: false,
								// Round positions to prevent sub-pixel rendering
								roundOffsets: ({ x, y }: { x: number; y: number }) => ({
									x: Math.round(x),
									y: Math.round(y),
								}),
							},
						},
					],
				}}
				// sx={{
				// 	// position: "absolute",
				// 	top: `${tooltipPosition.top}px`,
				// 	left: `${tooltipPosition.left}px`,
				// 	transform: getTransformStyle(tooltipPlacement),
				// 	// ...tooltipStyle
				// }}
				canvasStyle={canvasStyle}
				buttonData={currentStepData?.buttonData}
				hasContent={hasValidContent}
				hasOnlyText={hasOnlyTextContent}
				hasOnlyButton={hasOnlyButton}
			>
				<Box
					sx={{
						position: "absolute",

					}}
				/>
			</CustomWidthTooltip>
		</>
	);
};

export default TooltipGuide;
