module.exports = {
	webpack: {
	  configure: (webpackConfig) => {
		webpackConfig.output.filename = "embeded.js";  // JS output
		
		webpackConfig.module.rules = webpackConfig.module.rules.filter(rule => {
			return !(rule.loader && rule.loader.includes('source-map-loader'));
		  });
		webpackConfig.module.rules.forEach(rule => {
			if (rule.loader && rule.use) {
			  delete rule.loader; 
			}
		  });
		// Add a new rule for CSS and SCSS with style-loader
		const cssRule = webpackConfig.module.rules.find(rule => rule.test && rule.test.toString().includes('css'));

		if (cssRule) {
		  cssRule.use = [
			'style-loader',
			'css-loader',
			'sass-loader',  
		  ];
		}
  
		return webpackConfig;
	  },
	},
  };
  