import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Typography,
  IconButton,
  TextField,
  Paper,
  Slide,
  Tooltip,
  CircularProgress,
  Button
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import SendIcon from '@mui/icons-material/Send';
import MicIcon from '@mui/icons-material/Mic';
import MicOffIcon from '@mui/icons-material/MicOff';
import VolumeUpIcon from '@mui/icons-material/VolumeUp';
import VolumeOffIcon from '@mui/icons-material/VolumeOff';
import WifiIcon from '@mui/icons-material/Wifi';
import WifiOffIcon from '@mui/icons-material/WifiOff';
import * as signalR from '@microsoft/signalr';
import {
  isSpeechRecognitionSupported,
  startSpeechRecognition,
  stopSpeechRecognition
} from '../../services/SpeechRecognitionService';
import {
  speak,
  stop as stopSpeaking,
  setCurrentMessageId,
  isSpeechSynthesisSupported
} from '../../services/TextToSpeechService';
// Note: Using inline styles instead of imported styles for better maintainability
import { FetchWelcomeAudio, streamWelcomeAudio, fetchTTSStream } from '../Services/AssistantService';
import { speakModal } from '../Services/APIservice';
import AIResponseDisplay from './AIResponseDisplay';
// Icons imported but not used in this component
import robot from "../../assets/icons/robot.png";
import workAgentSignalRService, { WorkAgentCallbacks, UserInputRequest } from '../../services/SignalRService';
import { openStdin } from 'process';
import { GetAgentById } from '../Services/AgentService';
interface Message {
  text: string;
  isUser: boolean;
  timestamp: Date;
  id?: string;
  guide: any;
  isStatusMessage?: boolean;
  isInputRequest?: boolean; // Flag to indicate this message expects user input
  userInputRequest?: UserInputRequest; // Data for user input requests
}

let base64Audio: any = "";

interface CustomAgentChatModalProps {
  open: boolean;
  onClose: () => void;
  guide: any;
  isFromAi: boolean;
  setGuide: (guide: any) => void;
  setIsFromAi: (isFromAi: boolean) => void;
  setIsOpen: (open: any) => void;
  newThreadCreated?: boolean;
  setNewThreadCreated?: React.Dispatch<React.SetStateAction<boolean>>;
  isWelcomeMessageShown: boolean;
  setWelcomeMessageShown: (isWelcomeMessageShown: any) => void;
  messages: any;
  setMessages: (messages: any) => void;
  isReWelcomeMessageShown: boolean;
  setReWelcomeMessageShown: (isReWelcomeMessageShown: any) => void;
    onStartTraining?: () => void;
    setStartTraining:any;
    setBackgroundMode:any;
    setBindingData:any;
    setWorkerAgentVisible:any;
    agentId:string;
    setAgentId:any;
}

const CustomAgentChatModal: React.FC<CustomAgentChatModalProps> = ({
  open,
  onClose,
  guide,
  isFromAi,
  setGuide,
  setIsFromAi,
  setIsOpen,
  newThreadCreated,
  setNewThreadCreated,
  isWelcomeMessageShown,
  setWelcomeMessageShown,
  messages,
  setMessages,
  isReWelcomeMessageShown,
  setReWelcomeMessageShown,
  onStartTraining,
  setStartTraining, setBackgroundMode,setBindingData,setWorkerAgentVisible,
  agentId,
    setAgentId
}) => {

  // Removed speackingModal - TTS functionality removed

  // Initialize speakingModal variable and add debug logs
  var speakingModal = speakModal || "ElevenLabs";

  const [inputValue, setInputValue] = useState('');
  const [isListening, setIsListening] = useState(false);
  const [speechRecognitionSupported, setSpeechRecognitionSupported] = useState(false);
  const [speakingMessageId, setSpeakingMessageId] = useState<string | null>(null);
  const [autoSpeakResponse, setAutoSpeakResponse] = useState<boolean>(true);
  const [lastResponseId, setLastResponseId] = useState<string | null>('welcome-message');
  const [micPermissionError, setMicPermissionError] = useState<string | null>(null);
  const [micPermissionStatus, setMicPermissionStatus] = useState<string>('unknown');
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [connectionState, setConnectionState] = useState<signalR.HubConnectionState>(signalR.HubConnectionState.Disconnected);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const currentAudioRef = useRef<HTMLAudioElement | null>(null);
  const audioQueueRef = useRef<string[]>([]);
  const abortControllerRef = useRef<AbortController | null>(null);
  const ttsCacheRef = useRef<Record<string, string[]>>({});
  const [firstName, setFirstName] = useState("");

  const getUserData = () => {
    try {
      const userStatsString = localStorage.getItem('userStats');
      if (userStatsString) {
        return JSON.parse(userStatsString);
      }
      return null;
    } catch (error) {
      return null;
    }
  };

  const streamTTSChunks = async (
    text: string,
    signal: AbortSignal,
    onChunk: (chunk: { index: number; base64: string }) => void
  ) => {
    console.log('📡 streamTTSChunks starting for text length:', text.length);

    try {
      const stream = await fetchTTSStream(text, signal);
      console.log('📡 Got stream from fetchTTSStream:', !!stream);

      const reader = stream.getReader();
      const decoder = new TextDecoder("utf-8");
      let buffer = "";
      let chunkCount = 0;

      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          console.log('📡 Stream reading completed, total chunks processed:', chunkCount);
          break;
        }

        buffer += decoder.decode(value, { stream: true });

        let boundary = buffer.indexOf("\n\n");
        while (boundary !== -1) {
          const chunkStr = buffer.slice(0, boundary).trim();
          buffer = buffer.slice(boundary + 2);

          const dataLine = chunkStr.split("\n").find(line => line.startsWith("data: "));
          if (dataLine) {
            try {
              const jsonStr = dataLine.slice("data: ".length);
              const chunk = JSON.parse(jsonStr);
              console.log('📦 Parsed chunk:', { index: chunk.index, hasBase64: !!chunk.base64, base64Length: chunk.base64?.length });
              chunkCount++;
              onChunk(chunk);
            } catch (err) {
              console.error("❌ JSON parse error:", err, "Raw data:", dataLine);
            }
          }

          boundary = buffer.indexOf("\n\n");
        }
      }
    } catch (error) {
      console.error('❌ Error in streamTTSChunks:', error);
      throw error;
    }
  };

  const stopStreamedAudio = () => {
    // Stop current audio
    if (currentAudioRef.current) {
      currentAudioRef.current.pause();
      currentAudioRef.current = null;
    }

    // Clear audio queue
    audioQueueRef.current.forEach(url => URL.revokeObjectURL(url));
    audioQueueRef.current = [];

    // Abort streaming
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  };

  const playTTSChunks = async (messageId: string, text: string) => {
    console.log('🎵 playTTSChunks called for messageId:', messageId, 'text length:', text.length);

    if (speakingMessageId === messageId) {
      console.log('🛑 Already speaking this message, stopping...');
      stopStreamedAudio();
      setSpeakingMessageId(null);
      if (!isListening && !isProcessing && open) {
        startSpeech();
      }
      return;
    }

    // Stop any current playback
    stopStreamedAudio();
    setSpeakingMessageId(messageId);
    console.log('🎵 Starting TTS for messageId:', messageId);

    const queue: string[] = [];
    let isPlaying = false;
    let allChunksReceived = false;

    const playNext = () => {
      if (queue.length === 0) {
        if (allChunksReceived) {
          console.log('✅ All chunks played, ending TTS for messageId:', messageId);
          setSpeakingMessageId(null);
          if (!isListening && !isProcessing && open) {
            startSpeech();
          }
        }
        isPlaying = false;
        return;
      }

      const src = queue.shift()!;
      const audio = new Audio(src);
      currentAudioRef.current = audio;
      console.log('🔊 Playing audio chunk, queue length:', queue.length);

      audio.onended = () => {
        console.log('✅ Audio chunk ended');
        URL.revokeObjectURL(src);
        playNext();
      };

      audio.onerror = (e) => {
        console.error("❌ Audio playback error", e);
        URL.revokeObjectURL(src);
        playNext();
      };

      audio.play().catch((err) => {
        console.error("❌ Playback failed:", err);
        URL.revokeObjectURL(src);
        playNext();
      });
    };

    const controller = new AbortController();
    abortControllerRef.current = controller;

    try {
      console.log('📡 Starting to stream TTS chunks...');
      await streamTTSChunks(text, controller.signal, (chunk) => {
        console.log('📦 Received chunk:', chunk);
        if (!chunk.base64) {
          console.warn('⚠️ Chunk missing base64 data');
          return;
        }

        const byteArray = Uint8Array.from(atob(chunk.base64), (c) => c.charCodeAt(0));
        const blob = new Blob([byteArray], { type: "audio/mpeg" });
        const url = URL.createObjectURL(blob);

        queue.push(url);
        audioQueueRef.current.push(url);
        console.log('📦 Added chunk to queue, total chunks:', queue.length);

        if (!isPlaying) {
          isPlaying = true;
          console.log('▶️ Starting playback');
          playNext();
        }
      });

      console.log('📡 Finished streaming chunks, total received:', queue.length);
      // ✅ This runs after all chunks are received
      allChunksReceived = true;

      // If playback already finished while chunks were being fetched
      if (queue.length === 0 && !isPlaying) {
        console.log('⚠️ No chunks to play or playback already finished');
        setSpeakingMessageId(null);
        if (!isListening && !isProcessing && open) {
          startSpeech();
        }
      }
    } catch (error) {
      console.error('❌ Error in playTTSChunks:', error);
      setSpeakingMessageId(null);
    }
  };

  const fetchTTSAudio = async (messageId: string, text: string) => {
    console.log('🎵 fetchTTSAudio called for messageId:', messageId);
    try {
      const audioBase64 = await FetchWelcomeAudio(text);

      if (audioBase64) {
        console.log('🎵 Got audio data, playing...');
        const audio = new Audio(`data:audio/mpeg;base64,${audioBase64}`);
        audioRef.current = audio;

        audio.onplay = () => {
          console.log('🔊 OpenAI audio started playing');
        };

        audio.onpause = () => {
          stopAudio();
        };

        audio.onended = () => {
          console.log('✅ OpenAI audio ended');
          setSpeakingMessageId(null);
          if (!isListening && !isProcessing && open) {
            startSpeech();
          }
        };

        audio.onerror = (error) => {
          console.error('❌ OpenAI audio playback error:', error);
          setSpeakingMessageId(null);
        };

        audio.play();
      } else {
        console.warn('⚠️ No audio data received from OpenAI TTS');
        setSpeakingMessageId(null);
      }
    } catch (error) {
      console.error('❌ Error fetching OpenAI TTS audio:', error);
      setSpeakingMessageId(null);
    }
  };

  const fetchWelcomeAudio = async (text: string) => {
    if (speakingModal === "ElevenLabs") {
      const response = await streamWelcomeAudio(text); // Make API call to stream

      if (response) {
        const mime = 'audio/mpeg';
        const mediaSource = new MediaSource();
        const audio = new Audio();
        audioRef.current = audio;
        audio.src = URL.createObjectURL(mediaSource);

        audio.onplay = () => {
          // Optional: Handle onplay logic
        };

        audio.onpause = () => {
          stopAudio();
        };

        audio.onended = () => {
          setSpeakingMessageId(null);
        };

        mediaSource.addEventListener('sourceopen', () => {
          const sourceBuffer = mediaSource.addSourceBuffer(mime);
          const reader = response.getReader();

          const read = async () => {
            const { done, value } = await reader.read();
            if (done) {
              mediaSource.endOfStream();
              return;
            }
            sourceBuffer.appendBuffer(value);
            sourceBuffer.addEventListener('updateend', read, { once: true });
          };

          read();
        });

        audio.play();
      }
    } else {
      base64Audio = await FetchWelcomeAudio(text);

      if (base64Audio) {
        const audio = new Audio(`data:audio/mpeg;base64,${base64Audio}`);
        audioRef.current = audio;
        audio.onplay = () => {
          // setSpeakingMessageId('audio-response');
          // setLastResponseId('audio-response');
        };
        audio.onpause = () => {
          stopAudio();
        };
        // Reset speaking state when audio finishes
        audio.onended = () => {
          setSpeakingMessageId(null);

          //startSpeech();
        };
        audio.play();
      }
    }
  };

  var welcomeText = "";
  var reWelcomeMessageText = "";

  const createWelcomeMessage = () => {
    const userData = getUserData();

    if (userData?.Name) {
      setFirstName(userData.Name);
    }
    setWelcomeMessageShown(true);

    const dynamicGreeting = firstName
      ? `Hi ${firstName}! I'm Dona, your AI-powered guide for everything in QuickAdopt.`
      : `Hi there! I'm Dona, your AI-powered guide for everything in QuickAdopt.`;

    welcomeText = `${dynamicGreeting} I can help you:

• Build onboarding tours to walk new users through your features
• Design checklists to track key tasks step by step
• Launch surveys to collect feedback and improve your app
…and much more.

You'll also have support from Rookie, our interactive-guide expert. Whenever you'd like a hands-on, step-by-step walkthrough of any feature, just let us know: I'll explain the "what" and "why," and Rookie will generate a clickable tutorial so you can follow along in real time.`;

    setSpeakingMessageId("welcome-message");
    if (speakingModal === "webkit") {
      handleSpeakMessage("welcome-message", welcomeText);
    } else if (speakingModal === "OpenAi") {
      fetchWelcomeAudio(welcomeText);
    } else {
      playTTSChunks("welcome-message", welcomeText);
    }

    return {
      text: welcomeText,
      isUser: false,
      timestamp: new Date(),
      id: 'welcome-message',
      guide: null
    };
  }

  // Removed unused state variables for cleaner code

  // Initialize WorkAgent SignalR connection
  useEffect(() => {
    const initializeWorkAgentSignalR = async () => {
      try {
        await workAgentSignalRService.ensureConnection();
        setConnectionState(workAgentSignalRService.getConnectionState());
      } catch (error) {
        console.error('Failed to connect to WorkAgent SignalR:', error);
      }
    };

    if (open) {
      initializeWorkAgentSignalR();
    }

    // Don't disconnect when modal closes - keep singleton connection alive
    // Only disconnect if no other components are using it
    return () => {
      // Cleanup will be handled by the singleton service
    };
  }, [open]);

  // Cleanup when component unmounts completely
  useEffect(() => {
    return () => {
      console.log('CustomAgentChatModal: Component unmounting, cleaning up...');

      // Stop speech recognition if active
      try {
        stopSpeechRecognition();
      } catch (error) {
        console.log('Speech recognition already stopped or not active');
      }

      // Note: We don't force disconnect here as other components might be using the connection
      // The singleton service will manage the connection lifecycle
      console.log('CustomAgentChatModal: Cleanup completed');
    };
  }, []); // Empty dependency array means this runs only on unmount



  useEffect(() => {
    if (open && !isWelcomeMessageShown && welcomeText === "" && messages.length === 0) {
      const welcomeMessageText = createWelcomeMessage();
      setWelcomeMessageShown(true);
      setMessages((prev: any) => [...prev, welcomeMessageText]);
    } else if (open && !isReWelcomeMessageShown && messages.length !== 0 && reWelcomeMessageText === "") {
      const reWelcomeMessageText = createReWelcomeMessage();
      setReWelcomeMessageShown(true);
      setMessages((prev: any) => [...prev, reWelcomeMessageText]);
    }
  }, [open])

  const createReWelcomeMessage = () => {
    const dynamicGreeting = firstName
      ? `Welcome back, ${firstName}!`
      : `Welcome back, `;

    reWelcomeMessageText = `${dynamicGreeting}

Nice to have you here again. If you ever need help navigating the app or understanding a feature, just let me know—I'm here to guide you. `;

    setLastResponseId(`rewelcome-message_${messages.length - 1}`);
    setSpeakingMessageId(`rewelcome-message_${messages.length - 1}`);

    if (speakingModal === "webkit") {
      handleSpeakMessage(`rewelcome-message_${messages.length - 1}`, reWelcomeMessageText);
    } else if (speakingModal === "OpenAi") {
      fetchWelcomeAudio(reWelcomeMessageText);
    } else {
      playTTSChunks(`rewelcome-message_${messages.length - 1}`, reWelcomeMessageText);
    }

    return {
      text: reWelcomeMessageText,
      isUser: false,
      timestamp: new Date(),
      id: `rewelcome-message_${messages.length - 1}`,
      guide: null
    };
  };

  // Function to request microphone permission
  const requestMicrophonePermission = async (): Promise<boolean> => {
    try {
      // Request microphone permission explicitly
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

      // If we get here, permission was granted
      // Stop all tracks to release the microphone
      stream.getTracks().forEach(track => track.stop());

      return true;
    } catch (error) {
      console.error('Error requesting microphone permission:', error);
      return false;
    }
  };

  // Function to check microphone permission status
  const checkMicrophonePermission = async (): Promise<string> => {
    try {
      // Check if the permissions API is supported
      if (navigator.permissions && navigator.permissions.query) {
        const permissionStatus = await navigator.permissions.query({ name: 'microphone' as PermissionName });

        return permissionStatus.state; // 'granted', 'denied', or 'prompt'
      } else {
        // Fallback for browsers that don't support the permissions API
        return 'unknown';
      }
    } catch (error) {
      console.error('Error checking microphone permission:', error);
      return 'unknown';
    }
  };

  // Function to explicitly request microphone permission
  const handleRequestMicrophonePermission = async () => {
    const permissionGranted = await requestMicrophonePermission();

    if (permissionGranted) {
      setMicPermissionStatus('granted');
      setMicPermissionError(null);
    } else {
      setMicPermissionStatus('denied');
      setMicPermissionError('Microphone permission denied. Please allow microphone access in your browser settings.');
    }
  };

  // Check if speech recognition and speech synthesis are supported
  useEffect(() => {
    // Check WebKit speech recognition support
    const recognitionSupported = isSpeechRecognitionSupported();
    setSpeechRecognitionSupported(recognitionSupported);
    if (!recognitionSupported) {
      console.warn('WebKit speech recognition is not supported in this browser');
    }

    // Check if speech synthesis is supported
    const synthesisSupported = isSpeechSynthesisSupported();
    if (!synthesisSupported) {
      console.warn('Speech synthesis is not supported in this browser');
      setAutoSpeakResponse(false); // Disable auto-speak if not supported
    }

    // Check microphone permission status
    const checkPermission = async () => {
      const permissionStatus = await checkMicrophonePermission();
      setMicPermissionStatus(permissionStatus);

      // If permission is denied, set the error message
      if (permissionStatus === 'denied') {
        setMicPermissionError('Microphone permission denied. Please allow microphone access in your browser settings.');
      }
    };

    checkPermission();
  }, []);

  // Scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Generate a unique ID for messages
  const generateMessageId = (): string => {
    return `msg-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
  };

  // Handle speaking a message
  const handleSpeakMessage = (messageId: string, text: string) => {
    if (speakingMessageId === messageId) {
      if (speakModal === "webkit") {
        stopSpeaking();
        if (!isListening && !isProcessing) {
          setTimeout(() => startSpeech(), 300);
        }
      } else if (speakModal === "OpenAi") {
        stopAudio();
      } else {
        stopStreamedAudio();
        setSpeakingMessageId(null);
        if (!isListening && !isProcessing && open) {
          startSpeech();
        }
      }
      setSpeakingMessageId(null);
      return;
    }

    // If speaking another message, stop it first
    if (speakingMessageId) {
      if (speakModal === "webkit") {
        stopSpeaking();
      } else if (speakModal === "OpenAi") {
        stopAudio();
      } else {
        stopStreamedAudio();
      }
    }

    // Start speaking the new message
    setSpeakingMessageId(messageId);

    // Set the current message ID in the service
    setCurrentMessageId(messageId);
    if (speakModal === "webkit") {
      speak(text, {
        rate: 1.0, // Normal speed
        pitch: 1.0, // Normal pitch
        volume: 1.0, // Full volume
        onStart: () => {
          //console.log('CustomAgentChatModal: Speech started for message:', messageId);
        },
        onEnd: () => {
          //console.log('CustomAgentChatModal: Speech ended for message:', messageId);
          setSpeakingMessageId(null);
          // Only start speech recognition if not already listening and not processing
          if (!isListening && !isProcessing) {
            startSpeech();
          }
        },
        onError: (error) => {
          console.error('CustomAgentChatModal: Speech synthesis error:', error);
          setSpeakingMessageId(null);
          // Only start speech recognition if not already listening and not processing
          if (!isListening && !isProcessing) {
            // startSpeech();
          }
        }
      });
    } else if (speakModal === "OpenAi") {
      const audio = new Audio(`data:audio/mpeg;base64,${base64Audio}`);
      audioRef.current = audio;
      audio.onplay = () => {
        // setSpeakingMessageId('audio-response');
        // setLastResponseId('audio-response');
      };
      audio.onpause = () => {
        stopAudio();
      };
      // Reset speaking state when audio finishes
      audio.onended = () => {
        setSpeakingMessageId(null);
        //startSpeech();
      };
      audio.play();
    } else {
      // ElevenLabs streaming
      playTTSChunks(messageId, text);
    }
  };

  // Handle speaking a user input message - after speaking, send via submitFieldValue
  const handleSpeakUserInputMessage = (messageId: string, text: string, userInputRequest: UserInputRequest) => {
    console.log('🎵 handleSpeakUserInputMessage called for:', messageId, text);

    // Set the current message ID in the service
    setCurrentMessageId(messageId);
    speak(text, {
      rate: 1.0, // Normal speed
      pitch: 1.0, // Normal pitch
      volume: 1.0, // Full volume
      onStart: () => {
        console.log('🎵 User input message speech started:', messageId);
      },
      onEnd: async () => {
        console.log('🎵 User input message speech ended, sending via submitFieldValue:', messageId);
        setSpeakingMessageId(null);

        // Send the field value after speaking completes
        try {
          await workAgentSignalRService.submitFieldValue(
            userInputRequest.FieldName,
            text
          );
          console.log(`✅ Successfully submitted field value after TTS: ${userInputRequest.FieldName} = ${text}`);

          // Set processing to false - let the existing callbacks handle the response
          setIsProcessing(false);
        } catch (error) {
          console.error('❌ Error submitting field value after TTS:', error);
          setIsProcessing(false);
          // Fall back to regular prompt if field submission fails
          // Could add error handling here
        }
      },
      onError: (error) => {
        console.error('🎵 User input message speech synthesis error:', error);
        setSpeakingMessageId(null);
        setIsProcessing(false);
      }
    });
  };

  // Handle TTS audio for user input messages - after playing, send via submitFieldValue
  const fetchTTSAudioForUserInput = async (messageId: string, text: string, userInputRequest: UserInputRequest) => {
    console.log('🎵 fetchTTSAudioForUserInput called for:', messageId);
    try {
      const audioBase64 = await FetchWelcomeAudio(text);

      if (audioBase64) {
        console.log('🎵 Got user input audio data, playing...');
        const audio = new Audio(`data:audio/mpeg;base64,${audioBase64}`);
        audioRef.current = audio;

        audio.onplay = () => {
          console.log('🔊 User input audio started playing');
        };

        audio.onpause = () => {
          stopAudio(false); // Don't auto-start listening
        };

        audio.onended = async () => {
          console.log('✅ User input audio ended, sending via submitFieldValue');
          setSpeakingMessageId(null);

          // Send the field value after audio completes
          try {
            await workAgentSignalRService.submitFieldValue(
              userInputRequest.FieldName,
              text
            );
            console.log(`✅ Successfully submitted field value after audio: ${userInputRequest.FieldName} = ${text}`);

            // Set processing to false - let the existing callbacks handle the response
            setIsProcessing(false);
          } catch (error) {
            console.error('❌ Error submitting field value after audio:', error);
            setIsProcessing(false);
          }
        };

        audio.onerror = (error) => {
          console.error('❌ User input audio playback error:', error);
          setSpeakingMessageId(null);
          setIsProcessing(false);
        };

        audio.play();
      } else {
        console.warn('⚠️ No audio data received for user input TTS');
        setSpeakingMessageId(null);
        setIsProcessing(false);
      }
    } catch (error) {
      console.error('❌ Error fetching user input TTS audio:', error);
      setSpeakingMessageId(null);
      setIsProcessing(false);
    }
  };

  // Handle ElevenLabs streaming TTS for user input messages - after playing, send via submitFieldValue
  const playTTSChunksForUserInput = async (messageId: string, text: string, userInputRequest: UserInputRequest) => {
    console.log('🎵 playTTSChunksForUserInput called for messageId:', messageId, 'text length:', text.length);

    // Stop any current playback
    stopStreamedAudio();
    setSpeakingMessageId(messageId);
    console.log('🎵 Starting user input TTS for messageId:', messageId);

    const queue: string[] = [];
    let isPlaying = false;
    let allChunksReceived = false;

    const playNext = () => {
      if (queue.length === 0) {
        if (allChunksReceived) {
          console.log('✅ All user input chunks played, sending via submitFieldValue for messageId:', messageId);
          setSpeakingMessageId(null);

          // Send the field value after all chunks are played
          setTimeout(async () => {
            try {
              await workAgentSignalRService.submitFieldValue(
                userInputRequest.FieldName,
                text
              );
              console.log(`✅ Successfully submitted field value after streaming TTS: ${userInputRequest.FieldName} = ${text}`);

              // Set processing to false - let the existing callbacks handle the response
              setIsProcessing(false);
            } catch (error) {
              console.error('❌ Error submitting field value after streaming TTS:', error);
              setIsProcessing(false);
            }
          }, 100);
        }
        isPlaying = false;
        return;
      }

      const src = queue.shift()!;
      const audio = new Audio(src);
      currentAudioRef.current = audio;
      console.log('🔊 Playing user input audio chunk, queue length:', queue.length);

      audio.onended = () => {
        console.log('✅ User input audio chunk ended');
        URL.revokeObjectURL(src);
        playNext();
      };

      audio.onerror = (e) => {
        console.error("❌ User input audio playback error", e);
        URL.revokeObjectURL(src);
        playNext();
      };

      audio.play().catch((err) => {
        console.error("❌ User input playback failed:", err);
        URL.revokeObjectURL(src);
        playNext();
      });
    };

    const controller = new AbortController();
    abortControllerRef.current = controller;

    try {
      console.log('📡 Starting to stream user input TTS chunks...');
      await streamTTSChunks(text, controller.signal, (chunk) => {
        console.log('📦 Received user input chunk:', chunk);
        if (!chunk.base64) {
          console.warn('⚠️ User input chunk missing base64 data');
          return;
        }

        const byteArray = Uint8Array.from(atob(chunk.base64), (c) => c.charCodeAt(0));
        const blob = new Blob([byteArray], { type: "audio/mpeg" });
        const url = URL.createObjectURL(blob);

        queue.push(url);
        audioQueueRef.current.push(url);
        console.log('📦 Added user input chunk to queue, total chunks:', queue.length);

        if (!isPlaying) {
          isPlaying = true;
          console.log('▶️ Starting user input playback');
          playNext();
        }
      });

      console.log('📡 Finished streaming user input chunks, total received:', queue.length);
      allChunksReceived = true;

      // If playback already finished while chunks were being fetched
      if (queue.length === 0 && !isPlaying) {
        console.log('⚠️ No user input chunks to play or playback already finished');
        setSpeakingMessageId(null);

        // Still send the field value even if no audio played
        setTimeout(async () => {
          try {
            await workAgentSignalRService.submitFieldValue(
              userInputRequest.FieldName,
              text
            );
            console.log(`✅ Successfully submitted field value (no audio): ${userInputRequest.FieldName} = ${text}`);
            setIsProcessing(false);
          } catch (error) {
            console.error('❌ Error submitting field value (no audio):', error);
            setIsProcessing(false);
          }
        }, 100);
      }
    } catch (error) {
      console.error('❌ Error in playTTSChunksForUserInput:', error);
      setSpeakingMessageId(null);
      setIsProcessing(false);
    }
  };

  const stopAudio = (startListening: boolean = true) => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0; // Reset to beginning
      audioRef.current = null;
      setSpeakingMessageId(null);
      if (!isListening && !isProcessing && startListening && open) {
        startSpeech();
      }
    }
  };

  // Function to toggle auto-speak for responses
  const toggleAutoSpeak = () => {
    setAutoSpeakResponse(prev => !prev);
  };

  // Check if the last message is an input request
  const isWaitingForInput = () => {
    if (messages.length === 0) return false;
    const lastMessage = messages[messages.length - 1];
    const result = !lastMessage.isUser && (lastMessage.isInputRequest || lastMessage.userInputRequest);
    console.log('🔍 isWaitingForInput check:', {
      messagesLength: messages.length,
      lastMessage: lastMessage,
      isUser: lastMessage?.isUser,
      isInputRequest: lastMessage?.isInputRequest,
      userInputRequest: lastMessage?.userInputRequest,
      result: result
    });
    return result;
  };

  // Utility function to filter click steps from GuideStep
  const getClickObjects = (guide: any) => {
    if (!guide || !guide.GuideStep) return [];
    return guide.GuideStep.filter(
      (step: any) => step.StepType === 'click' || step.IsClickable === true
    );
  };

  // Handle sending message via WorkAgent SignalR
  const handleSendMessage = async (text?: string) => {
    const messageToSend = text?.trim() ?? inputValue;

    // Check if this is a response to a user input request BEFORE adding user message
    const lastBotMessage = messages.length > 0 ? messages[messages.length - 1] : null;
    const isRespondingToInputRequest = lastBotMessage &&
                                      !lastBotMessage.isUser &&
                                      (lastBotMessage.userInputRequest || lastBotMessage.isInputRequest);

    console.log('📤 handleSendMessage called with:', messageToSend);
    console.log('📤 Last bot message:', lastBotMessage);
    console.log('📤 Last bot message isUser:', lastBotMessage?.isUser);
    console.log('📤 Last bot message userInputRequest:', lastBotMessage?.userInputRequest);
    console.log('📤 Last bot message isInputRequest:', lastBotMessage?.isInputRequest);
    console.log('📤 Is responding to input request:', isRespondingToInputRequest);
    console.log('📤 isWaitingForInput():', isWaitingForInput());
    console.log('📤 Messages array length:', messages.length);
    console.log('📤 Last 3 messages:', messages.slice(-3));

    // Add user message
    const userMessageId = generateMessageId();
    const userMessage: any = {
      text: messageToSend,
      isUser: true,
      timestamp: new Date(),
      id: userMessageId,
      guide: null
    };

    setMessages((prev: any) => [...prev!, userMessage]);

    setInputValue('');

    // Set processing state to true
    setIsProcessing(true);

    // Handle user input responses
    if (isRespondingToInputRequest) {
      // Get the userInputRequest object - it might be directly on the message or we need to create a default one
      const userInputRequest = lastBotMessage.userInputRequest || {
        FieldName: "user_input",
        Message: lastBotMessage.text,
        IsRequired: true,
        FieldType: "text"
      };

      console.log('🔄 USER INPUT DETECTED: Processing user input response');
      console.log('🔄 USER INPUT: userInputRequest:', userInputRequest);

      if (autoSpeakResponse) {
        // Auto-speak user input messages and send via submitFieldValue after speaking
        console.log('🎵 Auto-speaking user input message:', messageToSend);
        setSpeakingMessageId(userMessageId);
        setLastResponseId(userMessageId);

        // Wait a short moment for the UI to update, then speak the user message
        setTimeout(() => {
          if (speakModal === "webkit") {
            console.log('🎵 Using webkit TTS for user input auto-speak');
            handleSpeakUserInputMessage(userMessageId, messageToSend, userInputRequest);
          } else if (speakModal === "OpenAi") {
            console.log('🎵 Using OpenAI TTS for user input auto-speak');
            fetchTTSAudioForUserInput(userMessageId, messageToSend, userInputRequest);
          } else {
            console.log('🎵 Using ElevenLabs streaming TTS for user input auto-speak');
            playTTSChunksForUserInput(userMessageId, messageToSend, userInputRequest);
          }
        }, 300);

        return; // Don't send the message yet - wait for TTS to complete
      } else {
        // Send field value response directly through EXISTING SignalR connection (no auto-speak)
        console.log('🔄 FIELD RESPONSE: Responding to user input request (no auto-speak):', userInputRequest.FieldName);
        console.log('🔄 FIELD RESPONSE: Field value:', messageToSend);
        console.log('🔄 FIELD RESPONSE: Using existing SignalR connection (submitFieldValue)');
        try {
          await workAgentSignalRService.submitFieldValue(
            userInputRequest.FieldName,
            messageToSend
          );
          console.log(`✅ FIELD RESPONSE: Successfully submitted field value: ${userInputRequest.FieldName} = ${messageToSend}`);
          console.log('✅ FIELD RESPONSE: Callbacks remain active, waiting for backend response...');
          console.log('✅ FIELD RESPONSE: Microphone will restart after TTS completion (identical to normal prompts)');

          // Set processing to false - let the existing callbacks handle TTS and microphone restart
          // This ensures identical behavior to normal prompts
          setIsProcessing(false);

          return; // Don't send as regular prompt - callbacks remain active for next field
        } catch (error) {
          console.error('❌ FIELD RESPONSE: Error submitting field value:', error);
          // Fall back to regular prompt if field submission fails
        }
      }
    } else {
      console.log('🆕 NEW MESSAGE: Sending as new prompt (not responding to input request)');
      console.log('🆕 NEW MESSAGE: Will create new SignalR request (processPrompt)');
      console.log('🆕 NEW MESSAGE: isRespondingToInputRequest:', isRespondingToInputRequest);
      console.log('🆕 NEW MESSAGE: lastBotMessage:', lastBotMessage);
    }

    // Create callbacks for this specific message
    const messageCallbacks: WorkAgentCallbacks = {
      onMessage: (response: any) => {
        console.log('WorkAgent message received:', response);
        console.log('🎵 Auto-speak enabled:', autoSpeakResponse, 'speakModal:', speakModal);
const trimmedMessage = response.message.trim();
  const skipDisplay = trimmedMessage === "Form submitted successfully!";
        const botMessageId = generateMessageId();
        let botMessage: Message;

        // Handle different response types
        if (response.ResponseType === "guide") {
          if (response.Message == null || response.Message === "") {
            response.Message = "Here's the Guide, Click on Button to view the guide";
          }

          botMessage = {
            text: response.Message,
            isUser: false,
            timestamp: new Date(),
            id: botMessageId,
            guide: response.Guide,
            isStatusMessage: false
          };
          setMessages((prev: any) => [...prev, botMessage]);
        }
        else if (speakModal === "webkit" ||
                (speakModal !== "webkit" && !response.Message.startsWith("[AUDIO]"))) {
          // Default to "message" type or any other type

          // Check if this is a user input request
          const isInputRequest = response.Message.toLowerCase().includes('please provide') ||
                                response.Message.toLowerCase().includes('enter') ||
                                response.Message.toLowerCase().includes('required') ||
                                response.Message.toLowerCase().includes('input') ||
                                response.Message.toLowerCase().includes('specify') ||
                                response.Message.toLowerCase().includes('choose');

          botMessage = {
            text: response.Message,
            isUser: false,
            timestamp: new Date(),
            id: botMessageId,
            guide: null,
            isStatusMessage:
              response.Message.startsWith("🔍") ||
              response.Message.startsWith("💬") ||
              response.Message.startsWith("🤖") ||
              response.Message.startsWith("📥") ||
              response.Message.startsWith("🧠") ||
              response.Message.startsWith("Processing"),
            isInputRequest: isInputRequest
          };

           if (!skipDisplay) {
    setMessages((prev: any) => [...prev, botMessage]);
  }

        }

        // Handle audio responses for non-webkit TTS
        if (speakModal !== "webkit" && response.Message.startsWith("[AUDIO]")) {
          base64Audio = response.Message.replace("[AUDIO]", "");
          const audio = new Audio(`data:audio/mpeg;base64,${base64Audio}`);
          audioRef.current = audio;

          // Set speaking state when audio starts playing
          audio.onplay = () => {
            console.log('Audio started playing');
          };

          audio.onpause = () => {
            stopAudio();
          };

          // Reset speaking state when audio finishes
          audio.onended = () => {
            setSpeakingMessageId(null);
            console.log('🎤 Audio response ended, restarting microphone if needed');
            if (!isListening && !isProcessing && open) {
              setTimeout(() => {
                startSpeech();
              }, 500);
            }
          };

          // Handle errors
          audio.onerror = () => {
            console.error("Error playing audio response");
            setSpeakingMessageId(null);
          };

          audio.play();
        }

        // Auto-speak functionality for completed responses
        if (!response.Message.startsWith("Processing")) {
          console.log('🎵 Message completed, checking auto-speak conditions...');
          console.log('🎵 autoSpeakResponse:', autoSpeakResponse);
          console.log('🎵 Message starts with [AUDIO]:', response.Message.startsWith("[AUDIO]"));
          console.log('🎵 Message text:', response.Message.substring(0, 100) + '...');

          const messageId = botMessageId;
          const messageText = response.Message;

          setLastResponseId(messageId);

          // Auto-speak if enabled and not an audio response (which is handled separately)
          if (autoSpeakResponse && !response.Message.startsWith("[AUDIO]")) {
            console.log('🎵 ✅ Auto-speaking response with speakModal:', speakModal);
            setSpeakingMessageId(messageId);
            // Wait a short moment for the UI to update
            setTimeout(() => {
              if (speakModal === "webkit") {
                console.log('🎵 Using webkit TTS for auto-speak');
                handleSpeakMessage(messageId, messageText);
              } else if (speakModal === "OpenAi") {
                console.log('🎵 Using OpenAI TTS for auto-speak');
                // For OpenAI, we need to fetch the audio first, then play it
                fetchTTSAudio(messageId, messageText);
              } else {
                console.log('🎵 Using ElevenLabs streaming TTS for auto-speak');
                playTTSChunks(messageId, messageText);
              }
            }, 300);
          } else {
            console.log('🎵 ❌ Auto-speak conditions not met');
            console.log('🎵 autoSpeakResponse:', autoSpeakResponse);
            console.log('🎵 isAudioResponse:', response.Message.startsWith("[AUDIO]"));
          }
        } else {
          console.log('🎵 Message is processing, skipping auto-speak');
        }

        // If this is not a processing message, mark as complete
        if (!response.Message.startsWith("Processing")) {
          setIsProcessing(false);
          // Only remove callbacks when we receive a FINAL completion message
          // Keep callbacks active during field collection, confirmation, and intermediate steps
          const isFinalCompletionMessage = response.Message.includes("Form submitted successfully") ||
                                          response.Message.includes("Request cancelled") ||
                                          response.Message.includes("Workflow completed") ||
                                          response.Message.includes("Process finished");
          const isAcknowledgment = response.Message.startsWith("Thank you for providing");
          const isIntermediateMessage = response.Message.includes("Data binding completed") ||
                                       response.Message.includes("You can now proceed") ||
                                       response.Message.includes("DOM data") ||
                                       response.Message.includes("continuing with");

          const isInputRequest = response.Message.toLowerCase().includes('please provide') ||
                                response.Message.toLowerCase().includes('enter') ||
                                response.Message.toLowerCase().includes('required') ||
                                response.Message.toLowerCase().includes('input') ||
                                response.Message.toLowerCase().includes('specify') ||
                                response.Message.toLowerCase().includes('choose');

          // Only remove callbacks for final completion messages, not intermediate ones
          if (!isInputRequest && !isAcknowledgment && !isIntermediateMessage && isFinalCompletionMessage) {
            console.log('Removing callbacks due to FINAL completion message:', response.Message);
            workAgentSignalRService.removeCallbacks(messageCallbacks);
          } else if (isIntermediateMessage) {
            console.log('🔄 Keeping callbacks active for intermediate message:', response.Message);
          }
        }
      },

      onError: (error: string) => {
        console.error('CustomAgentChatModal: Error from WorkAgent SignalR service:', error);

        const errorMsg: Message = {
          text: `Error: ${error}`,
          isUser: false,
          timestamp: new Date(),
          id: generateMessageId(),
          guide: null,
          isStatusMessage: true
        };

        setMessages((prev: any) => [...prev, errorMsg]);
        setIsProcessing(false);

        // Remove callbacks on error
        workAgentSignalRService.removeCallbacks(messageCallbacks);

        if (!isListening) {
          startSpeech();
        }
      },

      onUserInputRequest: (inputRequest: UserInputRequest) => {
        console.log('WorkAgent User Input Request:', inputRequest);

        const botMessageId = generateMessageId();

        // Create bot message for the user input request
        const botMessage: Message = {
          text: inputRequest.Message,
          isUser: false,
          timestamp: new Date(),
          id: botMessageId,
          guide: null,
          isStatusMessage: false,
          isInputRequest: true,
          userInputRequest: inputRequest
        };

        setMessages((prev: any) => [...prev, botMessage]);
        setIsProcessing(false); // Allow user to respond

        // Set the last response ID for user input requests
        setLastResponseId(botMessageId);

        // Auto-speak the user input request message
        if (autoSpeakResponse) {
          console.log('🎵 Auto-speaking user input request message:', inputRequest.Message);
          setSpeakingMessageId(botMessageId);

          // Wait a short moment for the UI to update, then speak the request
          setTimeout(() => {
            if (speakModal === "webkit") {
              console.log('🎵 Using webkit TTS for user input request auto-speak');
              handleSpeakMessage(botMessageId, inputRequest.Message);
            } else if (speakModal === "OpenAi") {
              console.log('🎵 Using OpenAI TTS for user input request auto-speak');
              fetchTTSAudio(botMessageId, inputRequest.Message);
            } else {
              console.log('🎵 Using ElevenLabs streaming TTS for user input request auto-speak');
              playTTSChunks(botMessageId, inputRequest.Message);
            }
          }, 300);
        } else {
          // If auto-speak is disabled, start microphone immediately
          if (!isListening && open) {
            console.log('🎤 Starting microphone for user input request (auto-speak disabled)');
            setTimeout(() => {
              startSpeech();
            }, 500); // Small delay to ensure UI updates
          }
        }

        // Keep callbacks active for user response
      },

      onRequestConfirmation: (confirmationRequest: { Message: string; Data: Record<string, string> }) => {
        console.log('WorkAgent Confirmation Request:', confirmationRequest);

        const botMessageId = generateMessageId();

        // Create bot message for the confirmation request
        const botMessage: Message = {
          text: confirmationRequest.Message,
          isUser: false,
          timestamp: new Date(),
          id: botMessageId,
          guide: null,
          isStatusMessage: false,
          isInputRequest: true,
          userInputRequest: {
            Message: confirmationRequest.Message,
            FieldName: "confirmation",
            IsRequired: true,
            FieldType: "confirmation"
          }
        };

        setMessages((prev: any) => [...prev, botMessage]);
        setIsProcessing(false); // Allow user to respond

        // Set the last response ID for confirmation requests
        setLastResponseId(botMessageId);

        // Auto-speak the confirmation request message
        if (autoSpeakResponse) {
          console.log('🎵 Auto-speaking confirmation request message:', confirmationRequest.Message);
          setSpeakingMessageId(botMessageId);

          // Wait a short moment for the UI to update, then speak the request
          setTimeout(() => {
            if (speakModal === "webkit") {
              console.log('🎵 Using webkit TTS for confirmation request auto-speak');
              handleSpeakMessage(botMessageId, confirmationRequest.Message);
            } else if (speakModal === "OpenAi") {
              console.log('🎵 Using OpenAI TTS for confirmation request auto-speak');
              fetchTTSAudio(botMessageId, confirmationRequest.Message);
            } else {
              console.log('🎵 Using ElevenLabs streaming TTS for confirmation request auto-speak');
              playTTSChunks(botMessageId, confirmationRequest.Message);
            }
          }, 300);
        } else {
          // If auto-speak is disabled, start microphone immediately
          if (!isListening && open) {
            console.log('🎤 Starting microphone for confirmation request (auto-speak disabled)');
            setTimeout(() => {
              startSpeech();
            }, 500); // Small delay to ensure UI updates
          }
        }

        // Keep callbacks active for confirmation response
      },

onNavigationRequest: async (navigationRequest: { Url: string; Id:string; Message: string }) => {
        const botMessageId = generateMessageId();

  setMessages((prev: any) => [
    ...prev,
    {
          text: `${navigationRequest.Message}\n\nRedirecting to: ${navigationRequest.Url}`,
          isUser: false,
          timestamp: new Date(),
          id: botMessageId,
          guide: null,
          isStatusMessage: true
    }
  ]);
setAgentId(navigationRequest.Id);

  const currentUrl = window.location.href.replace(/\/$/, '');
  const targetUrl = navigationRequest.Url.replace(/\/$/, '');

  if (currentUrl === targetUrl) {
    await workAgentSignalRService.confirmNavigation(true, 'Already on the target page');
    setMessages((prev: any) => [
      ...prev,
      {
        text: '\u2705 Already on the target page!',
        isUser: false,
        timestamp: new Date(),
        id: generateMessageId(),
        guide: null,
        isStatusMessage: true
      }
    ]);
    return;
  }

  // If not on the correct page, get agent and filter click steps
                try {
    const agent = await GetAgentById(navigationRequest.Id);
    const clickObject = getClickObjects(agent?.GuideDetails);

    // Store clickObject if needed (e.g., in state or context)
    // Here, we use setBindingData if available to pass clickObject to the work agent
    if (setBindingData) {
      setBindingData(clickObject);
    }
    // Optionally, you can also trigger the work agent UI or background mode
    if (setBackgroundMode) setBackgroundMode(true);
    if (setWorkerAgentVisible) setWorkerAgentVisible(false);
    if (setStartTraining) setStartTraining(true);

    // After work agent completes, re-check the URL and proceed if matched
    // We'll use a polling approach for simplicity
    const checkUrlAndProceed = async () => {
      const pollInterval = 1000; // 1 second
      const maxAttempts = 30; // 30 seconds max
      let attempts = 0;
      const intervalId = setInterval(async () => {
        const currentUrlNow = window.location.href.replace(/\/$/, '');
        if (currentUrlNow === targetUrl) {
          clearInterval(intervalId);
          // Confirm navigation and proceed with next steps
          await workAgentSignalRService.confirmNavigation(true, 'Navigation completed after click actions');
          setMessages((prev: any) => [
            ...prev,
            {
              text: '\u2705 Redirected and navigation confirmed after click actions!',
                    isUser: false,
                    timestamp: new Date(),
                    id: generateMessageId(),
                    guide: null,
                    isStatusMessage: true
                }
          ]);
          // Optionally, trigger next steps (PromptDataExtraction, DomDataBinding, etc.)
          // This depends on your agent flow, but you may need to call setStartTraining or similar again
        }
        attempts++;
        if (attempts >= maxAttempts) {
          clearInterval(intervalId);
          setMessages((prev: any) => [
            ...prev,
            {
              text: ' Navigation did not complete in time after click actions.',
                isUser: false,
                timestamp: new Date(),
                id: generateMessageId(),
                guide: null,
                isStatusMessage: true
            }
          ]);
        }
      }, pollInterval);
    };
    checkUrlAndProceed();
          } catch (error) {
    console.log(error, 'error');
    setMessages((prev: any) => [
      ...prev,
      {
        text: ` Error fetching agent or processing click actions: ${error}`,
        isUser: false,
        timestamp: new Date(),
        id: generateMessageId(),
        guide: null,
        isStatusMessage: true
          }
    ]);
  }
}
,



// onNavigationRequest: async (navigationRequest: { Url: string; Id:string; Message: string }) => {
//   const botMessageId = generateMessageId();

//   setMessages((prev: any) => [
//     ...prev,
//     {
//       text: `${navigationRequest.Message}\n\nRedirecting to: ${navigationRequest.Url}`,
//       isUser: false,
//       timestamp: new Date(),
//       id: botMessageId,
//       guide: null,
//       isStatusMessage: true
//     }
//   ]);

//   const currentUrl = window.location.href.replace(/\/$/, '');
//   const targetUrl = navigationRequest.Url.replace(/\/$/, '');

//   if (currentUrl === targetUrl) {
//     await workAgentSignalRService.confirmNavigation(true, 'Already on the target page');


//     setMessages((prev: any) => [
//       ...prev,
//       {
//         text: '\u2705 Already on the target page!',
//         isUser: false,
//         timestamp: new Date(),
//         id: generateMessageId(),
//         guide: null,
//         isStatusMessage: true
//       }
//     ]);
//     return;
//   }

//   sessionStorage.setItem('pendingNavigationConfirmation', 'true');
//   sessionStorage.setItem('targetUrl', navigationRequest.Url);

//   window.location.href = navigationRequest.Url;
// }
// ,  
      onConnectionEstablished: (message: string) => {
        console.log('WorkAgent connection established:', message);
      },

      onConnectionStateChanged: (state) => {
        setConnectionState(state);
      }
    };

    try {
      console.log('🆕 NEW MESSAGE: Creating new SignalR request with processPrompt()');
      await workAgentSignalRService.processPrompt(messageToSend, messageCallbacks);
     // await workAgentSignalRService.processPrompt(userMessage, messageCallbacks);
    } catch (error) {
      console.error('Error sending prompt to WorkAgent SignalR:', error);

      // Add error message to chat
      const errorMsg: Message = {
        text: `Error: Failed to connect to the WorkAgent. ${error instanceof Error ? error.message : 'Please try again.'}`,
        isUser: false,
        timestamp: new Date(),
        id: generateMessageId(),
        guide: null,
        isStatusMessage: true
      };

      setMessages((prev: any) => [...prev, errorMsg]);
      setIsProcessing(false);

      // Remove callbacks on error
      workAgentSignalRService.removeCallbacks(messageCallbacks);
    }
  };

  // Removed stopAudio function - TTS functionality removed

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey && !isProcessing && !isListening && inputValue.trim() !== '') {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Toggle WebKit speech recognition
  const startSpeech = async () => {
    // Clear any previous error
    setMicPermissionError(null);

    // Don't start speech recognition if already processing a message
    if (isProcessing) {
      console.log('CustomAgentChatModal: Cannot start speech recognition while processing a message');
      return;
    }

    if (isListening) {
      stopSpeechRecognition();
      setIsListening(false);
      // Input value is already set with the latest transcript
    } else {
      // First, explicitly request microphone permission
      const permissionGranted = await requestMicrophonePermission();

      if (!permissionGranted) {
        setMicPermissionStatus('denied');
        setMicPermissionError('Microphone permission denied. Please allow microphone access in your browser settings.');
        return;
      }

      // Update permission status
      setMicPermissionStatus('granted');
      setMicPermissionError(null);

      setIsListening(true);
      // Clear the input field when starting speech recognition
      setInputValue('');

      try {
        startSpeechRecognition({
          onStart: () => {
            setIsListening(true);
            setMicPermissionError(null);
          },
          onResult: (text, _isFinal) => {
            console.log('🎤 Speech recognition result:', text);
            setInputValue(text);
          },
          onEnd: (text: string) => {
            console.log('🎤 Speech recognition ended with text:', text);
            setIsListening(false);

            // Set the final transcribed text in the input field
            if (text.trim() !== '') {
              setInputValue(text.trim());

              // Check if we're waiting for user input
              const isWaitingForUserInput = isWaitingForInput();
              console.log('🎤 isWaitingForUserInput:', isWaitingForUserInput);

              // Auto-send the message after speech recognition completes
              if (!isProcessing) {
                if (isWaitingForUserInput) {
                  console.log('🎤 Auto-sending USER INPUT response after speech recognition');
                } else {
                  console.log('🎤 Auto-sending NORMAL MESSAGE after speech recognition');
                }

                // Auto-send when speech recognition completes
                setTimeout(() => {
                  handleSendMessage(text.trim());
                }, 500); // Small delay to ensure UI updates
              } else {
                console.log('🎤 Cannot send message while processing another message');
              }
            } else {
              console.log('🎤 No text transcribed');
            }
          },
          onError: (error) => {
            console.error('CustomAgentChatModal: WebKit speech recognition error:', error);
            setIsListening(false);
          }
        });
      } catch (error) {
        console.error('CustomAgentChatModal: Failed to start WebKit speech recognition:', error);

        // Set user-friendly error message
        let errorMessage = 'Failed to start speech recognition';
        if (error instanceof Error) {
          errorMessage = error.message;
        }

        // Check for permission errors
        if (errorMessage.toLowerCase().includes('permission') ||
          errorMessage.toLowerCase().includes('denied') ||
          errorMessage.toLowerCase().includes('access')) {
          errorMessage = 'Microphone permission denied. Please allow microphone access in your browser settings.';
          setMicPermissionStatus('denied');
        }

        setMicPermissionError(errorMessage);

        // Add error message to chat
        const errorMsg: Message = {
          text: `Microphone error: ${errorMessage}`,
          isUser: false,
          timestamp: new Date(),
          id: generateMessageId(),
          guide: null
        };
        setMessages((prev: any) => [...prev, errorMsg]);
      }
    }
  };

  // Removed toggleAutoSpeak function - TTS functionality removed

  // Get connection status display
  const getConnectionStatusIcon = () => {
    switch (connectionState) {
      case signalR.HubConnectionState.Connected:
        return <WifiIcon sx={{ color: '#4caf50', fontSize: '18px' }} />;
      case signalR.HubConnectionState.Connecting:
      case signalR.HubConnectionState.Reconnecting:
        return <CircularProgress size={16} sx={{ color: '#ff9800' }} />;
      case signalR.HubConnectionState.Disconnected:
      default:
        return <WifiOffIcon sx={{ color: '#f44336', fontSize: '18px' }} />;
    }
  };

  const getConnectionStatusText = () => {
    switch (connectionState) {
      case signalR.HubConnectionState.Connected:
        return 'Connected';
      case signalR.HubConnectionState.Connecting:
        return 'Connecting...';
      case signalR.HubConnectionState.Reconnecting:
        return 'Reconnecting...';
      case signalR.HubConnectionState.Disconnected:
      default:
        return 'Disconnected';
    }
  };

  // useEffect(() => {
  //   const pending = sessionStorage.getItem('pendingNavigationConfirmation');
  //   const targetUrl = sessionStorage.getItem('targetUrl');
  
  //   if (pending === 'true' && targetUrl) {
  //     const currentUrl = window.location.href.replace(/\/$/, '');
  //     const expectedUrl = targetUrl.replace(/\/$/, '');
  
  //     if (currentUrl === expectedUrl) {
  //       // Confirm navigation back to backend
  //       workAgentSignalRService.confirmNavigation(true, 'Navigation completed successfully').then(() => {
  //         console.log('Navigation confirmation sent to backend.');
  
  //         // Optionally show a message to user
  //         setMessages((prev:any) => [
  //           ...prev,
  //           {
  //             text: '\u2705 Redirected and navigation confirmed!',
  //             isUser: false,
  //             timestamp: new Date(),
  //             id: generateMessageId(),
  //             guide: null,
  //             isStatusMessage: true
  //           }
  //         ]);
  
  //         // Clear navigation intent
  //         sessionStorage.removeItem('pendingNavigationConfirmation');
  //         sessionStorage.removeItem('targetUrl');
  //       });
  //     }
  //   }
  // }, []);
  

  return (<>{
    open &&
    (
        <Paper sx={{
          position: "relative",
          width: "380px",
          display: "flex",
          flexDirection: "column",
          borderRadius: "0px",
          boxShadow: "0px 0px 15px rgba(0,0,0,0.2)",
          overflow: "hidden",
          zIndex: "1",
          height: "100vh",
          background: '#F8F9FA',
        }} elevation={3}>
          {/* Chat Header */}
          <Box sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            padding: "8px 12px",
            color: "white",
            position: "relative",
            overflow: "hidden",
            boxShadow: "0px 5px 10px rgba(0, 0, 0, 0.2)",
            "&::before": {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: '#4361EE',
              zIndex: -1
            }
          }}>
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
            }}>
              <img
                src={robot}
                alt="Chat"
                style={{
                  width: "36px",
                  height: "36px"
                }}
              />
              <Typography
                sx={{
                  background: '#fff',
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  fontSize: "22px",
                  fontWeight: "600",
                  fontFamily: "Gotham Pro",
                }}>Dona (SignalR)</Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              {/* Auto-speak toggle button */}
              <Tooltip
                title={autoSpeakResponse ? "Turn off auto-speak" : "Turn on auto-speak"}
                componentsProps={{
                  tooltip: {
                    sx: {
                      fontSize: "12px",
                      fontFamily: "Gotham Pro",
                      color: "white",
                    }
                  }
                }}
              >
                <IconButton
                  onClick={toggleAutoSpeak}
                  size="small"
                  sx={{
                    color: 'white', marginRight: 1,
                    svg: {
                      fontSize: "18px"
                    }
                  }}
                >
                  {autoSpeakResponse ? <VolumeUpIcon /> : <VolumeOffIcon />}
                </IconButton>
              </Tooltip>

              {/* Connection status indicator */}
              <Tooltip
                title={`WorkAgent ${getConnectionStatusText()}`}
                componentsProps={{
                  tooltip: {
                    sx: {
                      fontSize: "12px",
                      fontFamily: "Gotham Pro",
                      color: "white",
                    }
                  }
                }}
              >
                <Box sx={{ marginRight: 1, display: 'flex', alignItems: 'center' }}>
                  {getConnectionStatusIcon()}
                </Box>
              </Tooltip>

              {/* Disconnect button - Always visible */}
              <Tooltip
                title={
                  connectionState === signalR.HubConnectionState.Connected
                    ? "Disconnect from WorkAgent"
                    : connectionState === signalR.HubConnectionState.Connecting
                      ? "Connecting to WorkAgent..."
                      : "Connect to WorkAgent"
                }
                componentsProps={{
                  tooltip: {
                    sx: {
                      fontSize: "12px",
                      fontFamily: "Gotham Pro",
                      color: "white",
                    }
                  }
                }}
              >
                <IconButton
                  size="small"
                  onClick={async () => {
                    try {
                      if (connectionState === signalR.HubConnectionState.Connected) {
                        // Disconnect if connected
                        await workAgentSignalRService.disconnect();
                        setConnectionState(signalR.HubConnectionState.Disconnected);
                      } else if (connectionState === signalR.HubConnectionState.Disconnected) {
                        // Connect if disconnected
                        await workAgentSignalRService.ensureConnection();
                        setConnectionState(workAgentSignalRService.getConnectionState());
                      }
                    } catch (error) {
                      console.error('Error toggling connection:', error);
                    }
                  }}
                  disabled={connectionState === signalR.HubConnectionState.Connecting || connectionState === signalR.HubConnectionState.Reconnecting}
                  sx={{
                    color: 'white',
                    marginRight: 1,
                    svg: {
                      fontSize: "16px"
                    },
                    '&.Mui-disabled': {
                      color: 'rgba(255, 255, 255, 0.5)',
                    }
                  }}
                >
                  {connectionState === signalR.HubConnectionState.Connected ? (
                    <WifiOffIcon />
                  ) : (
                    <WifiIcon />
                  )}
                </IconButton>
              </Tooltip>

              {/* Removed auto-speak toggle button - TTS functionality removed */}
              <IconButton
                size="small"
                onClick={() => {
                  onClose();
                  stopSpeechRecognition();
                  setIsListening(false);
                  setReWelcomeMessageShown(false);

                  // Stop TTS if active
                  if (speakModal !== "webkit") {
                    stopAudio(false);
                  }

                  if (speakingMessageId) {
                    stopSpeaking();
                    setSpeakingMessageId(null);
                  }

                  // Don't disconnect WorkAgent SignalR - keep singleton connection alive
                  // The singleton service will manage the connection lifecycle
                }}
                sx={{
                  color: 'white',
                  svg: {
                    fontSize: "18px"
                  }
                }}
              >
                <CloseIcon />
              </IconButton>
            </Box>
          </Box>

          {/* Chat Body - Messages */}
          <Box sx={{
            flexGrow: 1,
            overflowY: "auto",
            padding: "0px",
            display: "flex",
            flexDirection: "column",
            gap: "8px",
            height: "calc(90vh - 110px)",
            placeContent: "flex-end",
          }}>
            <Box sx={{
              display: "flex",
              flexDirection: "column",
              gap: "8px",
              width: "100%",
              height: "auto",
              overflow: "auto",
              padding: "16px",
            }}>
              {messages.map((message: any) => (
                <Box
                  key={message.id || `msg-${message.timestamp.getTime()}`}
                  sx={{
                    alignSelf: message.isUser ? "flex-end" : "flex-start",
                    width: "100%",
                    maxWidth: "100%",
                    wordBreak: "break-word",
                  }}
                >
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'flex-start',
                    width: '100%'
                  }}>
                    {
                      message.isUser
                        ?
                        <Box sx={{
                          width: '100%',
                          textAlign: "right"
                        }}>
                          <Typography sx={{
                            fontSize: "14px",
                            color: " #000",
                            fontFamily: "Gotham Pro",
                            borderWidth: "0px",
                            padding: "12px 14px",
                            background: "#DEE2E6",
                            width: "fit-content",
                            borderRadius: "20px",
                            float: "right",
                            maxWidth: "95%"
                          }}>
                            {message.text}
                          </Typography>
                        </Box>
                        :
                        <Box sx={{ flex: 1 }}>
                          {
                            message.guide == null && (
                              <Box sx={{
                                position: 'relative',
                                // Add special styling for input request messages
                                ...(message.isInputRequest && {
                                  border: '2px solid #ff9800',
                                  borderRadius: '8px',
                                  padding: '8px',
                                  backgroundColor: '#fff3e0',
                                  '&::before': {
                                    content: '"⚡"',
                                    position: 'absolute',
                                    top: '-8px',
                                    right: '8px',
                                    backgroundColor: '#ff9800',
                                    color: 'white',
                                    borderRadius: '50%',
                                    width: '20px',
                                    height: '20px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    fontSize: '12px',
                                  }
                                })
                              }}>
                                <AIResponseDisplay text={message.text} />
                                {/* Show input prompt indicator for input request messages */}
                                {message.isInputRequest && (
                                  <Box sx={{ marginTop: '8px' }}>
                                    {/* Field information */}
                                    {message.userInputRequest && (
                                      <Box sx={{
                                        padding: '8px',
                                        backgroundColor: '#f5f5f5',
                                        borderRadius: '4px',
                                        marginBottom: '8px',
                                        border: '1px solid #ddd'
                                      }}>
                                        <Typography sx={{ fontSize: '12px', fontWeight: 'bold', color: '#666' }}>
                                          Field: {message.userInputRequest.FieldName}
                                        </Typography>
                                        <Typography sx={{ fontSize: '12px', color: '#666' }}>
                                          Type: {message.userInputRequest.FieldType}
                                        </Typography>
                                        {message.userInputRequest.IsRequired && (
                                          <Typography sx={{ fontSize: '12px', color: '#d32f2f', fontWeight: 'bold' }}>
                                            * Required
                                          </Typography>
                                        )}
                                      </Box>
                                    )}
                                    {/* Waiting indicator */}
                                    <Box sx={{
                                      padding: '4px 8px',
                                      backgroundColor: '#ff9800',
                                      color: 'white',
                                      borderRadius: '4px',
                                      fontSize: '12px',
                                      fontWeight: 'bold',
                                      display: 'inline-block'
                                    }}>
                                      💬 Waiting for your input...
                                    </Box>
                                  </Box>
                                )}
                              </Box>
                            )
                          }
                          {
                            message.guide != null &&
                            <>
                              <Button variant="outlined" size="small" color="primary" onClick={() => {
                                setIsOpen(false); setIsFromAi(true); setGuide(message.guide);
                                stopSpeechRecognition();
                                setReWelcomeMessageShown(false);
                                setIsListening(false);

                                // Don't disconnect WorkAgent SignalR when opening guide
                                // Keep the singleton connection alive for other components
                              }}
                                sx={{
                                  color: "#4361ee",
                                  borderColor: "#4361ee",
                                  marginTop: "8px",
                                  fontSize: "10px",
                                  fontWeight: "600",
                                  "&:hover": {
                                    color: "#fff",
                                    borderColor: "#4361ee",
                                    background: "#4361ee",
                                  },
                                }}
                              >{message.text}</Button>
                            </>
                          }
                        </Box>
                    }

                    {/* Text-to-speech button only for the last bot message */}
                    {!message.isUser && message.id && (
                      <Tooltip title={speakingMessageId === message.id ? "Stop speaking" : "Listen again"}
                        componentsProps={{
                          tooltip: {
                            sx: {
                              fontSize: "12px",
                              fontFamily: "Gotham Pro",
                            }
                          }
                        }}
                      >
                        <IconButton
                          disabled={isListening || message.id !== lastResponseId}
                          onClick={() => { if (isListening) return; handleSpeakMessage(message.id!, message.text) }}
                          size="small"
                          sx={{
                            marginRight: '10px',
                            color: '#000',
                            '&:hover': {
                              color: '#000',
                              background: "transparent",
                            },
                            // color: speakingMessageId === message.id ? "#5f9ea0" : "inherit"
                          }}
                        >
                          {speakingMessageId === message.id ? <VolumeUpIcon /> : <VolumeOffIcon />}
                        </IconButton>
                      </Tooltip>
                    )}
                  </Box>
                </Box>
              ))}
              <div ref={messagesEndRef} />
            </Box>
          </Box>

          {/* Chat Footer - Input */}
          <Box sx={{
            background: '#e7e7e7',
            display: "flex",
            alignItems: "center",
            gap: "8px",
            padding: "0px 14px",
          }}>
            <Box sx={{
              display: "flex",
              alignItems: "center",
              gap: "8px",
              margin: "10px 0px",
              paddingRight: "10px",
              width: "100%",
              borderRadius: "10px",
              background: "transparent",
              border: "1px solid #a3a3a3",
            }}>
              {/* TextField */}
              <TextField
                fullWidth
                multiline
                placeholder={
                  isListening
                    ? "🎤 Listening... (speak now)"
                    : isProcessing
                      ? "Processing your request..."
                      : connectionState !== signalR.HubConnectionState.Connected
                        ? "Connecting to WorkAgent..."
                        : isWaitingForInput()
                          ? "💬 Speak or type your response (auto-send enabled)"
                          : "Type your message or use voice input..."
                }
                variant="outlined"
                size="small"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyDown={handleKeyDown}
                disabled={isListening || isProcessing || connectionState !== signalR.HubConnectionState.Connected}
                inputRef={inputRef}
                InputProps={{
                  sx: {
                    padding: "6px 0px 6px 12px",
                    color: '#fff !important',
                    border: "0px !important",
                    '& textarea': {
                      minHeight: '25px',
                      maxHeight: '100px',
                      overflowY: 'auto !important',
                      height: "50px",
                      placeContent: "center",
                      fontSize: "13px",
                      color: "#000",
                    },
                    '& fieldset': {
                      border: '0px',
                    },
                  },
                }}
              />
              {/* Mic Button */}
              {speechRecognitionSupported && (
                <Tooltip
                  title={
                    isListening
                      ? "Stop listening"
                      : micPermissionStatus === 'denied'
                        ? "Microphone permission denied. Click to request access."
                        : connectionState !== signalR.HubConnectionState.Connected
                          ? "WorkAgent not connected"
                          : isWaitingForInput()
                            ? "Start voice input (will auto-send when waiting for input)"
                            : "Start voice input (will show in text field)"
                  }
                  componentsProps={{
                    tooltip: {
                      sx: {
                        fontSize: "12px",
                        fontFamily: "Gotham Pro",
                      }
                    }
                  }}
                >
                  <IconButton
                    disabled={!!speakingMessageId || connectionState !== signalR.HubConnectionState.Connected}
                    onClick={
                      micPermissionStatus === 'denied'
                        ? handleRequestMicrophonePermission
                        : startSpeech
                    }
                    size="small"
                    sx={{
                      backgroundColor: 'transparent',
                      color: '#000',
                      '&:hover': {
                        backgroundColor: 'transparent',
                      },
                    }}
                  >
                    {isListening ? (
                      <>
                        <MicIcon sx={{ fontSize: "20px" }} />
                        <Box
                          sx={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            width: '100%',
                            height: '100%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                        >
                          <CircularProgress
                            size={24}
                            thickness={3}
                            sx={{ color: '#000' }}
                          />
                        </Box>
                      </>
                    ) : micPermissionStatus === 'denied' ? (
                      <MicOffIcon sx={{ fontSize: "20px" }} />
                    ) : (
                      <MicIcon sx={{ fontSize: "20px" }} />
                    )}
                  </IconButton>
                </Tooltip>
              )}
            </Box>
            {/* Send Button */}
            <IconButton
              onClick={() => {
                handleSendMessage();
                // Stop TTS when sending a new message
                if (speakModal !== "webkit") {
                  stopAudio(false);
                }
                if (speakingMessageId) {
                  stopSpeaking();
                  setSpeakingMessageId(null);
                }
              }}
              disabled={inputValue.trim() === '' || isListening || isProcessing || connectionState !== signalR.HubConnectionState.Connected}
              size="small"
              sx={{
                padding: "9px 8px 11px 12px",
                color: '#fff',
                backgroundColor: '#4361ee',
                '&.Mui-disabled': {
                  backgroundColor: '#4361ee',
                  opacity: 0.6,
                  color: '#fff',
                },
                '&:hover': {
                  backgroundColor: '#4361ee',
                },
              }}
            >
              <SendIcon sx={{
                fontSize: "20px",
                transform: "rotate(-35deg)",
              }} />
            </IconButton>
          </Box>

        </Paper>
    )
  }
  </>);
};

export default CustomAgentChatModal;
